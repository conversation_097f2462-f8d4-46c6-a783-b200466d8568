namespace Domain.Identity;

public class UserProfile
{
    public int Id { get; set; }             // PK profilu
    public int UserId { get; set; }         // FK na Identity user
    public string FirstName { get; set; }   // Jméno
    public string LastName { get; set; }    // Příjmení
    public string DisplayName => $"{FirstName} {LastName}";
        
    // Rozšířitelné: Department, ManagerId, DeputyId...
}