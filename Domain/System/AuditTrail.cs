

using SharedKernel.Domain;

namespace Domain.System;

public class AuditTrail : BaseEntity<int>
{
    public string? UserId { get; set; }
    public string? TableName { get; set; }

    public string   EntityName  { get; set; }
    public string?  EntityId    { get; set; }
    public string   Operation   { get; set; }
    public DateTimeOffset Timestamp   { get; set; }
    public string   UserName    { get; set; }
    public string   ChangesJson { get; set; }
    public string? ErrorMessage { get; set; }
}