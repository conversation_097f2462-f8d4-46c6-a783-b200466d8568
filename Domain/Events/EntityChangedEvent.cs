using SharedKernel.Domain;
using System;
using System.Collections.Generic;

namespace Domain.Events;

/// <summary>
/// <PERSON><PERSON><PERSON> do<PERSON> událost pro změny entit.
/// Tato událost je generována automaticky na základě definic událostí.
/// </summary>
public class EntityChangedEvent : DomainEvent
{
    /// <summary>
    /// Název události podle definice.
    /// </summary>
    public string EventName { get; }

    /// <summary>
    /// Entita, která byla změněna.
    /// </summary>
    public object Entity { get; }

    /// <summary>
    /// Typ entity.
    /// </summary>
    public Type EntityType { get; }

    /// <summary>
    /// Název typu entity.
    /// </summary>
    public string EntityTypeName => EntityType.Name;

    /// <summary>
    /// Operace, která byla provedena.
    /// </summary>
    public string Operation { get; }

    /// <summary>
    /// Seznam vlastností, kter<PERSON> byly změn<PERSON>ny.
    /// Prázdný pro operace Created a Deleted.
    /// </summary>
    public IReadOnlyList<string> ChangedProperties { get; }

    /// <summary>
    /// Slovník původních hodnot vlastností (pouze pro Update operace).
    /// </summary>
    public IReadOnlyDictionary<string, object?> OriginalValues { get; }

    /// <summary>
    /// Slovník nových hodnot vlastností (pouze pro Update operace).
    /// </summary>
    public IReadOnlyDictionary<string, object?> CurrentValues { get; }

    /// <summary>
    /// Identifikátor entity (pokud je dostupný).
    /// </summary>
    public object? EntityId { get; }

    /// <summary>
    /// Konstruktor pro vytvoření události změny entity.
    /// </summary>
    /// <param name="eventName">Název události</param>
    /// <param name="entity">Změněná entita</param>
    /// <param name="operation">Provedená operace</param>
    /// <param name="changedProperties">Seznam změněných vlastností</param>
    /// <param name="originalValues">Původní hodnoty</param>
    /// <param name="currentValues">Aktuální hodnoty</param>
    /// <param name="entityId">Identifikátor entity</param>
    public EntityChangedEvent(
        string eventName,
        object entity,
        string operation,
        IEnumerable<string>? changedProperties = null,
        IDictionary<string, object?>? originalValues = null,
        IDictionary<string, object?>? currentValues = null,
        object? entityId = null)
    {
        EventName = eventName ?? throw new ArgumentNullException(nameof(eventName));
        Entity = entity ?? throw new ArgumentNullException(nameof(entity));
        EntityType = entity.GetType();
        Operation = operation ?? throw new ArgumentNullException(nameof(operation));
        ChangedProperties = changedProperties?.ToList().AsReadOnly() ?? new List<string>().AsReadOnly();
        OriginalValues = originalValues?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value).AsReadOnly() ?? 
                        new Dictionary<string, object?>().AsReadOnly();
        CurrentValues = currentValues?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value).AsReadOnly() ?? 
                       new Dictionary<string, object?>().AsReadOnly();
        EntityId = entityId;
    }

    /// <summary>
    /// Vytvoří událost pro vytvoření entity.
    /// </summary>
    /// <param name="eventName">Název události</param>
    /// <param name="entity">Vytvořená entita</param>
    /// <param name="entityId">Identifikátor entity</param>
    /// <returns>Událost vytvoření entity</returns>
    public static EntityChangedEvent Created(string eventName, object entity, object? entityId = null)
    {
        return new EntityChangedEvent(eventName, entity, "Created", entityId: entityId);
    }

    /// <summary>
    /// Vytvoří událost pro aktualizaci entity.
    /// </summary>
    /// <param name="eventName">Název události</param>
    /// <param name="entity">Aktualizovaná entita</param>
    /// <param name="changedProperties">Seznam změněných vlastností</param>
    /// <param name="originalValues">Původní hodnoty</param>
    /// <param name="currentValues">Aktuální hodnoty</param>
    /// <param name="entityId">Identifikátor entity</param>
    /// <returns>Událost aktualizace entity</returns>
    public static EntityChangedEvent Updated(
        string eventName,
        object entity,
        IEnumerable<string> changedProperties,
        IDictionary<string, object?> originalValues,
        IDictionary<string, object?> currentValues,
        object? entityId = null)
    {
        return new EntityChangedEvent(eventName, entity, "Updated", changedProperties, originalValues, currentValues, entityId);
    }

    /// <summary>
    /// Vytvoří událost pro smazání entity.
    /// </summary>
    /// <param name="eventName">Název události</param>
    /// <param name="entity">Smazaná entita</param>
    /// <param name="entityId">Identifikátor entity</param>
    /// <returns>Událost smazání entity</returns>
    public static EntityChangedEvent Deleted(string eventName, object entity, object? entityId = null)
    {
        return new EntityChangedEvent(eventName, entity, "Deleted", entityId: entityId);
    }

    /// <summary>
    /// Ověří, zda byla změněna konkrétní vlastnost.
    /// </summary>
    /// <param name="propertyName">Název vlastnosti</param>
    /// <returns>True, pokud byla vlastnost změněna</returns>
    public bool IsPropertyChanged(string propertyName)
    {
        return ChangedProperties.Contains(propertyName);
    }

    /// <summary>
    /// Získá původní hodnotu vlastnosti.
    /// </summary>
    /// <param name="propertyName">Název vlastnosti</param>
    /// <returns>Původní hodnota nebo null</returns>
    public object? GetOriginalValue(string propertyName)
    {
        OriginalValues.TryGetValue(propertyName, out var value);
        return value;
    }

    /// <summary>
    /// Získá aktuální hodnotu vlastnosti.
    /// </summary>
    /// <param name="propertyName">Název vlastnosti</param>
    /// <returns>Aktuální hodnota nebo null</returns>
    public object? GetCurrentValue(string propertyName)
    {
        CurrentValues.TryGetValue(propertyName, out var value);
        return value;
    }
}
