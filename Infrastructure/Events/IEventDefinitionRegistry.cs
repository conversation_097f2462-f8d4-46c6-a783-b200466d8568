using System;
using System.Collections.Generic;
using SharedKernel.Domain;

namespace Infrastructure.Events;

/// <summary>
/// Rozhraní pro registry definic doménových událostí.
/// </summary>
public interface IEventDefinitionRegistry
{
    /// <summary>
    /// Registruje definici události.
    /// </summary>
    /// <param name="definition">Definice události</param>
    void Register(EventDefinition definition);

    /// <summary>
    /// Registruje více definic událostí najednou.
    /// </summary>
    /// <param name="definitions">Kolekce definic událostí</param>
    void RegisterRange(IEnumerable<EventDefinition> definitions);

    /// <summary>
    /// Získá všechny definice událostí pro daný typ entity a operaci.
    /// </summary>
    /// <param name="entityType">Typ entity</param>
    /// <param name="operation">Operace</param>
    /// <returns><PERSON>lek<PERSON> definic událostí</returns>
    IEnumerable<EventDefinition> GetDefinitions(Type entityType, EventType operation);

    /// <summary>
    /// Získá všechny definice událostí pro daný typ entity.
    /// </summary>
    /// <param name="entityType">Typ entity</param>
    /// <returns>Kolekce definic událostí</returns>
    IEnumerable<EventDefinition> GetDefinitions(Type entityType);

    /// <summary>
    /// Získá definici události podle názvu.
    /// </summary>
    /// <param name="eventName">Název události</param>
    /// <returns>Definice události nebo null</returns>
    EventDefinition? GetDefinition(string eventName);

    /// <summary>
    /// Získá všechny registrované definice událostí.
    /// </summary>
    /// <returns>Kolekce všech definic událostí</returns>
    IEnumerable<EventDefinition> GetAllDefinitions();

    /// <summary>
    /// Odstraní definici události podle názvu.
    /// </summary>
    /// <param name="eventName">Název události</param>
    /// <returns>True, pokud byla definice odstraněna</returns>
    bool Remove(string eventName);

    /// <summary>
    /// Vymaže všechny definice událostí.
    /// </summary>
    void Clear();

    /// <summary>
    /// Ověří, zda je registrována definice události s daným názvem.
    /// </summary>
    /// <param name="eventName">Název události</param>
    /// <returns>True, pokud je definice registrována</returns>
    bool Contains(string eventName);

    /// <summary>
    /// Získá počet registrovaných definic událostí.
    /// </summary>
    int Count { get; }
}
