using Domain.Entities;
using System;
using System.Collections.Generic;

namespace Infrastructure.Events;

/// <summary>
/// Definice doménových událostí pro SampleEntity.
/// Tato třída obsahuje ukázkové definice událostí pro demonstraci systému.
/// </summary>
public static class SampleEntityEventDefinitions
{
    /// <summary>
    /// Získá všechny definice událostí pro SampleEntity.
    /// </summary>
    /// <returns>Kolekce definic událostí</returns>
    public static IEnumerable<EventDefinition> GetDefinitions()
    {
        return new[]
        {
            // Událost při vytvoření nové SampleEntity
            new EventDefinition("SampleEntityCreated", typeof(SampleEntity), EntityOperation.Created)
                .WithDescription("Událost spuštěná při vytvoření nové ukázkové entity"),

            // Událost při aktualizaci názvu
            new EventDefinition("SampleEntityNameChanged", typeof(SampleEntity), EntityOperation.Updated)
                .WithTrackedProperties(nameof(SampleEntity.Name))
                .WithDescription("Událost spuštěná při změně názvu ukázkové entity"),

            // Událost při změně stavu aktivity
            new EventDefinition("SampleEntityStatusChanged", typeof(SampleEntity), EntityOperation.Updated)
                .WithTrackedProperties(nameof(SampleEntity.IsActive))
                .WithDescription("Událost spuštěná při změně stavu aktivity ukázkové entity"),

            // Událost při aktivaci entity
            new EventDefinition("SampleEntityActivated", typeof(SampleEntity), EntityOperation.Updated)
                .WithTrackedProperties(nameof(SampleEntity.IsActive))
                .WithCondition(entity => entity is SampleEntity sample && sample.IsActive)
                .WithDescription("Událost spuštěná při aktivaci ukázkové entity"),

            // Událost při deaktivaci entity
            new EventDefinition("SampleEntityDeactivated", typeof(SampleEntity), EntityOperation.Updated)
                .WithTrackedProperties(nameof(SampleEntity.IsActive))
                .WithCondition(entity => entity is SampleEntity sample && !sample.IsActive)
                .WithDescription("Událost spuštěná při deaktivaci ukázkové entity"),

            // Událost při změně věku (pouze pro dospělé)
            new EventDefinition("SampleEntityAgeChangedAdult", typeof(SampleEntity), EntityOperation.Updated)
                .WithTrackedProperties(nameof(SampleEntity.Age))
                .WithCondition(entity => entity is SampleEntity sample && sample.Age >= 18)
                .WithDescription("Událost spuštěná při změně věku u dospělých osob"),

            // Událost při změně popisu
            new EventDefinition("SampleEntityDescriptionChanged", typeof(SampleEntity), EntityOperation.Updated)
                .WithTrackedProperties(nameof(SampleEntity.Description))
                .WithDescription("Událost spuštěná při změně popisu ukázkové entity"),

            // Událost při změně data narození
            new EventDefinition("SampleEntityBirthDateChanged", typeof(SampleEntity), EntityOperation.Updated)
                .WithTrackedProperties(nameof(SampleEntity.DateOfBirth))
                .WithDescription("Událost spuštěná při změně data narození ukázkové entity"),

            // Událost při smazání entity
            new EventDefinition("SampleEntityDeleted", typeof(SampleEntity), EntityOperation.Deleted)
                .WithDescription("Událost spuštěná při smazání ukázkové entity"),

            // Komplexní událost při změně více vlastností najednou
            new EventDefinition("SampleEntityProfileUpdated", typeof(SampleEntity), EntityOperation.Updated)
                .WithTrackedProperties(nameof(SampleEntity.Name), nameof(SampleEntity.Description), nameof(SampleEntity.Age))
                .WithDescription("Událost spuštěná při aktualizaci profilu ukázkové entity"),

            // Událost pro jakoukoliv změnu entity (univerzální)
            new EventDefinition("SampleEntityChanged", typeof(SampleEntity), EntityOperation.Any)
                .WithDescription("Univerzální událost spuštěná při jakékoliv změně ukázkové entity")
        };
    }

    /// <summary>
    /// Registruje všechny definice událostí pro SampleEntity do registry.
    /// </summary>
    /// <param name="registry">Registry definic událostí</param>
    public static void RegisterAll(IEventDefinitionRegistry registry)
    {
        if (registry == null)
            throw new ArgumentNullException(nameof(registry));

        registry.RegisterRange(GetDefinitions());
    }

    /// <summary>
    /// Vytvoří definici události s podmínkou pro konkrétní hodnotu vlastnosti.
    /// </summary>
    /// <param name="eventName">Název události</param>
    /// <param name="propertyName">Název vlastnosti</param>
    /// <param name="expectedValue">Očekávaná hodnota</param>
    /// <param name="description">Popis události</param>
    /// <returns>Definice události</returns>
    public static EventDefinition CreatePropertyValueEvent(
        string eventName, 
        string propertyName, 
        object expectedValue, 
        string description)
    {
        return new EventDefinition(eventName, typeof(SampleEntity), EntityOperation.Updated)
            .WithTrackedProperties(propertyName)
            .WithCondition(entity =>
            {
                if (entity is not SampleEntity sample)
                    return false;

                var property = typeof(SampleEntity).GetProperty(propertyName);
                if (property == null)
                    return false;

                var currentValue = property.GetValue(sample);
                return Equals(currentValue, expectedValue);
            })
            .WithDescription(description);
    }

    /// <summary>
    /// Vytvoří definici události pro změnu číselné vlastnosti nad určitou hodnotu.
    /// </summary>
    /// <param name="eventName">Název události</param>
    /// <param name="propertyName">Název vlastnosti</param>
    /// <param name="threshold">Prahová hodnota</param>
    /// <param name="description">Popis události</param>
    /// <returns>Definice události</returns>
    public static EventDefinition CreateNumericThresholdEvent(
        string eventName,
        string propertyName,
        int threshold,
        string description)
    {
        return new EventDefinition(eventName, typeof(SampleEntity), EntityOperation.Updated)
            .WithTrackedProperties(propertyName)
            .WithCondition(entity =>
            {
                if (entity is not SampleEntity sample)
                    return false;

                var property = typeof(SampleEntity).GetProperty(propertyName);
                if (property == null)
                    return false;

                var currentValue = property.GetValue(sample);
                return currentValue is int intValue && intValue > threshold;
            })
            .WithDescription(description);
    }
}
