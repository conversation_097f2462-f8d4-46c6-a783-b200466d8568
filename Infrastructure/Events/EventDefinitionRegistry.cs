using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace Infrastructure.Events;

/// <summary>
/// Implementace registry pro definice doménových událostí.
/// Používá thread-safe kolekce pro podporu concurrent přístupu.
/// </summary>
public class EventDefinitionRegistry : IEventDefinitionRegistry
{
    private readonly ConcurrentDictionary<string, EventDefinition> _definitions = new();

    /// <summary>
    /// Počet registrovaných definic událostí.
    /// </summary>
    public int Count => _definitions.Count;

    /// <summary>
    /// Registruje definici události.
    /// </summary>
    /// <param name="definition">Definice události</param>
    /// <exception cref="ArgumentNullException">Pokud je definice null</exception>
    /// <exception cref="ArgumentException">Pokud už existuje definice se stejným názvem</exception>
    public void Register(EventDefinition definition)
    {
        if (definition == null)
            throw new ArgumentNullException(nameof(definition));

        if (string.IsNullOrWhiteSpace(definition.EventName))
            throw new ArgumentException("Název události nesmí být prázdný.", nameof(definition));

        if (!_definitions.TryAdd(definition.EventName, definition))
        {
            throw new ArgumentException($"Definice události s názvem '{definition.EventName}' už existuje.", nameof(definition));
        }
    }

    /// <summary>
    /// Registruje více definic událostí najednou.
    /// </summary>
    /// <param name="definitions">Kolekce definic událostí</param>
    /// <exception cref="ArgumentNullException">Pokud je kolekce null</exception>
    public void RegisterRange(IEnumerable<EventDefinition> definitions)
    {
        if (definitions == null)
            throw new ArgumentNullException(nameof(definitions));

        foreach (var definition in definitions)
        {
            Register(definition);
        }
    }

    /// <summary>
    /// Získá všechny definice událostí pro daný typ entity a operaci.
    /// </summary>
    /// <param name="entityType">Typ entity</param>
    /// <param name="operation">Operace</param>
    /// <returns>Kolekce definic událostí</returns>
    public IEnumerable<EventDefinition> GetDefinitions(Type entityType, EntityOperation operation)
    {
        if (entityType == null)
            return Enumerable.Empty<EventDefinition>();

        return _definitions.Values
            .Where(d => d.IsActive && 
                       d.EntityType == entityType && 
                       (d.Operation == operation || d.Operation == EntityOperation.Any));
    }

    /// <summary>
    /// Získá všechny definice událostí pro daný typ entity.
    /// </summary>
    /// <param name="entityType">Typ entity</param>
    /// <returns>Kolekce definic událostí</returns>
    public IEnumerable<EventDefinition> GetDefinitions(Type entityType)
    {
        if (entityType == null)
            return Enumerable.Empty<EventDefinition>();

        return _definitions.Values
            .Where(d => d.IsActive && d.EntityType == entityType);
    }

    /// <summary>
    /// Získá definici události podle názvu.
    /// </summary>
    /// <param name="eventName">Název události</param>
    /// <returns>Definice události nebo null</returns>
    public EventDefinition? GetDefinition(string eventName)
    {
        if (string.IsNullOrWhiteSpace(eventName))
            return null;

        _definitions.TryGetValue(eventName, out var definition);
        return definition;
    }

    /// <summary>
    /// Získá všechny registrované definice událostí.
    /// </summary>
    /// <returns>Kolekce všech definic událostí</returns>
    public IEnumerable<EventDefinition> GetAllDefinitions()
    {
        return _definitions.Values.ToList();
    }

    /// <summary>
    /// Odstraní definici události podle názvu.
    /// </summary>
    /// <param name="eventName">Název události</param>
    /// <returns>True, pokud byla definice odstraněna</returns>
    public bool Remove(string eventName)
    {
        if (string.IsNullOrWhiteSpace(eventName))
            return false;

        return _definitions.TryRemove(eventName, out _);
    }

    /// <summary>
    /// Vymaže všechny definice událostí.
    /// </summary>
    public void Clear()
    {
        _definitions.Clear();
    }

    /// <summary>
    /// Ověří, zda je registrována definice události s daným názvem.
    /// </summary>
    /// <param name="eventName">Název události</param>
    /// <returns>True, pokud je definice registrována</returns>
    public bool Contains(string eventName)
    {
        if (string.IsNullOrWhiteSpace(eventName))
            return false;

        return _definitions.ContainsKey(eventName);
    }
}
