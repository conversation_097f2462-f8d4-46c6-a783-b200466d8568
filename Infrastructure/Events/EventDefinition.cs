using System;
using System.Collections.Generic;
using System.Linq;
using SharedKernel.Domain;

namespace Infrastructure.Events;

/// <summary>
/// Definice doménové události - technická třída specifikující kdy a jak se má událost generovat.
/// Tato třída není součástí obchodních entit, ale slouží pro konfiguraci systému událostí.
/// </summary>
public class EventDefinition
{
    /// <summary>
    /// Jedinečný název události.
    /// </summary>
    public string EventName { get; set; } = string.Empty;

    /// <summary>
    /// Typ entity, na které událost vzniká.
    /// </summary>
    public Type EntityType { get; set; } = null!;

    /// <summary>
    /// Název entity pro lepší čitelnost.
    /// </summary>
    public string EntityName => EntityType.Name;

    /// <summary>
    /// Operace, při které se událost spouští.
    /// </summary>
    public EventType Operation { get; set; }

    /// <summary>
    /// Seznam vlastností, které se sledují pro změny.
    /// Pokud je prázdný, sledují se všechny vlastnosti.
    /// </summary>
    public List<string> TrackedProperties { get; set; } = new();

    /// <summary>
    /// Volitelná podmínka pro spuštění události.
    /// Pokud je null, událost se spouští vždy při dané operaci.
    /// </summary>
    public Func<object, bool>? Condition { get; set; }

    /// <summary>
    /// Popis události pro dokumentační účely.
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Indikuje, zda je definice aktivní.
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Datum vytvoření definice.
    /// </summary>
    public DateTimeOffset CreatedAt { get; set; } = DateTimeOffset.UtcNow;

    /// <summary>
    /// Konstruktor pro vytvoření definice události.
    /// </summary>
    /// <param name="eventName">Název události</param>
    /// <param name="entityType">Typ entity</param>
    /// <param name="operation">Operace</param>
    public EventDefinition(string eventName, Type entityType, EventType operation)
    {
        EventName = eventName;
        EntityType = entityType;
        Operation = operation;
    }

    /// <summary>
    /// Konstruktor pro vytvoření definice události s popisem.
    /// </summary>
    /// <param name="eventName">Název události</param>
    /// <param name="entityType">Typ entity</param>
    /// <param name="operation">Operace</param>
    /// <param name="description">Popis události</param>
    public EventDefinition(string eventName, Type entityType, EventType operation, string description)
        : this(eventName, entityType, operation)
    {
        Description = description;
    }

    /// <summary>
    /// Nastaví sledované vlastnosti.
    /// </summary>
    /// <param name="properties">Seznam názvů vlastností</param>
    /// <returns>Aktuální instanci pro fluent API</returns>
    public EventDefinition WithTrackedProperties(params string[] properties)
    {
        TrackedProperties.AddRange(properties);
        return this;
    }

    /// <summary>
    /// Nastaví podmínku pro spuštění události.
    /// </summary>
    /// <param name="condition">Podmínka</param>
    /// <returns>Aktuální instanci pro fluent API</returns>
    public EventDefinition WithCondition(Func<object, bool> condition)
    {
        Condition = condition;
        return this;
    }

    /// <summary>
    /// Nastaví popis události.
    /// </summary>
    /// <param name="description">Popis</param>
    /// <returns>Aktuální instanci pro fluent API</returns>
    public EventDefinition WithDescription(string description)
    {
        Description = description;
        return this;
    }

    /// <summary>
    /// Deaktivuje definici události.
    /// </summary>
    /// <returns>Aktuální instanci pro fluent API</returns>
    public EventDefinition Deactivate()
    {
        IsActive = false;
        return this;
    }

    /// <summary>
    /// Aktivuje definici události.
    /// </summary>
    /// <returns>Aktuální instanci pro fluent API</returns>
    public EventDefinition Activate()
    {
        IsActive = true;
        return this;
    }

    /// <summary>
    /// Ověří, zda se má událost spustit pro danou entitu.
    /// </summary>
    /// <param name="entity">Entita</param>
    /// <returns>True, pokud se má událost spustit</returns>
    public bool ShouldTrigger(object entity)
    {
        if (!IsActive)
            return false;

        if (entity.GetType() != EntityType)
            return false;

        return Condition?.Invoke(entity) ?? true;
    }

    /// <summary>
    /// Ověří, zda se změnila některá ze sledovaných vlastností.
    /// </summary>
    /// <param name="changedProperties">Seznam změněných vlastností</param>
    /// <returns>True, pokud se změnila sledovaná vlastnost</returns>
    public bool HasTrackedPropertyChanged(IEnumerable<string> changedProperties)
    {
        // Pokud nejsou definovány sledované vlastnosti, sledujeme všechny
        if (!TrackedProperties.Any())
            return true;

        return TrackedProperties.Any(prop => changedProperties.Contains(prop));
    }
}


