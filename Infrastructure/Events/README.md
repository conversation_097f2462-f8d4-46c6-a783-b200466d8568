# Systém doménových událostí

Tento systém umožňuje definovat a automaticky generovat doménové události na základě změn entit v databázi. Události jsou plně uživatelské a konfigurují se pomocí technických tříd, kter<PERSON> nejsou součástí obchodních entit.

## Přehled komponent

### EventDefinition
Technická třída definující kdy a jak se má událost generovat:
- **Název události** - jedinečný identifikátor
- **Typ entity** - na jaké entitě událost vzniká
- **Operace** - při jak<PERSON> operaci (Created, Updated, Deleted, Any)
- **Sledované vlastnosti** - kter<PERSON> vlastnosti se sledují pro změny
- **Podmínky** - volite<PERSON><PERSON> podmínky pro spuštění události

### EventDefinitionRegistry
Registry pro správu definic událostí:
- Thread-safe implementace
- Registrace a vyhledávání definic
- Filtrování podle entity a operace

### DomainEventInterceptor
EF Core interceptor pro automatické generování událostí:
- Zachytává změny entit před uložením
- Generuje události na základě registrovaných definic
- Přidává události k entitám pro následné zpracování

### EntityChangedEvent
Generická doménová událost obsahující:
- Informace o změněné entitě
- Typ operace
- Seznam změněných vlastností
- Původní a aktuální hodnoty

## Použití

### 1. Definice událostí

```csharp
// Základní definice události
var definition = new EventDefinition("SampleEntityCreated", typeof(SampleEntity), EntityOperation.Created)
    .WithDescription("Událost při vytvoření ukázkové entity");

// Událost při změně konkrétní vlastnosti
var nameChangedDefinition = new EventDefinition("SampleEntityNameChanged", typeof(SampleEntity), EntityOperation.Updated)
    .WithTrackedProperties(nameof(SampleEntity.Name))
    .WithDescription("Událost při změně názvu");

// Událost s podmínkou
var activatedDefinition = new EventDefinition("SampleEntityActivated", typeof(SampleEntity), EntityOperation.Updated)
    .WithTrackedProperties(nameof(SampleEntity.IsActive))
    .WithCondition(entity => entity is SampleEntity sample && sample.IsActive)
    .WithDescription("Událost při aktivaci entity");
```

### 2. Registrace definic

```csharp
// V Infrastructure/Events/SampleEntityEventDefinitions.cs
public static void RegisterAll(IEventDefinitionRegistry registry)
{
    registry.RegisterRange(GetDefinitions());
}

// Automatická registrace při startu aplikace
services.AddHostedService<EventDefinitionRegistrationService>();
```

### 3. Zpracování událostí

```csharp
public class EntityChangedEventHandler : INotificationHandler<DomainEventNotification<EntityChangedEvent>>
{
    public async Task Handle(DomainEventNotification<EntityChangedEvent> notification, CancellationToken cancellationToken)
    {
        var domainEvent = notification.DomainEvent;
        
        // Zpracování podle názvu události
        switch (domainEvent.EventName)
        {
            case "SampleEntityCreated":
                await HandleEntityCreated(domainEvent);
                break;
                
            case "SampleEntityNameChanged":
                await HandleNameChanged(domainEvent);
                break;
        }
    }
}
```

## Ukázkové definice pro SampleEntity

Systém obsahuje předpřipravené definice pro SampleEntity:

- **SampleEntityCreated** - při vytvoření entity
- **SampleEntityNameChanged** - při změně názvu
- **SampleEntityStatusChanged** - při změně stavu aktivity
- **SampleEntityActivated** - při aktivaci (IsActive = true)
- **SampleEntityDeactivated** - při deaktivaci (IsActive = false)
- **SampleEntityAgeChangedAdult** - při změně věku u dospělých (≥18)
- **SampleEntityDescriptionChanged** - při změně popisu
- **SampleEntityBirthDateChanged** - při změně data narození
- **SampleEntityDeleted** - při smazání entity
- **SampleEntityProfileUpdated** - při změně více vlastností najednou
- **SampleEntityChanged** - univerzální událost pro jakoukoliv změnu

## Architektura

```
Infrastructure/Events/
├── EventDefinition.cs              # Definice události
├── IEventDefinitionRegistry.cs     # Rozhraní registry
├── EventDefinitionRegistry.cs      # Implementace registry
├── SampleEntityEventDefinitions.cs # Ukázkové definice
├── EventDefinitionRegistrationService.cs # Hosted service pro registraci
└── README.md                       # Dokumentace

Infrastructure/Persistence/Interceptors/
└── DomainEventInterceptor.cs       # EF Core interceptor

Domain/Events/
└── EntityChangedEvent.cs           # Generická doménová událost

Application/Features/Events/
└── EntityChangedEventHandler.cs    # Ukázkový handler
```

## Výhody systému

1. **Oddělení technické konfigurace od business logiky**
2. **Flexibilní definice událostí** - podmínky, sledované vlastnosti
3. **Automatické generování** - žádný manuální kód v entitách
4. **Type-safe** - kompilační kontrola typů
5. **Testovatelnost** - jednoduché unit testy
6. **Performance** - efektivní zpracování pouze relevantních změn
7. **Rozšiřitelnost** - snadné přidání nových definic

## Rozšíření

Pro přidání událostí pro novou entitu:

1. Vytvořte třídu s definicemi (např. `OrderEventDefinitions`)
2. Zaregistrujte definice v `EventDefinitionRegistrationService`
3. Implementujte specifické handlery pro zpracování událostí
4. Otestujte funkčnost

Systém je navržen tak, aby byl snadno rozšiřitelný a udržovatelný.
