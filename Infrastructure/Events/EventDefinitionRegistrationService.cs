using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Infrastructure.Events;

/// <summary>
/// Hosted service pro registraci definic doménových událostí při startu aplikace.
/// </summary>
public class EventDefinitionRegistrationService : IHostedService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<EventDefinitionRegistrationService> _logger;

    /// <summary>
    /// Inicializuje novou instanci EventDefinitionRegistrationService.
    /// </summary>
    /// <param name="serviceProvider">Service provider</param>
    /// <param name="logger">Logger</param>
    public EventDefinitionRegistrationService(
        IServiceProvider serviceProvider,
        ILogger<EventDefinitionRegistrationService> logger)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Spustí registraci definic událostí.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Zahajuji registraci definic doménových událostí...");

            using var scope = _serviceProvider.CreateScope();
            var registry = scope.ServiceProvider.GetRequiredService<IEventDefinitionRegistry>();

            // Registrace definic pro SampleEntity
            SampleEntityEventDefinitions.RegisterAll(registry);

            _logger.LogInformation("Registrace definic doménových událostí dokončena. Celkem registrováno: {Count} definic.", 
                registry.Count);

            // Výpis registrovaných definic pro debugging
            if (_logger.IsEnabled(LogLevel.Debug))
            {
                foreach (var definition in registry.GetAllDefinitions())
                {
                    _logger.LogDebug("Registrována definice události: {EventName} pro entitu {EntityName} při operaci {Operation}",
                        definition.EventName, definition.EntityName, definition.Operation);
                }
            }

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při registraci definic doménových událostí");
            throw;
        }
    }

    /// <summary>
    /// Zastaví službu (není potřeba žádná akce).
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("EventDefinitionRegistrationService byl zastaven.");
        return Task.CompletedTask;
    }
}
