using Domain.Entities;
using SharedKernel.Domain;
using System;
using System.Collections.Generic;

namespace Infrastructure.Events.Examples;

/// <summary>
/// Ukázkové vlastní definice událostí demonstrující pokročilé použití systému.
/// </summary>
public static class CustomEventDefinitions
{
    /// <summary>
    /// Vytvoří definici události pro významné změny věku (milníky).
    /// </summary>
    /// <returns>Definice události</returns>
    public static EventDefinition CreateAgeMilestoneEvent()
    {
        return new EventDefinition("SampleEntityAgeMilestone", typeof(SampleEntity), EventType.Updated)
            .WithTrackedProperties(nameof(SampleEntity.Age))
            .WithCondition(entity =>
            {
                if (entity is not SampleEntity sample || !sample.Age.HasValue)
                    return false;

                var age = sample.Age.Value;
                // Milníky: 18, 21, 30, 40, 50, 60, 65, 70, 80, 90, 100
                return age == 18 || age == 21 || age % 10 == 0 && age >= 30;
            })
            .WithDescription("Událost spuštěná při dosažení významného věkového milníku");
    }

    /// <summary>
    /// Vytvoří definici události pro změnu na prázdný nebo neprázdný popis.
    /// </summary>
    /// <returns>Definice události</returns>
    public static EventDefinition CreateDescriptionStatusEvent()
    {
        return new EventDefinition("SampleEntityDescriptionStatusChanged", typeof(SampleEntity), EventType.Updated)
            .WithTrackedProperties(nameof(SampleEntity.Description))
            .WithDescription("Událost spuštěná při změně stavu popisu (prázdný/neprázdný)");
    }

    /// <summary>
    /// Vytvoří definici události pro kompletní profil (všechny povinné údaje vyplněny).
    /// </summary>
    /// <returns>Definice události</returns>
    public static EventDefinition CreateCompleteProfileEvent()
    {
        return new EventDefinition("SampleEntityProfileCompleted", typeof(SampleEntity), EventType.Updated)
            .WithTrackedProperties(nameof(SampleEntity.Name), nameof(SampleEntity.Description),
                                 nameof(SampleEntity.Age), nameof(SampleEntity.DateOfBirth))
            .WithCondition(entity =>
            {
                if (entity is not SampleEntity sample)
                    return false;

                return !string.IsNullOrWhiteSpace(sample.Name) &&
                       !string.IsNullOrWhiteSpace(sample.Description) &&
                       sample.Age.HasValue &&
                       sample.DateOfBirth.HasValue &&
                       sample.IsActive;
            })
            .WithDescription("Událost spuštěná při dokončení kompletního profilu entity");
    }

    /// <summary>
    /// Vytvoří definici události pro nekonzistentní data (věk neodpovídá datu narození).
    /// </summary>
    /// <returns>Definice události</returns>
    public static EventDefinition CreateDataInconsistencyEvent()
    {
        return new EventDefinition("SampleEntityDataInconsistency", typeof(SampleEntity), EventType.Updated)
            .WithTrackedProperties(nameof(SampleEntity.Age), nameof(SampleEntity.DateOfBirth))
            .WithCondition(entity =>
            {
                if (entity is not SampleEntity sample || !sample.Age.HasValue || !sample.DateOfBirth.HasValue)
                    return false;

                var calculatedAge = DateTime.Now.Year - sample.DateOfBirth.Value.Year;
                if (sample.DateOfBirth.Value.Date > DateTime.Now.AddYears(-calculatedAge))
                    calculatedAge--;

                return Math.Abs(calculatedAge - sample.Age.Value) > 1; // Tolerance 1 rok
            })
            .WithDescription("Událost spuštěná při detekci nekonzistence mezi věkem a datem narození");
    }

    /// <summary>
    /// Vytvoří definici události pro dlouhý popis (více než určitý počet znaků).
    /// </summary>
    /// <param name="maxLength">Maximální délka popisu</param>
    /// <returns>Definice události</returns>
    public static EventDefinition CreateLongDescriptionEvent(int maxLength = 500)
    {
        return new EventDefinition("SampleEntityLongDescription", typeof(SampleEntity), EventType.Updated)
            .WithTrackedProperties(nameof(SampleEntity.Description))
            .WithCondition(entity =>
            {
                if (entity is not SampleEntity sample)
                    return false;

                return !string.IsNullOrEmpty(sample.Description) && sample.Description.Length > maxLength;
            })
            .WithDescription($"Událost spuštěná při nastavení popisu delšího než {maxLength} znaků");
    }

    /// <summary>
    /// Vytvoří definici události pro podezřelé změny (více vlastností změněno najednou).
    /// </summary>
    /// <returns>Definice události</returns>
    public static EventDefinition CreateSuspiciousChangesEvent()
    {
        return new EventDefinition("SampleEntitySuspiciousChanges", typeof(SampleEntity), EventType.Updated)
            .WithTrackedProperties(nameof(SampleEntity.Name), nameof(SampleEntity.Description), 
                                 nameof(SampleEntity.Age), nameof(SampleEntity.DateOfBirth))
            .WithDescription("Událost spuštěná při podezřelých změnách (více než 2 vlastnosti najednou)");
    }

    /// <summary>
    /// Získá všechny ukázkové definice událostí.
    /// </summary>
    /// <returns>Kolekce definic událostí</returns>
    public static IEnumerable<EventDefinition> GetAllCustomDefinitions()
    {
        return new[]
        {
            CreateAgeMilestoneEvent(),
            CreateDescriptionStatusEvent(),
            CreateCompleteProfileEvent(),
            CreateDataInconsistencyEvent(),
            CreateLongDescriptionEvent(),
            CreateSuspiciousChangesEvent()
        };
    }

    /// <summary>
    /// Registruje všechny ukázkové definice do registry.
    /// </summary>
    /// <param name="registry">Registry definic událostí</param>
    public static void RegisterAllCustomDefinitions(IEventDefinitionRegistry registry)
    {
        if (registry == null)
            throw new ArgumentNullException(nameof(registry));

        registry.RegisterRange(GetAllCustomDefinitions());
    }

    /// <summary>
    /// Vytvoří definici události s dynamickou podmínkou na základě lambda výrazu.
    /// </summary>
    /// <param name="eventName">Název události</param>
    /// <param name="propertyName">Název vlastnosti</param>
    /// <param name="condition">Podmínka pro spuštění</param>
    /// <param name="description">Popis události</param>
    /// <returns>Definice události</returns>
    public static EventDefinition CreateDynamicConditionEvent(
        string eventName,
        string propertyName,
        Func<SampleEntity, bool> condition,
        string description)
    {
        return new EventDefinition(eventName, typeof(SampleEntity), EventType.Updated)
            .WithTrackedProperties(propertyName)
            .WithCondition(entity => entity is SampleEntity sample && condition(sample))
            .WithDescription(description);
    }

    /// <summary>
    /// Vytvoří definici události pro změnu hodnoty vlastnosti z jedné konkrétní hodnoty na jinou.
    /// </summary>
    /// <param name="eventName">Název události</param>
    /// <param name="propertyName">Název vlastnosti</param>
    /// <param name="fromValue">Původní hodnota</param>
    /// <param name="toValue">Nová hodnota</param>
    /// <param name="description">Popis události</param>
    /// <returns>Definice události</returns>
    public static EventDefinition CreateValueTransitionEvent(
        string eventName,
        string propertyName,
        object fromValue,
        object toValue,
        string description)
    {
        return new EventDefinition(eventName, typeof(SampleEntity), EventType.Updated)
            .WithTrackedProperties(propertyName)
            .WithCondition(entity =>
            {
                if (entity is not SampleEntity sample)
                    return false;

                var property = typeof(SampleEntity).GetProperty(propertyName);
                if (property == null)
                    return false;

                var currentValue = property.GetValue(sample);
                return Equals(currentValue, toValue);
            })
            .WithDescription(description);
    }
}
