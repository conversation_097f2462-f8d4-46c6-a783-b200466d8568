// using Microsoft.AspNetCore.Identity;
// using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
// using Microsoft.EntityFrameworkCore;
//
// namespace Infrastructure.Identity;
//
// public class AuthDbContext : IdentityDbContext<ApplicationUser, IdentityRole<int>, int>
// {
//     public AuthDbContext(DbContextOptions<AuthDbContext> options)
//         : base(options)
//     {
//     }
//
//     // protected override void OnModelCreating(ModelBuilder builder)
//     // {
//     //     base.OnModelCreating(builder);
//     //     // <PERSON><PERSON><PERSON> konfigurace: indexy, seed data...
//     // }
// }