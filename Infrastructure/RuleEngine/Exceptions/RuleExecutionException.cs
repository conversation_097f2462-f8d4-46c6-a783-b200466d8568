namespace Infrastructure.RuleEngine.Exceptions;

/// <summary>
/// Výjimka vyvolaná při chybě během vykonávání obchodního pravidla.
/// </summary>
public class RuleExecutionException : Exception
{
    /// <summary>
    /// Inicializuje novou instanci RuleExecutionException.
    /// </summary>
    /// <param name="message">Zpráva popisující chybu</param>
    public RuleExecutionException(string message) : base(message)
    {
    }

    /// <summary>
    /// Inicializuje novou instanci RuleExecutionException s vnitřn<PERSON> výjimkou.
    /// </summary>
    /// <param name="message">Zpráva popisující chybu</param>
    /// <param name="innerException">Vnitřní výjimka</param>
    public RuleExecutionException(string message, Exception innerException) : base(message, innerException)
    {
    }
}
