namespace Infrastructure.RuleEngine.Validation;

/// <summary>
/// Výsledek validace obchodního pravidla.
/// </summary>
public class RuleValidationResult
{
    /// <summary>
    /// Ur<PERSON><PERSON><PERSON>, zda je pravidlo syntakticky správné.
    /// </summary>
    public bool IsValid { get; private set; }

    /// <summary>
    /// Chybová zpráva v případě neplatného pravidla.
    /// </summary>
    public string? ErrorMessage { get; private set; }

    /// <summary>
    /// Seznam všech nalezených chyb.
    /// </summary>
    public List<string> Errors { get; private set; } = new();

    private RuleValidationResult(bool isValid, string? errorMessage = null)
    {
        IsValid = isValid;
        ErrorMessage = errorMessage;
        if (!string.IsNullOrEmpty(errorMessage))
        {
            Errors.Add(errorMessage);
        }
    }

    /// <summary>
    /// Vyt<PERSON><PERSON><PERSON> výsledek pro platné pravidlo.
    /// </summary>
    /// <returns>Platný výsledek validace</returns>
    public static RuleValidationResult Valid()
    {
        return new RuleValidationResult(true);
    }

    /// <summary>
    /// Vytvoří výsledek pro neplatné pravidlo s chybovou zprávou.
    /// </summary>
    /// <param name="errorMessage">Popis chyby</param>
    /// <returns>Neplatný výsledek validace</returns>
    public static RuleValidationResult Invalid(string errorMessage)
    {
        return new RuleValidationResult(false, errorMessage);
    }

    /// <summary>
    /// Vytvoří výsledek pro neplatné pravidlo s více chybami.
    /// </summary>
    /// <param name="errors">Seznam chyb</param>
    /// <returns>Neplatný výsledek validace</returns>
    public static RuleValidationResult Invalid(IEnumerable<string> errors)
    {
        var result = new RuleValidationResult(false);
        result.Errors.AddRange(errors);
        result.ErrorMessage = string.Join("; ", errors);
        return result;
    }

    /// <summary>
    /// Přidá další chybu k existujícímu výsledku.
    /// </summary>
    /// <param name="error">Chybová zpráva</param>
    public void AddError(string error)
    {
        IsValid = false;
        Errors.Add(error);
        ErrorMessage = string.Join("; ", Errors);
    }

    /// <summary>
    /// Kombinuje dva výsledky validace.
    /// </summary>
    /// <param name="other">Další výsledek validace</param>
    /// <returns>Kombinovaný výsledek</returns>
    public RuleValidationResult Combine(RuleValidationResult other)
    {
        if (IsValid && other.IsValid)
            return Valid();

        var allErrors = Errors.Concat(other.Errors).ToList();
        return Invalid(allErrors);
    }

    public override string ToString()
    {
        return IsValid ? "Platné pravidlo" : $"Neplatné pravidlo: {ErrorMessage}";
    }
}
