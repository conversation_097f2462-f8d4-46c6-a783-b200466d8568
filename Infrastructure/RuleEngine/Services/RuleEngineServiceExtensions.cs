using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Infrastructure.RuleEngine.Services;

/// <summary>
/// Extension metody pro registraci služeb Rule Engine do DI kontejneru.
/// </summary>
public static class RuleEngineServiceExtensions
{
    /// <summary>
    /// Registruje všechny služby potřebné pro Rule Engine včetně cache.
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection pro fluent API</returns>
    public static IServiceCollection AddRuleEngineServices(this IServiceCollection services)
    {
        // Registrujeme cache službu jako singleton pro sdílení napříč aplikací
        services.AddSingleton<IRuleCacheService, RuleCacheService>();
        
        // Registrujeme ostatní služby Rule Engine
        services.AddScoped<CalculationEngine>();
        services.AddScoped<RuleRepository>();
        services.AddScoped<IExpressionBuilder, ExpressionBuilder>();
        services.AddScoped<IRuleDataProvider, RuleDataProvider>();
        
        return services;
    }

    /// <summary>
    /// Registruje pouze cache službu Rule Engine.
    /// Užitečné pokud ostatní služby jsou registrovány jinde.
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection pro fluent API</returns>
    public static IServiceCollection AddRuleEngineCache(this IServiceCollection services)
    {
        services.AddSingleton<IRuleCacheService, RuleCacheService>();
        return services;
    }
}
