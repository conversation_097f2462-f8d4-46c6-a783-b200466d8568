using System.Collections.Concurrent;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;

namespace Infrastructure.RuleEngine.Services;

/// <summary>
/// Implementace IRuleCacheService pro cachování v Business Rules Engine.
/// Používá thread-safe in-memory cache s podporou expirací a pattern-based invalidace.
/// </summary>
public class RuleCacheService : IRuleCacheService, IDisposable
{
    private readonly ILogger<RuleCacheService> _logger;
    private readonly ConcurrentDictionary<string, CacheEntry> _cache = new();
    private readonly Timer _cleanupTimer;
    private readonly object _statsLock = new();
    
    private long _hitCount = 0;
    private long _missCount = 0;

    /// <summary>
    /// Inicializuje novou instanci RuleCacheService.
    /// </summary>
    /// <param name="logger">Logger pro diagnostiku</param>
    public RuleCacheService(ILogger<RuleCacheService> logger)
    {
        _logger = logger;
        
        // Spustíme timer pro čištěn<PERSON> expirovaných záznamů každých 5 minut
        _cleanupTimer = new Timer(CleanupExpiredEntries, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        
        _logger.LogDebug("RuleCacheService inicializován s automatickým čištěním každých 5 minut");
    }

    /// <inheritdoc />
    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiry = null)
    {
        if (string.IsNullOrWhiteSpace(key))
            throw new ArgumentException("Cache klíč nesmí být prázdný", nameof(key));
        
        if (factory == null)
            throw new ArgumentNullException(nameof(factory));

        var expiryTime = expiry ?? TimeSpan.FromMinutes(10);
        
        // Pokusíme se získat z cache
        if (_cache.TryGetValue(key, out var existingEntry) && !existingEntry.IsExpired)
        {
            Interlocked.Increment(ref _hitCount);
            _logger.LogTrace("Cache hit pro klíč '{Key}'", key);
            return (T)existingEntry.Value;
        }

        // Cache miss - vytvoříme novou hodnotu
        Interlocked.Increment(ref _missCount);
        _logger.LogTrace("Cache miss pro klíč '{Key}' - vytváříme novou hodnotu", key);

        var startTime = DateTime.UtcNow;
        var value = await factory();
        var creationTime = DateTime.UtcNow - startTime;

        // Uložíme do cache
        var entry = new CacheEntry(value, DateTime.UtcNow.Add(expiryTime));
        _cache.AddOrUpdate(key, entry, (k, existing) => entry);

        _logger.LogTrace("Hodnota pro klíč '{Key}' vytvořena za {CreationTime}ms a uložena do cache s expirací {Expiry}", 
            key, creationTime.TotalMilliseconds, expiryTime);

        return value;
    }

    /// <inheritdoc />
    public async Task<T> GetOrSetAsync<T>(string key, Func<T> factory, TimeSpan? expiry = null)
    {
        return await GetOrSetAsync(key, () => Task.FromResult(factory()), expiry);
    }

    /// <inheritdoc />
    public async Task<int> InvalidateAsync(string pattern)
    {
        if (string.IsNullOrWhiteSpace(pattern))
            throw new ArgumentException("Pattern nesmí být prázdný", nameof(pattern));

        var regex = CreateRegexFromPattern(pattern);
        var keysToRemove = new List<string>();

        // Najdeme všechny klíče odpovídající patternu
        foreach (var kvp in _cache)
        {
            if (regex.IsMatch(kvp.Key))
            {
                keysToRemove.Add(kvp.Key);
            }
        }

        // Odstraníme nalezené klíče
        var removedCount = 0;
        foreach (var key in keysToRemove)
        {
            if (_cache.TryRemove(key, out _))
            {
                removedCount++;
            }
        }

        _logger.LogDebug("Invalidováno {Count} cache záznamů podle patternu '{Pattern}'", removedCount, pattern);
        return await Task.FromResult(removedCount);
    }

    /// <inheritdoc />
    public async Task<bool> RemoveAsync(string key)
    {
        if (string.IsNullOrWhiteSpace(key))
            return false;

        var removed = _cache.TryRemove(key, out _);
        if (removed)
        {
            _logger.LogTrace("Cache záznam s klíčem '{Key}' byl odstraněn", key);
        }

        return await Task.FromResult(removed);
    }

    /// <inheritdoc />
    public async Task ClearAsync()
    {
        var count = _cache.Count;
        _cache.Clear();
        
        // Resetujeme statistiky
        lock (_statsLock)
        {
            _hitCount = 0;
            _missCount = 0;
        }

        _logger.LogInformation("Cache byla vymazána ({Count} záznamů)", count);
        await Task.CompletedTask;
    }

    /// <inheritdoc />
    public RuleCacheStatistics GetStatistics()
    {
        lock (_statsLock)
        {
            return new RuleCacheStatistics
            {
                TotalEntries = _cache.Count,
                HitCount = _hitCount,
                MissCount = _missCount
            };
        }
    }

    /// <summary>
    /// Vytvoří regex z pattern s podporou wildcards (* a ?).
    /// </summary>
    private static Regex CreateRegexFromPattern(string pattern)
    {
        // Escapujeme speciální regex znaky kromě * a ?
        var escaped = Regex.Escape(pattern);
        
        // Nahradíme escapované wildcards za regex ekvivalenty
        escaped = escaped.Replace("\\*", ".*").Replace("\\?", ".");
        
        // Přidáme anchors pro exact match
        return new Regex($"^{escaped}$", RegexOptions.Compiled | RegexOptions.IgnoreCase);
    }

    /// <summary>
    /// Vyčistí expirované záznamy z cache.
    /// </summary>
    private void CleanupExpiredEntries(object? state)
    {
        var expiredKeys = new List<string>();
        var now = DateTime.UtcNow;

        // Najdeme expirované klíče
        foreach (var kvp in _cache)
        {
            if (kvp.Value.IsExpired)
            {
                expiredKeys.Add(kvp.Key);
            }
        }

        // Odstraníme expirované záznamy
        var removedCount = 0;
        foreach (var key in expiredKeys)
        {
            if (_cache.TryRemove(key, out _))
            {
                removedCount++;
            }
        }

        if (removedCount > 0)
        {
            _logger.LogDebug("Automaticky odstraněno {Count} expirovaných cache záznamů", removedCount);
        }
    }

    /// <summary>
    /// Uvolní zdroje.
    /// </summary>
    public void Dispose()
    {
        _cleanupTimer?.Dispose();
        _cache.Clear();
        _logger.LogDebug("RuleCacheService byl uvolněn");
    }

    /// <summary>
    /// Reprezentuje záznam v cache s expirací.
    /// </summary>
    private class CacheEntry
    {
        public object Value { get; }
        public DateTime ExpiresAt { get; }
        public bool IsExpired => DateTime.UtcNow > ExpiresAt;

        public CacheEntry(object value, DateTime expiresAt)
        {
            Value = value;
            ExpiresAt = expiresAt;
        }
    }
}
