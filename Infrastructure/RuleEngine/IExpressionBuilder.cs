using System.Linq.Expressions;

namespace Infrastructure.RuleEngine;

/// <summary>
/// Interface pro builder v<PERSON><PERSON><PERSON> z RuleNode struktury.
/// Abstrakce umožňuje testování a různé implementace builderu.
/// </summary>
public interface IExpressionBuilder
{
    /// <summary>
    /// Sestaví Expression z RuleNode struktury.
    /// </summary>
    /// <param name="node">Kořenový uzel pravidla</param>
    /// <param name="param">Parametr reprezentující vstupní entitu</param>
    /// <returns>Expression reprezentující logiku pravidla</returns>
    Expression Build(RuleNode node, ParameterExpression param);
}
