using System.Collections.Generic;

namespace Infrastructure.RuleEngine.API;

/// <summary>
/// Metadata obsahující informace pro vytváření pravidel.
/// </summary>
public class RuleMetadata
{
    public required List<EntityMetadata> AvailableEntities { get; set; }
    public required List<OperatorMetadata> AvailableOperators { get; set; }
    public required List<AggregationMetadata> AvailableAggregations { get; set; }
    public required List<ValueTypeMetadata> AvailableValueTypes { get; set; }
    public required string SchemaVersion { get; set; }
}

/// <summary>
/// Metadata entity pro rule engine.
/// </summary>
public class EntityMetadata
{
    public required string Name { get; set; }
    public required string DisplayName { get; set; }
    public string? Description { get; set; }
}

/// <summary>
/// Metadata operátoru pro rule engine.
/// </summary>
public class OperatorMetadata
{
    public required string Value { get; set; }
    public required string DisplayName { get; set; }
    public required string Category { get; set; }
    public string? Description { get; set; }
}

/// <summary>
/// Metadata agregační funkce pro rule engine.
/// </summary>
public class AggregationMetadata
{
    public required string Value { get; set; }
    public required string DisplayName { get; set; }
    public string? Description { get; set; }
    public bool RequiresField { get; set; }
}

/// <summary>
/// Metadata datového typu pro rule engine.
/// </summary>
public class ValueTypeMetadata
{
    public required string Value { get; set; }
    public required string DisplayName { get; set; }
    public string? Description { get; set; }
}

/// <summary>
/// Metadata vlastnosti entity pro rule engine.
/// </summary>
public class PropertyMetadata
{
    public required string Name { get; set; }
    public required string DisplayName { get; set; }
    public required string Type { get; set; }
    public string? Description { get; set; }
}






