using System;
using System.Collections.Generic;
using System.Linq;
using Infrastructure.RuleEngine.Services;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;

namespace Infrastructure.RuleEngine.API;

/// <summary>
/// Minimal API endpointy pro RuleEngine systém.
/// Poskytuje technické rozhraní pro vytváření, úpravu a testování pravidel.
/// </summary>
public static class RuleEngineEndpoints
{
    /// <summary>
    /// Registruje všechny RuleEngine endpointy.
    /// </summary>
    public static IEndpointRouteBuilder MapRuleEngineEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/rule-engine")
            .WithTags("RuleEngine");

        // GET /api/rule-engine/rules - Získá všechna pravidla
        group.MapGet("/rules", async (
            [FromServices] IRuleRepository repository) =>
        {
            try
            {
                var rules = await repository.GetAllAsync();
                return Results.Ok(rules);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při načítání pravidel: {ex.Message}");
            }
        })
        .WithName("GetAllRules")
        .WithSummary("Získá všechna obchodní pravidla")
        .WithDescription("Vrací seznam všech obchodních pravidel v systému")
        .Produces<IEnumerable<BusinessRule>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status500InternalServerError);

        // GET /api/rule-engine/rules/{id} - Získá konkrétní pravidlo
        group.MapGet("/rules/{id:guid}", async (
            Guid id,
            [FromServices] IRuleRepository repository) =>
        {
            try
            {
                var rule = await repository.GetByIdAsync(id);
                if (rule == null)
                {
                    return Results.NotFound($"Pravidlo s ID {id} nebylo nalezeno.");
                }
                return Results.Ok(rule);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při načítání pravidla: {ex.Message}");
            }
        })
        .WithName("GetRule")
        .WithSummary("Získá obchodní pravidlo podle ID")
        .WithDescription("Vrací konkrétní obchodní pravidlo včetně jeho struktury")
        .Produces<BusinessRule>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status500InternalServerError);

        // POST /api/rule-engine/rules - Vytvoří nové pravidlo
        group.MapPost("/rules", async (
            [FromBody] BusinessRule rule,
            [FromServices] IRuleRepository repository,
            [FromServices] CalculationEngine engine) =>
        {
            try
            {
                // Kontrola duplicitního názvu
                if (await repository.ExistsWithNameAsync(rule.Name, null))
                {
                    return Results.BadRequest($"Pravidlo s názvem '{rule.Name}' již existuje.");
                }

                // Nastavení základních vlastností
                rule.Id = Guid.NewGuid();
                rule.SchemaVersion = "1.0";

                // Validace syntaxe
                var validationResult = await engine.ValidateRuleAsync(rule);
                if (!validationResult.IsValid)
                {
                    return Results.BadRequest(new {
                        Message = "Pravidlo obsahuje syntaktické chyby.",
                        Errors = new[] { validationResult.ErrorMessage }
                    });
                }

                await repository.AddAsync(rule);
                return Results.CreatedAtRoute("GetRule", new { id = rule.Id }, rule);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při vytváření pravidla: {ex.Message}");
            }
        })
        .WithName("CreateRule")
        .WithSummary("Vytvoří nové obchodní pravidlo")
        .WithDescription("Vytvoří nové obchodní pravidlo s validací syntaxe")
        .Accepts<BusinessRule>("application/json")
        .Produces<BusinessRule>(StatusCodes.Status201Created)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status500InternalServerError);

        // PUT /api/rule-engine/rules/{id} - Aktualizuje pravidlo
        group.MapPut("/rules/{id:guid}", async (
            Guid id,
            [FromBody] BusinessRule updatedRule,
            [FromServices] IRuleRepository repository,
            [FromServices] CalculationEngine engine) =>
        {
            try
            {
                var existingRule = await repository.GetByIdAsync(id);
                if (existingRule == null)
                {
                    return Results.NotFound($"Pravidlo s ID {id} nebylo nalezeno.");
                }

                // Kontrola duplicitního názvu (kromě aktuálního pravidla)
                if (await repository.ExistsWithNameAsync(updatedRule.Name, id))
                {
                    return Results.BadRequest($"Pravidlo s názvem '{updatedRule.Name}' již existuje.");
                }

                // Aktualizace pravidla (zachování ID a RowVersion)
                existingRule.Name = updatedRule.Name;
                existingRule.Description = updatedRule.Description;
                existingRule.TargetEntityName = updatedRule.TargetEntityName;
                existingRule.TargetProperty = updatedRule.TargetProperty;
                existingRule.RootNode = updatedRule.RootNode;
                existingRule.IsActive = updatedRule.IsActive;
                existingRule.InternalNotes = updatedRule.InternalNotes;

                // Validace syntaxe
                var validationResult = await engine.ValidateRuleAsync(existingRule);
                if (!validationResult.IsValid)
                {
                    return Results.BadRequest(new {
                        Message = "Pravidlo obsahuje syntaktické chyby.",
                        Errors = new[] { validationResult.ErrorMessage }
                    });
                }

                await repository.UpdateAsync(existingRule);

                // Vymazání z cache
                await engine.InvalidateRuleAsync(id);

                return Results.NoContent();
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při aktualizaci pravidla: {ex.Message}");
            }
        })
        .WithName("UpdateRule")
        .WithSummary("Aktualizuje obchodní pravidlo")
        .WithDescription("Aktualizuje existující obchodní pravidlo s validací syntaxe")
        .Accepts<BusinessRule>("application/json")
        .Produces(StatusCodes.Status204NoContent)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status500InternalServerError);

        // DELETE /api/rule-engine/rules/{id} - Smaže pravidlo
        group.MapDelete("/rules/{id:guid}", async (
            Guid id,
            [FromServices] IRuleRepository repository,
            [FromServices] CalculationEngine engine) =>
        {
            try
            {
                var rule = await repository.GetByIdAsync(id);
                if (rule == null)
                {
                    return Results.NotFound($"Pravidlo s ID {id} nebylo nalezeno.");
                }

                await repository.DeleteAsync(id);
                
                // Vymazání z cache
                await engine.InvalidateRuleAsync(id);
                
                return Results.NoContent();
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při mazání pravidla: {ex.Message}");
            }
        })
        .WithName("DeleteRule")
        .WithSummary("Smaže obchodní pravidlo")
        .WithDescription("Smaže existující obchodní pravidlo ze systému")
        .Produces(StatusCodes.Status204NoContent)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status500InternalServerError);

        // GET /api/rule-engine/metadata - Získá metadata pro tvorbu pravidel
        group.MapGet("/metadata", (
            [FromServices] IEntityMetadataService metadataService) =>
        {
            try
            {
                var metadata = new RuleMetadata
                {
                    AvailableEntities = metadataService.GetAvailableEntities(),
                    AvailableOperators = GetAvailableOperators(),
                    AvailableAggregations = GetAvailableAggregations(),
                    AvailableValueTypes = GetAvailableValueTypes(),
                    SchemaVersion = "1.0"
                };
                return Results.Ok(metadata);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při načítání metadat: {ex.Message}");
            }
        })
        .WithName("GetMetadata")
        .WithSummary("Získá metadata pro tvorbu pravidel")
        .WithDescription("Vrací dostupné entity, operátory a další metadata potřebná pro vytváření pravidel")
        .Produces<RuleMetadata>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status500InternalServerError);

        // GET /api/rule-engine/entities/{entityName}/properties - Získá vlastnosti entity
        group.MapGet("/entities/{entityName}/properties", (
            string entityName,
            [FromServices] IEntityMetadataService metadataService) =>
        {
            try
            {
                if (!metadataService.IsEntitySupported(entityName))
                {
                    return Results.NotFound($"Entita '{entityName}' není podporována.");
                }

                var properties = metadataService.GetEntityProperties(entityName);
                return Results.Ok(properties);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při načítání vlastností entity: {ex.Message}");
            }
        })
        .WithName("GetEntityProperties")
        .WithSummary("Získá vlastnosti konkrétní entity")
        .WithDescription("Vrací seznam vlastností zadané entity včetně jejich typů a popisů")
        .Produces<IEnumerable<PropertyMetadata>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status500InternalServerError);

        return app;
    }

    // Helper metody pro metadata

    private static List<OperatorMetadata> GetAvailableOperators()
    {
        return new List<OperatorMetadata>
        {
            new() { Value = "Add", DisplayName = "Sčítání (+)", Category = "Aritmetické", Description = "Sečte dva číselné operandy" },
            new() { Value = "Subtract", DisplayName = "Odčítání (-)", Category = "Aritmetické", Description = "Odečte druhý operand od prvního" },
            new() { Value = "Multiply", DisplayName = "Násobení (*)", Category = "Aritmetické", Description = "Vynásobí dva číselné operandy" },
            new() { Value = "Divide", DisplayName = "Dělení (/)", Category = "Aritmetické", Description = "Vydělí první operand druhým" },
            new() { Value = "Equal", DisplayName = "Rovná se (==)", Category = "Porovnání", Description = "Porovná dva operandy na rovnost" },
            new() { Value = "NotEqual", DisplayName = "Nerovná se (!=)", Category = "Porovnání", Description = "Porovná dva operandy na nerovnost" },
            new() { Value = "GreaterThan", DisplayName = "Větší než (>)", Category = "Porovnání", Description = "Ověří, zda je první operand větší než druhý" },
            new() { Value = "LessThan", DisplayName = "Menší než (<)", Category = "Porovnání", Description = "Ověří, zda je první operand menší než druhý" },
            new() { Value = "And", DisplayName = "Logické AND (&&)", Category = "Logické", Description = "Logický součin dvou boolean operandů" },
            new() { Value = "Or", DisplayName = "Logické OR (||)", Category = "Logické", Description = "Logický součet dvou boolean operandů" },
            new() { Value = "Not", DisplayName = "Logické NOT (!)", Category = "Logické", Description = "Logická negace boolean operandu" },
            new() { Value = "If", DisplayName = "Podmínka (IF-THEN-ELSE)", Category = "Podmíněné", Description = "Podmíněné vyhodnocení: IF podmínka THEN hodnota1 ELSE hodnota2" }
        };
    }

    private static List<AggregationMetadata> GetAvailableAggregations()
    {
        return new List<AggregationMetadata>
        {
            new() { Value = "Sum", DisplayName = "Součet", Description = "Sečte hodnoty zadaného pole", RequiresField = true },
            new() { Value = "Count", DisplayName = "Počet", Description = "Spočítá počet záznamů", RequiresField = false },
            new() { Value = "Average", DisplayName = "Průměr", Description = "Vypočítá průměr hodnot zadaného pole", RequiresField = true },
            new() { Value = "Min", DisplayName = "Minimum", Description = "Najde nejmenší hodnotu zadaného pole", RequiresField = true },
            new() { Value = "Max", DisplayName = "Maximum", Description = "Najde největší hodnotu zadaného pole", RequiresField = true }
        };
    }

    private static List<ValueTypeMetadata> GetAvailableValueTypes()
    {
        return new List<ValueTypeMetadata>
        {
            new() { Value = "String", DisplayName = "Text", Description = "Textová hodnota (např. \"Hello World\")" },
            new() { Value = "Integer", DisplayName = "Celé číslo", Description = "Celé číslo (např. 42, -10)" },
            new() { Value = "Decimal", DisplayName = "Desetinné číslo", Description = "Desetinné číslo (např. 3.14, -2.5)" },
            new() { Value = "Boolean", DisplayName = "Pravdivostní hodnota", Description = "Pravda nebo nepravda (true/false)" },
            new() { Value = "DateTime", DisplayName = "Datum a čas", Description = "Datum a čas (např. 2024-01-15 10:30:00)" },
            new() { Value = "Guid", DisplayName = "Jedinečný identifikátor", Description = "GUID identifikátor (např. 123e4567-e89b-12d3-a456-************)" }
        };
    }


}
