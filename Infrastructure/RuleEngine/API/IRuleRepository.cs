using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Infrastructure.RuleEngine.API;

/// <summary>
/// Rozhraní pro repository obchodn<PERSON>ch pravidel.
/// </summary>
public interface IRuleRepository
{
    /// <summary>
    /// Získá všechna pravidla.
    /// </summary>
    Task<IEnumerable<BusinessRule>> GetAllAsync();

    /// <summary>
    /// Získá pravidlo podle ID.
    /// </summary>
    Task<BusinessRule?> GetByIdAsync(Guid id);

    /// <summary>
    /// Přidá nové pravidlo.
    /// </summary>
    Task AddAsync(BusinessRule rule);

    /// <summary>
    /// Aktualizuje existující pravidlo.
    /// </summary>
    Task UpdateAsync(BusinessRule rule);

    /// <summary>
    /// Smaže pravidlo podle ID.
    /// </summary>
    Task DeleteAsync(Guid id);

    /// <summary>
    /// Získá aktivní pravidla pro konkrétní entitu.
    /// </summary>
    Task<IEnumerable<BusinessRule>> GetActiveRulesForEntityAsync(string entityName);

    /// <summary>
    /// Ověří, zda existuje pravidlo s daným názvem.
    /// </summary>
    Task<bool> ExistsWithNameAsync(string name, Guid? excludeId = null);
}
