using Application.Abstraction;
using Infrastructure.RuleEngine.API;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.RuleEngine;

/// <summary>
/// Implementace IRuleRepository pro správu obchodních pravidel v databázi.
/// Poskytuje CRUD operace pro BusinessRule entity s podporou cachování.
/// </summary>
public class RuleRepository : IRuleRepository
{
    private readonly IApplicationDbContext _context;
    private readonly IRuleCacheService _cacheService;

    /// <summary>
    /// Inicializuje novou instanci RuleRepository.
    /// </summary>
    /// <param name="context">Databázový kontext</param>
    /// <param name="cacheService">Cache služba pro Rule Engine</param>
    public RuleRepository(IApplicationDbContext context, IRuleCacheService cacheService)
    {
        _context = context;
        _cacheService = cacheService;
    }

    /// <summary>
    /// <PERSON>ísk<PERSON> všechna obchodní pravidla s cachováním.
    /// </summary>
    /// <returns>Kolekce všech pravidel</returns>
    public async Task<IEnumerable<BusinessRule>> GetAllAsync()
    {
        const string cacheKey = "rules:all";

        return await _cacheService.GetOrSetAsync(cacheKey, async () =>
        {
            return await _context.Set<BusinessRule>()
                .Where(r => r.IsActive)
                .OrderBy(r => r.Name)
                .ToListAsync();
        }, TimeSpan.FromMinutes(15));
    }

    /// <summary>
    /// Získá obchodní pravidlo podle ID s cachováním.
    /// </summary>
    /// <param name="id">ID pravidla</param>
    /// <returns>Nalezené pravidlo nebo null</returns>
    public async Task<BusinessRule?> GetByIdAsync(Guid id)
    {
        var cacheKey = $"rules:id:{id}";

        return await _cacheService.GetOrSetAsync(cacheKey, async () =>
        {
            return await _context.Set<BusinessRule>()
                .FirstOrDefaultAsync(r => r.Id == id);
        }, TimeSpan.FromMinutes(30));
    }

    /// <summary>
    /// Přidá nové obchodní pravidlo a invaliduje související cache.
    /// </summary>
    /// <param name="rule">Pravidlo k přidání</param>
    public async Task AddAsync(BusinessRule rule)
    {
        if (rule == null)
            throw new ArgumentNullException(nameof(rule));

        // Nastavíme ID pokud není nastaveno
        if (rule.Id == Guid.Empty)
            rule.Id = Guid.NewGuid();

        _context.Set<BusinessRule>().Add(rule);
        await _context.SaveChangesAsync(CancellationToken.None);

        // Invalidujeme cache
        await InvalidateRuleCacheAsync(rule);
    }

    /// <summary>
    /// Aktualizuje existující obchodní pravidlo a invaliduje související cache.
    /// </summary>
    /// <param name="rule">Pravidlo k aktualizaci</param>
    public async Task UpdateAsync(BusinessRule rule)
    {
        if (rule == null)
            throw new ArgumentNullException(nameof(rule));

        var existingRule = await _context.Set<BusinessRule>()
            .FirstOrDefaultAsync(r => r.Id == rule.Id);

        if (existingRule == null)
            throw new InvalidOperationException($"Pravidlo s ID {rule.Id} nebylo nalezeno.");

        // Uložíme původní entity name pro invalidaci cache
        var originalEntityName = existingRule.TargetEntityName;

        // Aktualizujeme vlastnosti
        existingRule.Name = rule.Name;
        existingRule.Description = rule.Description;
        existingRule.TargetEntityName = rule.TargetEntityName;
        existingRule.TargetProperty = rule.TargetProperty;
        existingRule.SchemaVersion = rule.SchemaVersion;
        existingRule.RootNode = rule.RootNode;
        existingRule.IsActive = rule.IsActive;
        existingRule.InternalNotes = rule.InternalNotes;

        // UpdatedAt se nastavuje automaticky v TrackableEntityInterceptor
        await _context.SaveChangesAsync(CancellationToken.None);

        // Invalidujeme cache pro původní i novou entitu
        await InvalidateRuleCacheAsync(existingRule);
        if (originalEntityName != existingRule.TargetEntityName)
        {
            await _cacheService.InvalidateAsync($"rules:entity:{originalEntityName}");
        }
    }

    /// <summary>
    /// Smaže obchodní pravidlo podle ID a invaliduje související cache.
    /// </summary>
    /// <param name="id">ID pravidla ke smazání</param>
    public async Task DeleteAsync(Guid id)
    {
        var rule = await _context.Set<BusinessRule>()
            .FirstOrDefaultAsync(r => r.Id == id);

        if (rule == null)
            throw new InvalidOperationException($"Pravidlo s ID {id} nebylo nalezeno.");

        _context.Set<BusinessRule>().Remove(rule);
        await _context.SaveChangesAsync(CancellationToken.None);

        // Invalidujeme cache
        await InvalidateRuleCacheAsync(rule);
    }

    /// <summary>
    /// Získá aktivní pravidla pro konkrétní entitu s cachováním.
    /// </summary>
    /// <param name="entityName">Název cílové entity</param>
    /// <returns>Kolekce aktivních pravidel pro entitu</returns>
    public async Task<IEnumerable<BusinessRule>> GetActiveRulesForEntityAsync(string entityName)
    {
        if (string.IsNullOrWhiteSpace(entityName))
            throw new ArgumentException("Název entity nesmí být prázdný.", nameof(entityName));

        var cacheKey = $"rules:entity:{entityName}";

        return await _cacheService.GetOrSetAsync(cacheKey, async () =>
        {
            return await _context.Set<BusinessRule>()
                .Where(r => r.IsActive && r.TargetEntityName == entityName)
                .OrderBy(r => r.Name)
                .ToListAsync();
        }, TimeSpan.FromMinutes(20));
    }

    /// <summary>
    /// Ověří, zda existuje pravidlo se zadaným názvem (pro validaci duplicit).
    /// </summary>
    /// <param name="name">Název pravidla</param>
    /// <param name="excludeId">ID pravidla k vyloučení z kontroly (pro update)</param>
    /// <returns>True pokud pravidlo s názvem existuje</returns>
    public async Task<bool> ExistsWithNameAsync(string name, Guid? excludeId = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            return false;

        var query = _context.Set<BusinessRule>().Where(r => r.Name == name);

        if (excludeId.HasValue)
            query = query.Where(r => r.Id != excludeId.Value);

        return await query.AnyAsync();
    }

    /// <summary>
    /// Invaliduje cache záznamy související s pravidlem.
    /// </summary>
    /// <param name="rule">Pravidlo pro invalidaci cache</param>
    private async Task InvalidateRuleCacheAsync(BusinessRule rule)
    {
        // Invalidujeme všechny cache záznamy pro toto pravidlo
        await _cacheService.InvalidateAsync($"*:{rule.Id}:*");

        // Invalidujeme cache pro konkrétní entitu
        await _cacheService.InvalidateAsync($"rules:entity:{rule.TargetEntityName}");

        // Invalidujeme obecné cache
        await _cacheService.InvalidateAsync("rules:all");
    }
}
