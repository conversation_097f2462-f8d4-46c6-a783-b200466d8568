using System.Globalization;
using System.Linq.Expressions;

namespace Infrastructure.RuleEngine.Builders;

/// <summary>
/// Implementace builderu konstantních výrazů.
/// Zodpovídá za konverzi konstantních hodnot na Expression objekty.
/// </summary>
public class ConstantExpressionBuilder : IConstantExpressionBuilder
{
    /// <summary>
    /// Sestaví Expression z konstantního uzlu.
    /// </summary>
    /// <param name="node">Konstantní uzel s hodnotou a datovým typem</param>
    /// <returns>Expression reprezentující konstantní hodnotu</returns>
    /// <exception cref="ArgumentNullException">Pokud je node null</exception>
    public Expression BuildConstant(ConstantNode node)
    {
        if (node == null) throw new ArgumentNullException(nameof(node));

        object value = node.DataType switch
        {
            ValueType.Integer => int.Parse(node.Value, CultureInfo.InvariantCulture),
            ValueType.Decimal => decimal.Parse(node.Value, CultureInfo.InvariantCulture),
            ValueType.Boolean => bool.Parse(node.Value),
            ValueType.DateTime => DateTime.Parse(node.Value, CultureInfo.InvariantCulture),
            ValueType.Guid => Guid.Parse(node.Value),
            _ => node.Value
        };
        
        return Expression.Constant(value, value.GetType());
    }
}
