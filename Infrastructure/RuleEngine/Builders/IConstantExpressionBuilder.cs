using System.Linq.Expressions;

namespace Infrastructure.RuleEngine.Builders;

/// <summary>
/// Rozhraní pro builder konstantních výrazů.
/// Zodpovídá za vytváření Expression objektů z konstantních hodnot.
/// </summary>
public interface IConstantExpressionBuilder
{
    /// <summary>
    /// Sestaví Expression z konstantního uzlu.
    /// </summary>
    /// <param name="node">Konstantní uzel s hodnotou a datovým typem</param>
    /// <returns>Expression reprezentující konstantní hodnotu</returns>
    Expression BuildConstant(ConstantNode node);
}
