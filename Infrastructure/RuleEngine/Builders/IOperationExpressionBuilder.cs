using System.Linq.Expressions;

namespace Infrastructure.RuleEngine.Builders;

/// <summary>
/// Rozhraní pro builder operačních výrazů.
/// Zodpovídá za vytváření Expression objektů pro aritmetické, logické a podmíněné operace.
/// </summary>
public interface IOperationExpressionBuilder
{
    /// <summary>
    /// Sestaví Expression z operačního uzlu.
    /// </summary>
    /// <param name="node">Operační uzel s operátorem a operandy</param>
    /// <param name="param">Parametr reprezentující vstupní entitu</param>
    /// <param name="buildChild">Funkce pro sestavení podřízených uzlů</param>
    /// <returns>Expression reprezentující operaci</returns>
    Expression BuildOperation(OperationNode node, ParameterExpression param, Func<RuleNode, ParameterExpression, Expression> buildChild);
}
