using System.Linq.Expressions;

namespace Infrastructure.RuleEngine.Builders;

/// <summary>
/// Rozhraní pro builder agregačních výrazů.
/// Zodpovídá za vytváření Expression objektů pro agregační operace (Sum, Count, Average, atd.).
/// </summary>
public interface IAggregationExpressionBuilder
{
    /// <summary>
    /// Sestaví Expression z agregačního uzlu.
    /// </summary>
    /// <param name="node">Agregační uzel s typem agregace a cestou ke kolekci</param>
    /// <param name="param">Parametr reprezentující vstupní entitu</param>
    /// <param name="buildChild">Funkce pro sestavení podřízených uzlů</param>
    /// <returns>Expression reprezentující agregační operaci</returns>
    Expression BuildAggregation(AggregationNode node, ParameterExpression param, Func<RuleNode, ParameterExpression, Expression> buildChild);

    /// <summary>
    /// Sestaví Expression z uzlu související agregace.
    /// </summary>
    /// <param name="node">Uzel související agregace přes IRuleDataProvider</param>
    /// <param name="param">Parametr reprezentující vstupní entitu</param>
    /// <param name="buildChild">Funkce pro sestavení podřízených uzlů</param>
    /// <returns>Expression reprezentující související agregační operaci</returns>
    Expression BuildRelatedAggregation(RelatedAggregationNode node, ParameterExpression param, Func<RuleNode, ParameterExpression, Expression> buildChild);
}
