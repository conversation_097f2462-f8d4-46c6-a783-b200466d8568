using System.Linq.Expressions;

namespace Infrastructure.RuleEngine.Builders;

/// <summary>
/// Implementace builderu agregačních výrazů.
/// Zodpovídá za vytváření Expression objektů pro agregační operace (Sum, Count, Average, atd.).
/// </summary>
public class AggregationExpressionBuilder : IAggregationExpressionBuilder
{
    private readonly IRuleDataProvider _dataProvider;
    private readonly IReadOnlyDictionary<string, Type> _entityTypeMap;

    /// <summary>
    /// Inicializuje novou instanci AggregationExpressionBuilder.
    /// </summary>
    /// <param name="dataProvider">Poskytovatel dat pro související agregace</param>
    /// <param name="entityTypeMap">Mapa názvů entit na jejich typy</param>
    public AggregationExpressionBuilder(IRuleDataProvider dataProvider, IReadOnlyDictionary<string, Type> entityTypeMap)
    {
        _dataProvider = dataProvider ?? throw new ArgumentNullException(nameof(dataProvider));
        _entityTypeMap = entityTypeMap ?? throw new ArgumentNullException(nameof(entityTypeMap));
    }

    /// <summary>
    /// Sestaví Expression z agregačního uzlu.
    /// </summary>
    /// <param name="node">Agregační uzel s typem agregace a cestou ke kolekci</param>
    /// <param name="param">Parametr reprezentující vstupní entitu</param>
    /// <param name="buildChild">Funkce pro sestavení podřízených uzlů</param>
    /// <returns>Expression reprezentující agregační operaci</returns>
    /// <exception cref="ArgumentNullException">Pokud je některý z parametrů null</exception>
    public Expression BuildAggregation(AggregationNode node, ParameterExpression param, Func<RuleNode, ParameterExpression, Expression> buildChild)
    {
        if (node == null) throw new ArgumentNullException(nameof(node));
        if (param == null) throw new ArgumentNullException(nameof(param));
        if (buildChild == null) throw new ArgumentNullException(nameof(buildChild));

        Expression collection = param;
        foreach (var part in node.CollectionPath.Split('.'))
            collection = Expression.PropertyOrField(collection, part);

        var elementType = collection.Type.GetGenericArguments().First();
        var lambdaParam = Expression.Parameter(elementType, "x");

        // Apply filter if any
        Expression filtered = collection;
        if (node.Filter != null)
        {
            var filterBody = buildChild(node.Filter, lambdaParam);
            var predicate = Expression.Lambda(filterBody, lambdaParam);
            var providerType = typeof(Enumerable);
            var whereMethod = providerType
                .GetMethods()
                .First(m => m.Name == "Where" && m.GetParameters().Length == 2)
                .MakeGenericMethod(elementType);
            filtered = Expression.Call(whereMethod, collection, predicate);
        }

        // Build selector
        Expression selector = lambdaParam;
        if (!string.IsNullOrEmpty(node.FieldPath))
        {
            selector = node.FieldPath.Split('.')
                .Aggregate((Expression)lambdaParam,
                    (expr, part) => Expression.PropertyOrField(expr, part));
        }
        var selectorLambda = Expression.Lambda(selector, lambdaParam);

        // Perform aggregation
        var providerAggType = typeof(Enumerable);
        MethodCallExpression call;
        if (node.AggregationType == AggregationType.Count)
        {
            var countMethod = providerAggType
                .GetMethods()
                .First(m => m.Name == "Count" && m.GetParameters().Length == 1)
                .MakeGenericMethod(elementType);
            call = Expression.Call(countMethod, filtered);
        }
        else
        {
            var methodName = node.AggregationType.ToString(); // Sum, Average, Min, Max
            var aggMethod = providerAggType
                .GetMethods()
                .First(m => m.Name == methodName &&
                           m.GetParameters().Length == 2)
                .MakeGenericMethod(elementType);
            call = Expression.Call(aggMethod, filtered, selectorLambda);
        }
        return call;
    }

    /// <summary>
    /// Sestaví Expression z uzlu související agregace.
    /// Builds expression for related aggregation using IRuleDataProvider.FindMany
    /// </summary>
    /// <param name="node">Uzel související agregace přes IRuleDataProvider</param>
    /// <param name="param">Parametr reprezentující vstupní entitu</param>
    /// <param name="buildChild">Funkce pro sestavení podřízených uzlů</param>
    /// <returns>Expression reprezentující související agregační operaci</returns>
    /// <exception cref="ArgumentNullException">Pokud je některý z parametrů null</exception>
    /// <exception cref="InvalidOperationException">Pokud cílová entita není nalezena nebo agregační metoda není podporována</exception>
    public Expression BuildRelatedAggregation(RelatedAggregationNode node, ParameterExpression param, Func<RuleNode, ParameterExpression, Expression> buildChild)
    {
        if (node == null) throw new ArgumentNullException(nameof(node));
        if (param == null) throw new ArgumentNullException(nameof(param));
        if (buildChild == null) throw new ArgumentNullException(nameof(buildChild));

        if (!_entityTypeMap.TryGetValue(node.TargetEntityName, out var targetType))
        {
            throw new InvalidOperationException($"Neznámý typ entity: {node.TargetEntityName}");
        }

        // Retrieve relationship key from source entity
        var keyExpr = Expression.PropertyOrField(param, node.RelationshipProperty);

        // Build predicate: e => e.RelationshipProperty == key
        var itemParam = Expression.Parameter(targetType, "e");
        var left = Expression.Equal(
            Expression.PropertyOrField(itemParam, node.RelationshipProperty),
            keyExpr
        );
        Expression whereBody = left;
        if (node.FilterCondition != null)
        {
            var extra = buildChild(node.FilterCondition, itemParam);
            whereBody = Expression.AndAlso(left, Expression.Convert(extra, typeof(bool)));
        }
        var predicate = Expression.Lambda(whereBody, itemParam);

        // Call dataProvider.FindMany(targetEntity, predicate)
        var providerExpr = Expression.Constant(_dataProvider);
        var findManyMethod = typeof(IRuleDataProvider).GetMethod(nameof(IRuleDataProvider.FindMany));
        var findManyCall = Expression.Call(
            providerExpr,
            findManyMethod!,
            Expression.Constant(node.TargetEntityName),
            Expression.Quote(predicate) // Quote the lambda expression
        );

        // Cast IEnumerable<object> to IEnumerable<targetType>
        var castMethod = typeof(Enumerable)
            .GetMethod(nameof(Enumerable.Cast))!
            .MakeGenericMethod(targetType);
        var castedCollection = Expression.Call(castMethod, findManyCall);

        return BuildAggregationOverCollection(node, castedCollection, targetType);
    }

    /// <summary>
    /// Sestaví agregační operaci nad kolekcí.
    /// </summary>
    /// <param name="node">Uzel související agregace</param>
    /// <param name="collection">Expression reprezentující kolekci</param>
    /// <param name="elementType">Typ prvků kolekce</param>
    /// <returns>Expression reprezentující agregační operaci</returns>
    private Expression BuildAggregationOverCollection(RelatedAggregationNode node, Expression collection, Type elementType)
    {
        var lambdaParam = Expression.Parameter(elementType, "x");

        // Build selector for aggregation field
        Expression selector = lambdaParam;
        Type selectorType = elementType;

        if (!string.IsNullOrEmpty(node.AggregationField))
        {
            selector = node.AggregationField.Split('.')
                .Aggregate((Expression)lambdaParam, (expr, part) => Expression.PropertyOrField(expr, part));
            selectorType = selector.Type;
        }

        // Handle Count aggregation specially (doesn't need selector type matching)
        if (node.AggregationType == AggregationType.Count)
        {
            // Count with predicate - selector should return bool
            if (!string.IsNullOrEmpty(node.AggregationField))
            {
                // For Count, we need a predicate that returns bool
                // This is likely a filter condition, not a field selector
                var selectorLambda = Expression.Lambda(selector, lambdaParam);
                var countMethod = typeof(Enumerable)
                    .GetMethods()
                    .First(m => m.Name == "Count" && m.GetParameters().Length == 2)
                    .MakeGenericMethod(elementType);
                return Expression.Call(countMethod, collection, selectorLambda);
            }
            else
            {
                // Simple count without predicate
                var countMethod = typeof(Enumerable)
                    .GetMethods()
                    .First(m => m.Name == "Count" && m.GetParameters().Length == 1)
                    .MakeGenericMethod(elementType);
                return Expression.Call(countMethod, collection);
            }
        }

        // For other aggregations (Sum, Average, etc.), find the right method
        var selectorLambda2 = Expression.Lambda(selector, lambdaParam);
        var methodName = node.AggregationType.ToString();

        // Find the aggregation method that matches the selector type
        var aggMethod = typeof(Enumerable)
            .GetMethods()
            .Where(m => m.Name == methodName && m.GetParameters().Length == 2)
            .FirstOrDefault(m =>
            {
                var genericMethod = m.MakeGenericMethod(elementType);
                var paramType = genericMethod.GetParameters()[1].ParameterType;
                var expectedSelectorType = typeof(Func<,>).MakeGenericType(elementType, selectorType);
                return paramType.IsAssignableFrom(expectedSelectorType);
            });

        if (aggMethod == null)
        {
            throw new InvalidOperationException($"Nelze najít agregační metodu {methodName} pro typ {selectorType.Name}");
        }

        var genericAggMethod = aggMethod.MakeGenericMethod(elementType);
        return Expression.Call(genericAggMethod, collection, selectorLambda2);
    }
}
