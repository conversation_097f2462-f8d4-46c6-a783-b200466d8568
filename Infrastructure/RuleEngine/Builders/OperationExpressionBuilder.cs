using System.Linq.Expressions;

namespace Infrastructure.RuleEngine.Builders;

/// <summary>
/// Implementace builderu operačních výrazů.
/// Zodpovídá za vytváření Expression objektů pro aritmetické, logické a podmíněné operace.
/// </summary>
public class OperationExpressionBuilder : IOperationExpressionBuilder
{
    /// <summary>
    /// Sestaví Expression z operačního uzlu.
    /// </summary>
    /// <param name="node">Operační uzel s operátorem a operandy</param>
    /// <param name="param">Parametr reprezentující vstupní entitu</param>
    /// <param name="buildChild">Funkce pro sestavení podřízených uzlů</param>
    /// <returns>Expression reprezentující operaci</returns>
    /// <exception cref="ArgumentNullException">Pokud je některý z parametrů null</exception>
    /// <exception cref="NotSupportedException">Pokud operátor nen<PERSON></exception>
    public Expression BuildOperation(OperationNode node, ParameterExpression param, Func<RuleNode, ParameterExpression, Expression> buildChild)
    {
        if (node == null) throw new ArgumentNullException(nameof(node));
        if (param == null) throw new ArgumentNullException(nameof(param));
        if (buildChild == null) throw new ArgumentNullException(nameof(buildChild));

        var children = node.Operands.Select(n => buildChild(n, param)).ToList();
        
        return node.Operator switch
        {
            OperatorType.Add => Expression.Add(children[0], children[1]),
            OperatorType.Subtract => Expression.Subtract(children[0], children[1]),
            OperatorType.Multiply => Expression.Multiply(children[0], children[1]),
            OperatorType.Divide => Expression.Divide(children[0], children[1]),
            OperatorType.Equal => Expression.Equal(children[0], children[1]),
            OperatorType.NotEqual => Expression.NotEqual(children[0], children[1]),
            OperatorType.GreaterThan => Expression.GreaterThan(children[0], children[1]),
            OperatorType.LessThan => Expression.LessThan(children[0], children[1]),
            OperatorType.GreaterThanOrEqual => Expression.GreaterThanOrEqual(children[0], children[1]),
            OperatorType.LessThanOrEqual => Expression.LessThanOrEqual(children[0], children[1]),
            OperatorType.And => Expression.AndAlso(AsBool(children[0]), AsBool(children[1])),
            OperatorType.Or => Expression.OrElse(AsBool(children[0]), AsBool(children[1])),
            OperatorType.Not => Expression.Not(AsBool(children[0])),
            OperatorType.If => Expression.Condition(
                AsBool(children[0]),
                Expression.Convert(children[1], children[2].Type),
                children[2]),
            _ => throw new NotSupportedException($"Operator not supported: {node.Operator}")
        };
    }

    /// <summary>
    /// Převede výraz na boolean typ pro logické operace.
    /// </summary>
    /// <param name="expression">Výraz k převodu</param>
    /// <returns>Boolean výraz</returns>
    private static Expression AsBool(Expression expression) =>
        expression.Type == typeof(bool) ? expression : Expression.NotEqual(expression, Expression.Constant(null, expression.Type));
}
