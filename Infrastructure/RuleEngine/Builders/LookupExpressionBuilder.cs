using System.Linq.Expressions;

namespace Infrastructure.RuleEngine.Builders;

/// <summary>
/// Implementace builderu lookup výrazů.
/// Zodpovídá za vytváření Expression objektů pro lookup operace přes IRuleDataProvider.
/// </summary>
public class LookupExpressionBuilder : ILookupExpressionBuilder
{
    private readonly IRuleDataProvider _dataProvider;
    private readonly IReadOnlyDictionary<string, Type> _entityTypeMap;

    /// <summary>
    /// Inicializuje novou instanci LookupExpressionBuilder.
    /// </summary>
    /// <param name="dataProvider">Poskytovatel dat pro lookup operace</param>
    /// <param name="entityTypeMap">Mapa názvů entit na jejich typy</param>
    public LookupExpressionBuilder(IRuleDataProvider dataProvider, IReadOnlyDictionary<string, Type> entityTypeMap)
    {
        _dataProvider = dataProvider ?? throw new ArgumentNullException(nameof(dataProvider));
        _entityTypeMap = entityTypeMap ?? throw new ArgumentNullException(nameof(entityTypeMap));
    }

    /// <summary>
    /// Sestaví Expression z lookup uzlu.
    /// </summary>
    /// <param name="node">Lookup uzel s podmínkou a cílovou entitou</param>
    /// <param name="param">Parametr reprezentující vstupní entitu</param>
    /// <param name="buildChild">Funkce pro sestavení podřízených uzlů</param>
    /// <returns>Expression reprezentující lookup operaci</returns>
    /// <exception cref="ArgumentNullException">Pokud je některý z parametrů null</exception>
    /// <exception cref="InvalidOperationException">Pokud cílová entita není nalezena v mapě typů</exception>
    public Expression BuildLookup(LookupNode node, ParameterExpression param, Func<RuleNode, ParameterExpression, Expression> buildChild)
    {
        if (node == null) throw new ArgumentNullException(nameof(node));
        if (param == null) throw new ArgumentNullException(nameof(param));
        if (buildChild == null) throw new ArgumentNullException(nameof(buildChild));

        if (!_entityTypeMap.TryGetValue(node.TargetEntityName, out var entityType))
        {
            throw new InvalidOperationException($"Neznámý typ entity: {node.TargetEntityName}");
        }

        var lambdaParam = Expression.Parameter(entityType, "e");
        var body = buildChild(node.Condition, lambdaParam);
        var predicate = Expression.Lambda(body, lambdaParam);
        var providerExpr = Expression.Constant(_dataProvider);
        
        var call = Expression.Call(
            providerExpr,
            nameof(IRuleDataProvider.FindSingle),
            Type.EmptyTypes,
            Expression.Constant(node.TargetEntityName),
            predicate
        );
        
        var casted = Expression.Convert(call, entityType);
        return Expression.PropertyOrField(casted, node.ReturnFieldPath);
    }
}
