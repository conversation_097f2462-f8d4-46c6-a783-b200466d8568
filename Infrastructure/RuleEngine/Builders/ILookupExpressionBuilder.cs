using System.Linq.Expressions;

namespace Infrastructure.RuleEngine.Builders;

/// <summary>
/// Rozhraní pro builder lookup výrazů.
/// Zodpovídá za vytváření Expression objektů pro lookup operace přes IRuleDataProvider.
/// </summary>
public interface ILookupExpressionBuilder
{
    /// <summary>
    /// Sestaví Expression z lookup uzlu.
    /// </summary>
    /// <param name="node">Lookup uzel s podmínkou a cílovou entitou</param>
    /// <param name="param">Parametr reprezentující vstupní entitu</param>
    /// <param name="buildChild">Funkce pro sestavení podřízených uzlů</param>
    /// <returns>Expression reprezentující lookup operaci</returns>
    Expression BuildLookup(LookupNode node, ParameterExpression param, Func<RuleNode, ParameterExpression, Expression> buildChild);
}
