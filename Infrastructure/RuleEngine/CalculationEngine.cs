using System.Linq.Expressions;
using Infrastructure.RuleEngine.Exceptions;
using Infrastructure.RuleEngine.Services;
using Infrastructure.RuleEngine.Validation;
using Microsoft.Extensions.Logging;

namespace Infrastructure.RuleEngine;

/// <summary>
/// Zjednodušený engine pro vykonávání obchodních pravidel.
/// Zaměřený na jednoduchost a spolehlivost pro technické API.
/// Používá IRuleCacheService pro pokročilé cachování.
/// </summary>
public class CalculationEngine
{
    private readonly IExpressionBuilder _builder;
    private readonly IReadOnlyDictionary<string, Type> _entityTypeMap;
    private readonly IRuleCacheService _cacheService;
    private readonly ILogger<CalculationEngine> _logger;

    /// <summary>
    /// Konstruktor pro zpětnou kompatibilitu (bez cache služby).
    /// </summary>
    public CalculationEngine(
        IExpressionBuilder builder,
        IReadOnlyDictionary<string, Type> entityTypeMap,
        ILogger<CalculationEngine> logger)
        : this(builder, entityTypeMap, new RuleCacheService(
            new Microsoft.Extensions.Logging.Abstractions.NullLogger<RuleCacheService>()), logger)
    {
    }

    /// <summary>
    /// Hlavní konstruktor s cache službou.
    /// </summary>
    public CalculationEngine(
        IExpressionBuilder builder,
        IReadOnlyDictionary<string, Type> entityTypeMap,
        IRuleCacheService cacheService,
        ILogger<CalculationEngine> logger)
    {
        _builder = builder ?? throw new ArgumentNullException(nameof(builder));
        _entityTypeMap = entityTypeMap ?? throw new ArgumentNullException(nameof(entityTypeMap));
        _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Vykoná obchodní pravidlo na zadané entitě (synchronní verze pro zpětnou kompatibilitu).
    /// </summary>
    /// <param name="rule">Pravidlo k vykonání</param>
    /// <param name="entity">Entita pro vyhodnocení</param>
    /// <returns>Výsledek vyhodnocení pravidla</returns>
    /// <exception cref="ArgumentNullException">Pokud rule nebo entity je null</exception>
    /// <exception cref="RuleExecutionException">Pokud dojde k chybě při vykonávání pravidla</exception>
    public object Execute(BusinessRule rule, object entity)
    {
        return ExecuteAsync(rule, entity).GetAwaiter().GetResult();
    }

    /// <summary>
    /// Vykoná obchodní pravidlo na zadané entitě (asynchronní verze).
    /// </summary>
    /// <param name="rule">Pravidlo k vykonání</param>
    /// <param name="entity">Entita pro vyhodnocení</param>
    /// <returns>Výsledek vyhodnocení pravidla</returns>
    /// <exception cref="ArgumentNullException">Pokud rule nebo entity je null</exception>
    /// <exception cref="RuleExecutionException">Pokud dojde k chybě při vykonávání pravidla</exception>
    public async Task<object> ExecuteAsync(BusinessRule rule, object entity)
    {
        if (rule == null) throw new ArgumentNullException(nameof(rule));
        if (entity == null) throw new ArgumentNullException(nameof(entity));

        _logger.LogDebug("Spouštím vykonávání pravidla '{RuleName}' (ID: {RuleId}) na entitě typu {EntityType}",
            rule.Name, rule.Id, entity.GetType().Name);

        try
        {
            var startTime = DateTime.UtcNow;
            var compiledRule = await GetOrCompileRuleAsync(rule);
            var result = compiledRule(entity);
            var executionTime = DateTime.UtcNow - startTime;

            _logger.LogDebug("Pravidlo '{RuleName}' úspěšně vykonáno za {ExecutionTime}ms. Výsledek: {Result}",
                rule.Name, executionTime.TotalMilliseconds, result);

            return result;
        }
        catch (Exception ex) when (!(ex is ArgumentNullException))
        {
            _logger.LogError(ex, "Chyba při vykonávání pravidla '{RuleName}' (ID: {RuleId}): {ErrorMessage}",
                rule.Name, rule.Id, ex.Message);

            throw new RuleExecutionException(
                $"Chyba při vykonávání pravidla '{rule.Name}' (ID: {rule.Id}): {ex.Message}",
                ex);
        }
    }

    /// <summary>
    /// Validuje syntaktickou správnost pravidla bez jeho vykonání (synchronní verze pro zpětnou kompatibilitu).
    /// </summary>
    /// <param name="rule">Pravidlo k validaci</param>
    /// <returns>Výsledek validace</returns>
    public RuleValidationResult ValidateRule(BusinessRule rule)
    {
        return ValidateRuleAsync(rule).GetAwaiter().GetResult();
    }

    /// <summary>
    /// Validuje syntaktickou správnost pravidla bez jeho vykonání (asynchronní verze).
    /// Používá cache pro uložení výsledků validace.
    /// </summary>
    /// <param name="rule">Pravidlo k validaci</param>
    /// <returns>Výsledek validace</returns>
    public async Task<RuleValidationResult> ValidateRuleAsync(BusinessRule rule)
    {
        _logger.LogDebug("Validuji pravidlo '{RuleName}' (ID: {RuleId})",
            rule?.Name ?? "null", rule?.Id ?? Guid.Empty);

        if (rule == null)
        {
            _logger.LogWarning("Pokus o validaci null pravidla");
            return RuleValidationResult.Invalid("Pravidlo nesmí být null.");
        }

        if (rule.RootNode == null)
        {
            _logger.LogWarning("Pravidlo '{RuleName}' nemá kořenový uzel", rule.Name);
            return RuleValidationResult.Invalid("Pravidlo musí obsahovat kořenový uzel.");
        }

        if (string.IsNullOrWhiteSpace(rule.TargetEntityName))
        {
            _logger.LogWarning("Pravidlo '{RuleName}' nemá definovanou cílovou entitu", rule.Name);
            return RuleValidationResult.Invalid("Pravidlo musí mít definovanou cílovou entitu.");
        }

        if (!_entityTypeMap.ContainsKey(rule.TargetEntityName))
        {
            _logger.LogWarning("Pravidlo '{RuleName}' odkazuje na neznámou entitu '{EntityName}'",
                rule.Name, rule.TargetEntityName);
            return RuleValidationResult.Invalid($"Neznámá cílová entita: {rule.TargetEntityName}");
        }

        // Použijeme cache pro validaci - klíč obsahuje hash pravidla
        var validationCacheKey = $"validation:{rule.Id}:{GetRuleHash(rule)}";

        return await _cacheService.GetOrSetAsync(validationCacheKey, () =>
        {
            try
            {
                // Pokus o kompilaci pro ověření syntaxe
                var startTime = DateTime.UtcNow;
                CompileRule(rule);
                var validationTime = DateTime.UtcNow - startTime;

                _logger.LogDebug("Pravidlo '{RuleName}' úspěšně validováno za {ValidationTime}ms",
                    rule.Name, validationTime.TotalMilliseconds);

                return RuleValidationResult.Valid();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Syntaktická chyba v pravidle '{RuleName}': {ErrorMessage}",
                    rule.Name, ex.Message);
                return RuleValidationResult.Invalid($"Syntaktická chyba v pravidle: {ex.Message}");
            }
        }, TimeSpan.FromMinutes(30)); // Validace se cachuje na 30 minut
    }

    private async Task<Func<object, object>> GetOrCompileRuleAsync(BusinessRule rule)
    {
        var compilationCacheKey = $"compiled:{rule.Id}:{GetRuleHash(rule)}";

        return await _cacheService.GetOrSetAsync(compilationCacheKey, () =>
        {
            _logger.LogDebug("Kompiluji pravidlo '{RuleName}' (ID: {RuleId}) - není v cache",
                rule.Name, rule.Id);

            var startTime = DateTime.UtcNow;
            var compiledRule = CompileRule(rule);
            var compilationTime = DateTime.UtcNow - startTime;

            _logger.LogDebug("Pravidlo '{RuleName}' zkompilováno za {CompilationTime}ms a uloženo do cache",
                rule.Name, compilationTime.TotalMilliseconds);

            return compiledRule;
        }, TimeSpan.FromHours(1)); // Zkompilovaná pravidla se cachují na 1 hodinu
    }

    /// <summary>
    /// Invaliduje všechny cache záznamy pro konkrétní pravidlo (synchronní verze pro zpětnou kompatibilitu).
    /// Používá se při aktualizaci nebo smazání pravidla.
    /// </summary>
    /// <param name="ruleId">ID pravidla k invalidaci</param>
    public void InvalidateRule(Guid ruleId)
    {
        InvalidateRuleAsync(ruleId).GetAwaiter().GetResult();
    }

    /// <summary>
    /// Invaliduje všechny cache záznamy pro konkrétní pravidlo (asynchronní verze).
    /// Používá se při aktualizaci nebo smazání pravidla.
    /// </summary>
    /// <param name="ruleId">ID pravidla k invalidaci</param>
    public async Task InvalidateRuleAsync(Guid ruleId)
    {
        var invalidatedCount = await _cacheService.InvalidateAsync($"*:{ruleId}:*");
        _logger.LogDebug("Invalidováno {Count} cache záznamů pro pravidlo s ID {RuleId}", invalidatedCount, ruleId);
    }

    /// <summary>
    /// Vymaže celou cache (synchronní verze pro zpětnou kompatibilitu).
    /// </summary>
    public void ClearCache()
    {
        ClearCacheAsync().GetAwaiter().GetResult();
    }

    /// <summary>
    /// Vymaže celou cache (asynchronní verze).
    /// </summary>
    public async Task ClearCacheAsync()
    {
        await _cacheService.ClearAsync();
        _logger.LogInformation("Cache Rule Engine byla kompletně vymazána");
    }

    private Func<object, object> CompileRule(BusinessRule rule)
    {
        if (!_entityTypeMap.TryGetValue(rule.TargetEntityName, out var entityType))
        {
            throw new InvalidOperationException($"Neznámý typ entity: {rule.TargetEntityName}");
        }

        var paramEntity = Expression.Parameter(entityType, "entity");
        var body = _builder.Build(rule.RootNode, paramEntity);
        var result = Expression.Convert(body, typeof(object));

        // Vytvoříme wrapper lambda, která přijme object a převede ho na správný typ
        var objectParam = Expression.Parameter(typeof(object), "obj");
        var convertedParam = Expression.Convert(objectParam, entityType);
        var bodyWithConversion = Expression.Invoke(
            Expression.Lambda(result, paramEntity),
            convertedParam);

        var lambda = Expression.Lambda<Func<object, object>>(bodyWithConversion, objectParam);
        return lambda.Compile();
    }

    /// <summary>
    /// Vytvoří hash pravidla pro cache klíče.
    /// Hash se mění při změně struktury pravidla.
    /// </summary>
    /// <param name="rule">Pravidlo pro hash</param>
    /// <returns>Hash string</returns>
    private string GetRuleHash(BusinessRule rule)
    {
        // Jednoduchý hash založený na RowVersion a SchemaVersion
        var hashInput = $"{Convert.ToBase64String(rule.RowVersion)}:{rule.SchemaVersion}:{rule.TargetEntityName}";
        return hashInput.GetHashCode().ToString("X");
    }

    /// <summary>
    /// Ověří, zda je entita podporována v rule engine.
    /// </summary>
    /// <param name="entityName">Název entity</param>
    /// <returns>True pokud je entita podporována</returns>
    public bool IsEntitySupported(string entityName)
    {
        return !string.IsNullOrWhiteSpace(entityName) && _entityTypeMap.ContainsKey(entityName);
    }

    /// <summary>
    /// Získá seznam všech podporovaných entit.
    /// </summary>
    /// <returns>Kolekce názvů podporovaných entit</returns>
    public IEnumerable<string> GetSupportedEntities()
    {
        return _entityTypeMap.Keys;
    }
}