# Rule Engine - <PERSON><PERSON><PERSON><PERSON> integrace s generickou správou entit

## P<PERSON><PERSON>led navrhovan<PERSON>ho řešení

Navrhli jsme **hybridn<PERSON> přístup** pro integraci Rule Enginu s generickou správou entit, který podporuje:

1. **<PERSON>ké spouštění pravidel** - Při CRUD operacích přes interceptory
2. **Ruční spouštění pravidel** - Na vyžádání přes API nebo EntityFacade
3. **Flexibilní konfiguraci** - Různé režimy spouštění a typy akcí
4. **Validační pravidla** - Automatická validace entit

## Klíčové komponenty

### 1. Rozšířená BusinessRule entita
```csharp
public class BusinessRule
{
    // Existující vlastnosti...
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string TargetEntityName { get; set; }
    public RuleNode RootNode { get; set; }
    
    // Nové vlastnosti pro integraci
    public RuleExecutionMode ExecutionMode { get; set; } = RuleExecutionMode.Manual;
    public RuleResultAction ResultAction { get; set; } = RuleResultAction.SetProperty;
    public int Priority { get; set; } = 0;
    public string? ExecutionCondition { get; set; }
}
```

### 2. Nové enumerace
```csharp
public enum RuleExecutionMode
{
    Manual = 0,           // Pouze ruční spouštění
    OnCreate = 1,         // Při vytváření entity
    OnUpdate = 2,         // Při aktualizaci entity
    OnCreateOrUpdate = 3, // Při vytváření nebo aktualizaci
    OnDelete = 4,         // Při mazání entity
    OnAnyChange = 7       // Při jakékoliv změně
}

public enum RuleResultAction
{
    SetProperty = 0,      // Nastaví hodnotu vlastnosti
    Validate = 1,         // Validuje entitu
    Calculate = 2,        // Vypočítá hodnotu
    Trigger = 3          // Spustí akci
}
```

### 3. IRuleExecutionService
Centrální služba pro spouštění pravidel s metodami pro:
- Automatické spouštění při CRUD operacích
- Ruční spouštění konkrétních pravidel
- Validaci entit pomocí pravidel
- Správu priorit a podmínek

### 4. RuleExecutionInterceptor
Interceptor integrovaný do Entity Framework pipeline, který:
- Zachytává CRUD operace
- Spouští aplikovatelná pravidla
- Aplikuje výsledky na entity
- Zpracovává validační chyby

## Výhody řešení

### ✅ Flexibilita
- **Automatické i ruční spouštění** podle potřeby
- **Různé typy akcí** (nastavení vlastnosti, validace, výpočet, trigger)
- **Konfigurovatelné podmínky** spuštění
- **Priority pravidel** pro řízení pořadí

### ✅ Integrace s existující architekturou
- **Využívá existující interceptory** a pipeline
- **Kompatibilní s EntityFacade** a generickými operacemi
- **Zachovává Clean Architecture** principy
- **Minimální změny** v existujícím kódu

### ✅ Výkon
- **Cache kompilovaných pravidel** v CalculationEngine
- **Spouštění pouze aplikovatelných pravidel**
- **Možnost nastavení priorit** pro optimalizaci
- **Asynchronní zpracování**

### ✅ Rozšiřitelnost
- **Snadné přidání nových typů akcí**
- **Podpora pro složitější scénáře**
- **Možnost řetězení pravidel**
- **Extensible execution conditions**

## Případy použití

### 1. Automatické výpočty
```csharp
// Pravidlo se spustí automaticky při vytvoření objednávky
ExecutionMode = RuleExecutionMode.OnCreate
ResultAction = RuleResultAction.SetProperty
TargetProperty = "DiscountAmount"
```

### 2. Validace dat
```csharp
// Pravidlo validuje data při aktualizaci
ExecutionMode = RuleExecutionMode.OnUpdate
ResultAction = RuleResultAction.Validate
```

### 3. Složité výpočty na vyžádání
```csharp
// Pravidlo se spustí pouze manuálně
ExecutionMode = RuleExecutionMode.Manual
ResultAction = RuleResultAction.Calculate
```

### 4. Triggery a notifikace
```csharp
// Pravidlo spustí akci při jakékoliv změně
ExecutionMode = RuleExecutionMode.OnAnyChange
ResultAction = RuleResultAction.Trigger
```

## Implementační plán

### Fáze 1: Základní infrastruktura (1-2 týdny)
1. ✅ Rozšíření BusinessRule entity
2. ✅ Vytvoření IRuleExecutionService a implementace
3. ✅ Rozšíření IRuleRepository o nové metody
4. ✅ Registrace v DI kontejneru
5. ✅ Základní unit testy

### Fáze 2: Automatické spouštění (1-2 týdny)
1. ✅ Implementace RuleExecutionInterceptor
2. ✅ Registrace interceptoru v ApplicationDbContext
3. ✅ Testování na jednoduchých pravidlech
4. ✅ Integration testy s databází

### Fáze 3: Ruční spouštění (1 týden)
1. ✅ Rozšíření EntityFacade o rule execution metody
2. ✅ Nové API endpointy pro ruční spouštění
3. ✅ Dokumentace API
4. ✅ Příklady použití

### Fáze 4: UI a pokročilé funkce (2-3 týdny)
1. 🔄 UI komponenty pro správu pravidel
2. 🔄 Podmíněné spouštění pravidel
3. 🔄 Monitoring a logování
4. 🔄 Performance optimalizace

## Rizika a mitigace

### ⚠️ Výkonnostní rizika
**Riziko**: Pomalé CRUD operace kvůli spouštění pravidel
**Mitigace**: 
- Cache kompilovaných pravidel
- Indexy na ExecutionMode a TargetEntityName
- Možnost vypnutí automatického spouštění per entita

### ⚠️ Složitost debuggingu
**Riziko**: Těžké ladění automaticky spouštěných pravidel
**Mitigace**:
- Detailní logování všech rule executions
- Debug endpointy pro testování pravidel
- Možnost vypnutí automatického spouštění pro development

### ⚠️ Nekonečné smyčky
**Riziko**: Pravidlo mění entitu, což spustí další pravidlo
**Mitigace**:
- Přeskakování BusinessRule entit v interceptoru
- Omezení počtu iterací
- Careful design pravidel

### ⚠️ Validační chyby
**Riziko**: Validační pravidla mohou blokovat legitimní operace
**Mitigace**:
- Možnost dočasného vypnutí pravidel
- Rollback mechanismus
- Careful testing validačních pravidel

## Doporučení pro týmy

### Pro analytiky
1. **Začněte s jednoduchými pravidly** - Nejdříve základní slevy a výpočty
2. **Testujte důkladně** - Každé pravidlo na různých scénářích
3. **Dokumentujte business logiku** - Jasný popis účelu každého pravidla
4. **Používejte priority** - Pro řízení pořadí spouštění pravidel

### Pro vývojáře frontendu
1. **Implementujte UI postupně** - Začněte s manuálním spouštěním
2. **Používejte metadata API** - Pro dynamické vytváření formulářů
3. **Implementujte error handling** - Pro zpracování chyb pravidel
4. **Cachujte metadata** - Pro lepší výkon UI

### Pro backend vývojáře
1. **Implementujte postupně** - Podle navrhovaných fází
2. **Monitorujte výkon** - Sledujte dobu spouštění pravidel
3. **Logujte vše** - Pro debugging a audit
4. **Testujte edge cases** - Zejména validační pravidla

### Pro DevOps
1. **Monitorujte performance** - Doba CRUD operací
2. **Sledujte chyby** - Rule execution failures
3. **Backup strategie** - Pro pravidla a jejich výsledky
4. **Scaling considerations** - Pro velké množství pravidel

## Metriky úspěchu

### Technické metriky
- ⏱️ **Doba CRUD operací** < 200ms (včetně rule execution)
- 🎯 **Úspěšnost rule execution** > 99%
- 📊 **Cache hit ratio** > 90% pro kompilovaná pravidla
- 🔍 **Test coverage** > 95% pro rule execution komponenty

### Business metriky
- 📈 **Počet aktivních pravidel** - Růst adoption
- 🎯 **Přesnost automatických výpočtů** - Správnost business logiky
- ⚡ **Rychlost implementace nových pravidel** - Time to market
- 👥 **Spokojenost uživatelů** - Feedback od analytiků a vývojářů

## Závěr

Navrhované řešení poskytuje **flexibilní a výkonnou integraci** Rule Enginu s generickou správou entit. Podporuje jak automatické, tak ruční spouštění pravidel při zachování kompatibility s existující architekturou.

**Klíčové benefity:**
- 🚀 **Rychlá implementace** business logiky bez změn kódu
- 🔄 **Automatizace** rutinních výpočtů a validací
- 🎛️ **Flexibilní konfigurace** podle potřeb business
- 📈 **Škálovatelnost** pro rostoucí počet pravidel

**Doporučujeme postupnou implementaci** podle navrhovaných fází s důrazem na testování a monitoring výkonu.
