# Rule Engine - Kompletní průvodce

## Obsah
1. [<PERSON><PERSON><PERSON><PERSON> systému](#přehled-systému)
2. [Architektura a komponenty](#architektura-a-komponenty)
3. [API Reference](#api-reference)
4. [Struktura pravidel](#struktura-pravidel)
5. [Př<PERSON>lady použití](#příklady-použití)
6. [Průvodce pro analytiky](#průvodce-pro-analytiky)
7. [Průvodce pro vývojáře](#průvodce-pro-vývojáře)
8. [Nejčastějš<PERSON> chyby a řešení](#nejčastější-chyby-a-řešení)

---

## P<PERSON>ehled systému

Rule Engine je systém pro vytváření, správu a vykonávání obchodních pravidel za běhu aplikace. Umožňuje definovat složité business logiky bez nutnosti změn v kódu aplikace.

### Klíčové vlastnosti
- **Dynamické pravidla**: Vytváření a úprava pravidel bez restartování aplikace
- **Stromová struktura**: Hierarchické pravidla s podporou vnořených podmínek
- **Validace syntaxe**: Automatická kontrola správnosti pravidel před uložením
- **Testování**: Možnost testování pravidel na ukázkových datech
- **Metadata API**: Poskytuje informace o dostupných entitách, operátorech a funkcích
- **Optimalizace**: Cachování kompilovaných pravidel pro vysoký výkon

### Podporované operace
- **Aritmetické**: `+`, `-`, `*`, `/`
- **Porovnávací**: `==`, `!=`, `>`, `<`, `>=`, `<=`
- **Logické**: `AND`, `OR`, `NOT`
- **Podmíněné**: `IF-THEN-ELSE`
- **Agregační**: `SUM`, `COUNT`, `AVERAGE`, `MIN`, `MAX`

---

## Architektura a komponenty

### Hlavní komponenty

#### 1. BusinessRule Entity
Technická entita reprezentující obchodní pravidlo:
```csharp
public class BusinessRule
{
    public Guid Id { get; set; }
    public string Name { get; set; }           // Název pravidla
    public string? Description { get; set; }   // Popis účelu
    public string TargetEntityName { get; set; } // Cílová entita (Order, Invoice, ...)
    public string? TargetProperty { get; set; }  // Cílová vlastnost
    public RuleNode RootNode { get; set; }     // Kořenový uzel stromu
    public bool IsActive { get; set; }         // Aktivní/neaktivní
    public string? InternalNotes { get; set; } // Poznámky pro vývojáře
}
```

#### 2. RuleNode - Stromová struktura
Abstraktní základní třída pro uzly pravidla:

**OperationNode** - Operace s operandy:
```json
{
  "nodeType": "Operation",
  "operator": "Add",
  "operands": [
    { "nodeType": "SourceValue", "sourcePath": "Amount" },
    { "nodeType": "Constant", "dataType": "Decimal", "value": "100" }
  ]
}
```

**SourceValueNode** - Přístup k vlastnostem entity:
```json
{
  "nodeType": "SourceValue",
  "sourcePath": "Customer.Address.City"
}
```

**ConstantNode** - Konstantní hodnoty:
```json
{
  "nodeType": "Constant",
  "dataType": "Decimal",
  "value": "15.5"
}
```

**AggregationNode** - Agregace nad kolekcemi:
```json
{
  "nodeType": "Aggregation",
  "aggregationType": "Sum",
  "collectionPath": "Items",
  "fieldPath": "Price",
  "filter": {
    "nodeType": "Operation",
    "operator": "GreaterThan",
    "operands": [
      { "nodeType": "SourceValue", "sourcePath": "Quantity" },
      { "nodeType": "Constant", "dataType": "Integer", "value": "1" }
    ]
  }
}
```

#### 3. CalculationEngine
Jádro systému pro kompilaci a vykonávání pravidel:
- Kompiluje pravidla do optimalizovaných Expression Trees
- Cachuje kompilovaná pravidla pro vysoký výkon
- Validuje syntaktickou správnost pravidel
- Poskytuje bezpečné vykonávání s error handlingem

---

## API Reference

### Base URL
```
/api/rule-engine
```

### Endpointy

#### Správa pravidel

**GET /rules**
```http
GET /api/rule-engine/rules
```
Vrací seznam všech pravidel jako pole `BusinessRule` objektů.

**GET /rules/{id}**
```http
GET /api/rule-engine/rules/123e4567-e89b-12d3-a456-426614174000
```
Vrací konkrétní pravidlo podle ID.

**POST /rules**
```http
POST /api/rule-engine/rules
Content-Type: application/json

{
  "name": "Sleva nad 1000 Kč",
  "description": "10% sleva pro objednávky nad 1000 Kč",
  "targetEntityName": "Order",
  "targetProperty": "DiscountAmount",
  "rootNode": {
    "nodeType": "Operation",
    "operator": "If",
    "operands": [...]
  },
  "isActive": true
}
```

**PUT /rules/{id}**
```http
PUT /api/rule-engine/rules/123e4567-e89b-12d3-a456-426614174000
Content-Type: application/json

{
  "name": "Aktualizované pravidlo",
  "description": "Nový popis",
  ...
}
```

**DELETE /rules/{id}**
```http
DELETE /api/rule-engine/rules/123e4567-e89b-12d3-a456-426614174000
```

#### Metadata API

**GET /metadata**
```http
GET /api/rule-engine/metadata
```
Vrací metadata pro vytváření pravidel:
```json
{
  "availableEntities": [
    {
      "name": "Order",
      "displayName": "Objednávka",
      "description": "Objednávka zákazníka s položkami"
    }
  ],
  "availableOperators": [
    {
      "value": "Add",
      "displayName": "Sčítání (+)",
      "category": "Aritmetické",
      "description": "Sečte dva číselné operandy"
    }
  ],
  "availableAggregations": [
    {
      "value": "Sum",
      "displayName": "Součet",
      "description": "Sečte hodnoty zadaného pole",
      "requiresField": true
    }
  ],
  "availableValueTypes": [
    {
      "value": "Decimal",
      "displayName": "Desetinné číslo",
      "description": "Desetinné číslo (např. 3.14, -2.5)"
    }
  ],
  "schemaVersion": "1.0"
}
```

**GET /entities/{entityName}/properties**
```http
GET /api/rule-engine/entities/Order/properties
```
Vrací vlastnosti konkrétní entity pro použití v pravidlech.

---

## Struktura pravidel

### Typy uzlů

#### 1. OperationNode
Reprezentuje operaci s jedním nebo více operandy.

**Podporované operátory:**
- **Aritmetické**: `Add`, `Subtract`, `Multiply`, `Divide`
- **Porovnávací**: `Equal`, `NotEqual`, `GreaterThan`, `LessThan`, `GreaterThanOrEqual`, `LessThanOrEqual`
- **Logické**: `And`, `Or`, `Not`
- **Podmíněné**: `If` (IF-THEN-ELSE struktura)

#### 2. SourceValueNode
Přístup k vlastnostem zdrojové entity pomocí tečkové notace.

**Příklady:**
- `"Amount"` - přímá vlastnost
- `"Customer.Name"` - navigace přes vztah
- `"Items[0].Price"` - přístup k prvku kolekce

#### 3. ConstantNode
Konstantní hodnoty různých datových typů.

**Podporované typy:**
- `String`, `Integer`, `Decimal`, `Boolean`, `DateTime`, `Guid`

#### 4. AggregationNode
Agregační operace nad kolekcemi.

**Podporované agregace:**
- `Sum` - součet hodnot
- `Count` - počet prvků
- `Average` - průměr hodnot
- `Min` - minimální hodnota
- `Max` - maximální hodnota

### Příklad komplexního pravidla

```json
{
  "name": "Věrnostní sleva",
  "description": "Sleva podle celkové hodnoty objednávek zákazníka",
  "targetEntityName": "Order",
  "targetProperty": "LoyaltyDiscount",
  "rootNode": {
    "nodeType": "Operation",
    "operator": "If",
    "operands": [
      {
        "nodeType": "Operation",
        "operator": "GreaterThan",
        "operands": [
          {
            "nodeType": "Aggregation",
            "aggregationType": "Sum",
            "collectionPath": "Customer.Orders",
            "fieldPath": "TotalAmount",
            "filter": {
              "nodeType": "Operation",
              "operator": "GreaterThan",
              "operands": [
                { "nodeType": "SourceValue", "sourcePath": "OrderDate" },
                { "nodeType": "Constant", "dataType": "DateTime", "value": "2024-01-01" }
              ]
            }
          },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "50000" }
        ]
      },
      {
        "nodeType": "Operation",
        "operator": "Multiply",
        "operands": [
          { "nodeType": "SourceValue", "sourcePath": "TotalAmount" },
          { "nodeType": "Constant", "dataType": "Decimal", "value": "0.15" }
        ]
      },
      { "nodeType": "Constant", "dataType": "Decimal", "value": "0" }
    ]
  }
}
```

Toto pravidlo říká: "Pokud je součet objednávek zákazníka za letošní rok větší než 50 000 Kč, uděl 15% slevu, jinak 0."

---

## Příklady použití

### 1. Jednoduchá sleva
**Business požadavek**: 10% sleva pro objednávky nad 1000 Kč

```javascript
const rule = {
  name: "Sleva nad 1000 Kč",
  description: "10% sleva pro velké objednávky",
  targetEntityName: "Order",
  targetProperty: "DiscountAmount",
  rootNode: {
    nodeType: "Operation",
    operator: "If",
    operands: [
      // Podmínka: Amount > 1000
      {
        nodeType: "Operation",
        operator: "GreaterThan",
        operands: [
          { nodeType: "SourceValue", sourcePath: "Amount" },
          { nodeType: "Constant", dataType: "Decimal", value: "1000" }
        ]
      },
      // THEN: Amount * 0.1
      {
        nodeType: "Operation",
        operator: "Multiply",
        operands: [
          { nodeType: "SourceValue", sourcePath: "Amount" },
          { nodeType: "Constant", dataType: "Decimal", value: "0.1" }
        ]
      },
      // ELSE: 0
      { nodeType: "Constant", dataType: "Decimal", value: "0" }
    ]
  },
  isActive: true
};
```

### 2. Doprava zdarma
**Business požadavek**: Doprava zdarma pro objednávky nad 15 000 Kč

```javascript
const shippingRule = {
  name: "Doprava zdarma nad 15 000 Kč",
  description: "Doprava zdarma pro velké objednávky",
  targetEntityName: "Order",
  targetProperty: "ShippingCost",
  rootNode: {
    nodeType: "Operation",
    operator: "If",
    operands: [
      {
        nodeType: "Operation",
        operator: "GreaterThanOrEqual",
        operands: [
          { nodeType: "SourceValue", sourcePath: "TotalAmount" },
          { nodeType: "Constant", dataType: "Decimal", value: "15000" }
        ]
      },
      { nodeType: "Constant", dataType: "Decimal", value: "0" },
      { nodeType: "SourceValue", sourcePath: "StandardShippingCost" }
    ]
  },
  isActive: true
};
```

### 3. Složitější pravidlo s agregací
**Business požadavek**: Sleva podle počtu položek v objednávce

```javascript
const quantityDiscountRule = {
  name: "Množstevní sleva",
  description: "Sleva podle celkového množství položek",
  targetEntityName: "Order",
  targetProperty: "QuantityDiscount",
  rootNode: {
    nodeType: "Operation",
    operator: "If",
    operands: [
      // Podmínka: SUM(Items.Quantity) >= 10
      {
        nodeType: "Operation",
        operator: "GreaterThanOrEqual",
        operands: [
          {
            nodeType: "Aggregation",
            aggregationType: "Sum",
            collectionPath: "Items",
            fieldPath: "Quantity"
          },
          { nodeType: "Constant", dataType: "Integer", value: "10" }
        ]
      },
      // THEN: 5% sleva
      {
        nodeType: "Operation",
        operator: "Multiply",
        operands: [
          { nodeType: "SourceValue", sourcePath: "TotalAmount" },
          { nodeType: "Constant", dataType: "Decimal", value: "0.05" }
        ]
      },
      // ELSE: 0
      { nodeType: "Constant", dataType: "Decimal", value: "0" }
    ]
  },
  isActive: true
};
```

### 4. Práce s API - Vytvoření pravidla

```javascript
async function createRule(rule) {
  try {
    const response = await fetch('/api/rule-engine/rules', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + getAuthToken()
      },
      body: JSON.stringify(rule)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Chyba při vytváření pravidla: ${error.message}`);
    }

    const createdRule = await response.json();
    console.log('Pravidlo vytvořeno:', createdRule);
    return createdRule;
  } catch (error) {
    console.error('Chyba:', error);
    throw error;
  }
}
```

### 5. Validace pravidla před uložením

```javascript
async function validateRule(rule) {
  try {
    const response = await fetch('/api/rule-engine/rules/validate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: rule.name,
        targetEntityName: rule.targetEntityName,
        rootNode: rule.rootNode
      })
    });

    const result = await response.json();

    if (result.isValid) {
      console.log('Pravidlo je validní');
      return true;
    } else {
      console.error('Chyby v pravidle:', result.errors);
      return false;
    }
  } catch (error) {
    console.error('Chyba při validaci:', error);
    return false;
  }
}
```

### 6. Testování pravidla

```javascript
async function testRule(ruleId, testData) {
  try {
    const response = await fetch(`/api/rule-engine/rules/${ruleId}/test`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();

    if (result.success) {
      console.log('Výsledek testu:', result.result);
      return result.result;
    } else {
      console.error('Chyba při testování:', result.message);
      return null;
    }
  } catch (error) {
    console.error('Chyba při testování:', error);
    return null;
  }
}

// Příklad použití
const testData = {
  id: "123e4567-e89b-12d3-a456-426614174000",
  amount: 1500,
  totalAmount: 1500,
  items: [
    { quantity: 2, price: 500 },
    { quantity: 1, price: 500 }
  ]
};

testRule('rule-id-here', testData);
```

---

## Průvodce pro analytiky

### Jak navrhovat obchodní pravidla

#### 1. Identifikace požadavku
Před vytvořením pravidla si odpovězte na tyto otázky:
- **Co** chceme vypočítat nebo rozhodnout?
- **Kdy** se má pravidlo aplikovat?
- **Na jakých datech** pravidlo závisí?
- **Jaký** je očekávaný výsledek?

#### 2. Mapování na entity
Určete, které entity a jejich vlastnosti budete potřebovat:

**Dostupné entity:**
- `Order` - objednávka (amount, totalAmount, orderDate, customerId, ...)
- `OrderItem` - položka objednávky (quantity, price, productId, ...)
- `Invoice` - faktura (amount, dueDate, isPaid, ...)
- `Customer` - zákazník (name, email, registrationDate, ...)

#### 3. Typy pravidel podle složitosti

**Jednoduchá pravidla** (1 podmínka):
```
IF objednávka.částka > 1000 THEN sleva = 10% ELSE sleva = 0%
```

**Složená pravidla** (více podmínek):
```
IF (objednávka.částka > 1000 AND zákazník.typ = "VIP")
   OR objednávka.částka > 5000
THEN sleva = 15%
ELSE sleva = 5%
```

**Pravidla s agregací** (práce s kolekcemi):
```
IF SOUČET(položky.množství) >= 10
THEN množstevní_sleva = 5%
ELSE množstevní_sleva = 0%
```

#### 4. Testovací scénáře
Pro každé pravidlo připravte testovací případy:

**Pozitivní testy:**
- Minimální hodnoty, které splňují podmínku
- Typické hodnoty
- Maximální/extrémní hodnoty

**Negativní testy:**
- Hodnoty těsně pod hranicí
- Nulové/prázdné hodnoty
- Neplatné hodnoty

**Příklad testovacích dat:**
```javascript
// Test 1: Objednávka nad 1000 Kč (očekávaná sleva: 150 Kč)
{
  amount: 1500,
  customerId: "customer-123"
}

// Test 2: Objednávka pod 1000 Kč (očekávaná sleva: 0 Kč)
{
  amount: 800,
  customerId: "customer-456"
}

// Test 3: Hraničí případ (očekávaná sleva: 0 Kč)
{
  amount: 1000,
  customerId: "customer-789"
}
```

#### 5. Dokumentace pravidel
Pro každé pravidlo vytvořte dokumentaci obsahující:

```markdown
## Pravidlo: Sleva nad 1000 Kč

**Účel**: Motivace zákazníků k větším nákupům

**Podmínky aktivace**:
- Celková částka objednávky > 1000 Kč
- Objednávka není stornovaná

**Výpočet**:
- Sleva = 10% z celkové částky objednávky
- Maximální sleva: bez omezení

**Testovací případy**:
| Částka | Očekávaná sleva | Poznámka |
|--------|----------------|----------|
| 1500   | 150 Kč         | Standardní případ |
| 1000   | 0 Kč           | Hraničí případ |
| 999    | 0 Kč           | Pod hranicí |

**Datum platnosti**: Od 1.1.2024
**Odpovědná osoba**: Jan Novák (analytik)
```

#### 6. Časté business vzory

**Věrnostní programy:**
```javascript
// Sleva podle celkové hodnoty nákupů zákazníka
IF SOUČET(zákazník.objednávky.částka) > 50000 THEN sleva = 20%
ELSE IF SOUČET(zákazník.objednávky.částka) > 20000 THEN sleva = 10%
ELSE sleva = 0%
```

**Sezónní akce:**
```javascript
// Vánoční sleva v prosinci
IF MĚSÍC(objednávka.datum) = 12 THEN sleva = 15%
ELSE sleva = 0%
```

**Množstevní slevy:**
```javascript
// Sleva podle počtu kusů
IF SOUČET(položky.množství) >= 20 THEN sleva = 15%
ELSE IF SOUČET(položky.množství) >= 10 THEN sleva = 10%
ELSE IF SOUČET(položky.množství) >= 5 THEN sleva = 5%
ELSE sleva = 0%
```

---

## Průvodce pro vývojáře

### Integrace do frontendu

#### 1. Načtení metadat
Před vytvořením UI pro editaci pravidel načtěte metadata:

```javascript
class RuleEngineService {
  constructor(baseUrl = '/api/rule-engine') {
    this.baseUrl = baseUrl;
  }

  async getMetadata() {
    const response = await fetch(`${this.baseUrl}/metadata`);
    if (!response.ok) {
      throw new Error('Nepodařilo se načíst metadata');
    }
    return await response.json();
  }

  async getEntityProperties(entityName) {
    const response = await fetch(`${this.baseUrl}/entities/${entityName}/properties`);
    if (!response.ok) {
      throw new Error(`Nepodařilo se načíst vlastnosti entity ${entityName}`);
    }
    return await response.json();
  }
}
```

#### 2. Vytvoření rule builderu
Implementujte UI komponentu pro vizuální vytváření pravidel:

```javascript
class RuleBuilder {
  constructor(metadata) {
    this.metadata = metadata;
    this.currentRule = this.createEmptyRule();
  }

  createEmptyRule() {
    return {
      name: '',
      description: '',
      targetEntityName: '',
      targetProperty: '',
      rootNode: {
        nodeType: 'Constant',
        dataType: 'String',
        value: ''
      },
      isActive: true
    };
  }

  // Vytvoření operation node
  createOperationNode(operator, operands = []) {
    return {
      nodeType: 'Operation',
      operator: operator,
      operands: operands
    };
  }

  // Vytvoření constant node
  createConstantNode(dataType, value) {
    return {
      nodeType: 'Constant',
      dataType: dataType,
      value: value.toString()
    };
  }

  // Vytvoření source value node
  createSourceValueNode(sourcePath) {
    return {
      nodeType: 'SourceValue',
      sourcePath: sourcePath
    };
  }

  // Vytvoření aggregation node
  createAggregationNode(aggregationType, collectionPath, fieldPath = null, filter = null) {
    return {
      nodeType: 'Aggregation',
      aggregationType: aggregationType,
      collectionPath: collectionPath,
      fieldPath: fieldPath,
      filter: filter
    };
  }
}
```

#### 3. Validace na frontendu
Implementujte základní validaci před odesláním na server:

```javascript
class RuleValidator {
  static validateRule(rule) {
    const errors = [];

    // Základní validace
    if (!rule.name || rule.name.trim() === '') {
      errors.push('Název pravidla je povinný');
    }

    if (!rule.targetEntityName) {
      errors.push('Cílová entita je povinná');
    }

    if (!rule.rootNode) {
      errors.push('Pravidlo musí obsahovat alespoň jeden uzel');
    }

    // Validace stromu uzlů
    if (rule.rootNode) {
      const nodeErrors = this.validateNode(rule.rootNode);
      errors.push(...nodeErrors);
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  static validateNode(node) {
    const errors = [];

    if (!node.nodeType) {
      errors.push('Typ uzlu je povinný');
      return errors;
    }

    switch (node.nodeType) {
      case 'Operation':
        if (!node.operator) {
          errors.push('Operátor je povinný pro operační uzel');
        }
        if (!node.operands || node.operands.length === 0) {
          errors.push('Operační uzel musí mít alespoň jeden operand');
        }
        // Rekurzivní validace operandů
        if (node.operands) {
          node.operands.forEach((operand, index) => {
            const operandErrors = this.validateNode(operand);
            errors.push(...operandErrors.map(err => `Operand ${index + 1}: ${err}`));
          });
        }
        break;

      case 'Constant':
        if (!node.dataType) {
          errors.push('Datový typ je povinný pro konstantní uzel');
        }
        if (node.value === undefined || node.value === null) {
          errors.push('Hodnota je povinná pro konstantní uzel');
        }
        break;

      case 'SourceValue':
        if (!node.sourcePath) {
          errors.push('Cesta ke zdroji je povinná pro zdrojový uzel');
        }
        break;

      case 'Aggregation':
        if (!node.aggregationType) {
          errors.push('Typ agregace je povinný pro agregační uzel');
        }
        if (!node.collectionPath) {
          errors.push('Cesta ke kolekci je povinná pro agregační uzel');
        }
        break;

      default:
        errors.push(`Neznámý typ uzlu: ${node.nodeType}`);
    }

    return errors;
  }
}
```

#### 4. Error handling
Implementujte robustní error handling:

```javascript
class RuleEngineClient {
  async createRule(rule) {
    try {
      // Validace na frontendu
      const validation = RuleValidator.validateRule(rule);
      if (!validation.isValid) {
        throw new ValidationError('Pravidlo obsahuje chyby', validation.errors);
      }

      const response = await fetch('/api/rule-engine/rules', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`
        },
        body: JSON.stringify(rule)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new ApiError(`HTTP ${response.status}`, errorData);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof ValidationError) {
        this.showValidationErrors(error.errors);
      } else if (error instanceof ApiError) {
        this.showApiError(error.message);
      } else {
        this.showGenericError('Neočekávaná chyba při vytváření pravidla');
      }
      throw error;
    }
  }

  showValidationErrors(errors) {
    // Zobrazení chyb validace v UI
    console.error('Chyby validace:', errors);
  }

  showApiError(message) {
    // Zobrazení API chyby v UI
    console.error('API chyba:', message);
  }

  showGenericError(message) {
    // Zobrazení obecné chyby v UI
    console.error('Obecná chyba:', message);
  }
}

class ValidationError extends Error {
  constructor(message, errors) {
    super(message);
    this.name = 'ValidationError';
    this.errors = errors;
  }
}

class ApiError extends Error {
  constructor(message, data) {
    super(message);
    this.name = 'ApiError';
    this.data = data;
  }
}
```

#### 5. Optimalizace výkonu
Doporučení pro optimální výkon:

```javascript
class RuleEngineCache {
  constructor() {
    this.metadataCache = null;
    this.entityPropertiesCache = new Map();
    this.rulesCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minut
  }

  async getMetadata(forceRefresh = false) {
    if (!this.metadataCache || forceRefresh || this.isCacheExpired(this.metadataCache)) {
      const metadata = await this.fetchMetadata();
      this.metadataCache = {
        data: metadata,
        timestamp: Date.now()
      };
    }
    return this.metadataCache.data;
  }

  async getEntityProperties(entityName, forceRefresh = false) {
    const cacheKey = entityName;
    const cached = this.entityPropertiesCache.get(cacheKey);

    if (!cached || forceRefresh || this.isCacheExpired(cached)) {
      const properties = await this.fetchEntityProperties(entityName);
      this.entityPropertiesCache.set(cacheKey, {
        data: properties,
        timestamp: Date.now()
      });
    }

    return this.entityPropertiesCache.get(cacheKey).data;
  }

  isCacheExpired(cacheEntry) {
    return Date.now() - cacheEntry.timestamp > this.cacheTimeout;
  }

  clearCache() {
    this.metadataCache = null;
    this.entityPropertiesCache.clear();
    this.rulesCache.clear();
  }
}
```

#### 6. React komponenta příklad
Příklad React komponenty pro editaci pravidel:

```jsx
import React, { useState, useEffect } from 'react';

const RuleEditor = ({ ruleId, onSave, onCancel }) => {
  const [rule, setRule] = useState(null);
  const [metadata, setMetadata] = useState(null);
  const [loading, setLoading] = useState(true);
  const [errors, setErrors] = useState([]);

  useEffect(() => {
    loadData();
  }, [ruleId]);

  const loadData = async () => {
    try {
      setLoading(true);

      // Načtení metadat
      const metadataResponse = await fetch('/api/rule-engine/metadata');
      const metadataData = await metadataResponse.json();
      setMetadata(metadataData);

      // Načtení pravidla (pokud editujeme existující)
      if (ruleId) {
        const ruleResponse = await fetch(`/api/rule-engine/rules/${ruleId}`);
        const ruleData = await ruleResponse.json();
        setRule(ruleData);
      } else {
        // Nové pravidlo
        setRule({
          name: '',
          description: '',
          targetEntityName: '',
          targetProperty: '',
          rootNode: {
            nodeType: 'Constant',
            dataType: 'String',
            value: ''
          },
          isActive: true
        });
      }
    } catch (error) {
      console.error('Chyba při načítání dat:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      // Validace
      const validation = RuleValidator.validateRule(rule);
      if (!validation.isValid) {
        setErrors(validation.errors);
        return;
      }

      // Uložení
      const method = ruleId ? 'PUT' : 'POST';
      const url = ruleId ? `/api/rule-engine/rules/${ruleId}` : '/api/rule-engine/rules';

      const response = await fetch(url, {
        method: method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(rule)
      });

      if (!response.ok) {
        throw new Error('Chyba při ukládání pravidla');
      }

      const savedRule = method === 'POST' ? await response.json() : rule;
      onSave(savedRule);
    } catch (error) {
      console.error('Chyba při ukládání:', error);
      setErrors([error.message]);
    }
  };

  if (loading) {
    return <div>Načítání...</div>;
  }

  return (
    <div className="rule-editor">
      <h2>{ruleId ? 'Editace pravidla' : 'Nové pravidlo'}</h2>

      {errors.length > 0 && (
        <div className="errors">
          {errors.map((error, index) => (
            <div key={index} className="error">{error}</div>
          ))}
        </div>
      )}

      <div className="form-group">
        <label>Název:</label>
        <input
          type="text"
          value={rule.name}
          onChange={(e) => setRule({...rule, name: e.target.value})}
        />
      </div>

      <div className="form-group">
        <label>Popis:</label>
        <textarea
          value={rule.description || ''}
          onChange={(e) => setRule({...rule, description: e.target.value})}
        />
      </div>

      <div className="form-group">
        <label>Cílová entita:</label>
        <select
          value={rule.targetEntityName}
          onChange={(e) => setRule({...rule, targetEntityName: e.target.value})}
        >
          <option value="">Vyberte entitu</option>
          {metadata?.availableEntities.map(entity => (
            <option key={entity.name} value={entity.name}>
              {entity.displayName}
            </option>
          ))}
        </select>
      </div>

      {/* Zde by byl komplexní editor pro rootNode */}
      <RuleNodeEditor
        node={rule.rootNode}
        metadata={metadata}
        onChange={(newNode) => setRule({...rule, rootNode: newNode})}
      />

      <div className="form-actions">
        <button onClick={handleSave}>Uložit</button>
        <button onClick={onCancel}>Zrušit</button>
      </div>
    </div>
  );
};
```

---

## Nejčastější chyby a řešení

### 1. Chyby při vytváření pravidel

**Chyba**: "Neznámý typ entity: XYZ"
**Příčina**: Použití neexistující entity v `targetEntityName`
**Řešení**: Zkontrolujte dostupné entity pomocí `/api/rule-engine/metadata`

**Chyba**: "Syntaktická chyba v pravidle"
**Příčina**: Neplatná struktura `rootNode`
**Řešení**: Validujte strukturu uzlů před odesláním

**Chyba**: "Pravidlo s názvem 'XYZ' již existuje"
**Příčina**: Duplicitní název pravidla
**Řešení**: Použijte jedinečný název nebo aktualizujte existující pravidlo

### 2. Chyby při vykonávání pravidel

**Chyba**: "Vlastnost 'XYZ' neexistuje na entitě"
**Příčina**: Neplatná cesta v `SourceValueNode`
**Řešení**: Zkontrolujte dostupné vlastnosti entity

**Chyba**: "Nelze převést hodnotu na typ Decimal"
**Příčina**: Nekompatibilní datové typy v operacích
**Řešení**: Zajistěte kompatibilitu typů v `ConstantNode`

**Chyba**: "Dělení nulou"
**Příčina**: Dělení konstantou nebo vypočtenou hodnotou 0
**Řešení**: Přidejte kontrolu před dělením

### 3. Výkonnostní problémy

**Problém**: Pomalé vykonávání pravidel
**Příčina**: Složité agregace nebo neoptimalizované dotazy
**Řešení**:
- Minimalizujte počet agregací
- Používejte filtry v agregacích
- Cachujte často používaná pravidla

**Problém**: Vysoká spotřeba paměti
**Příčina**: Příliš mnoho aktivních pravidel
**Řešení**: Deaktivujte nepoužívaná pravidla

### 4. Debugging pravidel

```javascript
// Funkce pro debug výpis struktury pravidla
function debugRule(rule) {
  console.log('=== DEBUG PRAVIDLA ===');
  console.log('Název:', rule.name);
  console.log('Cílová entita:', rule.targetEntityName);
  console.log('Aktivní:', rule.isActive);
  console.log('Struktura uzlů:');
  debugNode(rule.rootNode, 0);
}

function debugNode(node, depth) {
  const indent = '  '.repeat(depth);
  console.log(`${indent}${node.nodeType}:`);

  switch (node.nodeType) {
    case 'Operation':
      console.log(`${indent}  Operátor: ${node.operator}`);
      console.log(`${indent}  Operandy (${node.operands.length}):`);
      node.operands.forEach((operand, index) => {
        console.log(`${indent}    [${index}]:`);
        debugNode(operand, depth + 2);
      });
      break;

    case 'Constant':
      console.log(`${indent}  Typ: ${node.dataType}`);
      console.log(`${indent}  Hodnota: ${node.value}`);
      break;

    case 'SourceValue':
      console.log(`${indent}  Cesta: ${node.sourcePath}`);
      break;

    case 'Aggregation':
      console.log(`${indent}  Typ: ${node.aggregationType}`);
      console.log(`${indent}  Kolekce: ${node.collectionPath}`);
      if (node.fieldPath) {
        console.log(`${indent}  Pole: ${node.fieldPath}`);
      }
      if (node.filter) {
        console.log(`${indent}  Filtr:`);
        debugNode(node.filter, depth + 1);
      }
      break;
  }
}
```

### 5. Testovací utility

```javascript
// Utility pro testování pravidel
class RuleTestUtils {
  static createTestOrder(amount = 1000, itemCount = 1) {
    return {
      id: crypto.randomUUID(),
      amount: amount,
      totalAmount: amount,
      orderDate: new Date().toISOString(),
      customerId: crypto.randomUUID(),
      items: Array.from({length: itemCount}, (_, i) => ({
        id: crypto.randomUUID(),
        quantity: 1,
        price: amount / itemCount,
        productId: `product-${i + 1}`
      }))
    };
  }

  static async testRuleWithScenarios(ruleId, scenarios) {
    const results = [];

    for (const scenario of scenarios) {
      try {
        const result = await this.testRule(ruleId, scenario.data);
        results.push({
          name: scenario.name,
          expected: scenario.expected,
          actual: result,
          passed: result === scenario.expected
        });
      } catch (error) {
        results.push({
          name: scenario.name,
          expected: scenario.expected,
          actual: null,
          error: error.message,
          passed: false
        });
      }
    }

    return results;
  }

  static async testRule(ruleId, testData) {
    const response = await fetch(`/api/rule-engine/rules/${ruleId}/test`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testData)
    });

    const result = await response.json();
    return result.success ? result.result : null;
  }
}

// Příklad použití
const scenarios = [
  {
    name: 'Objednávka nad 1000 Kč',
    data: RuleTestUtils.createTestOrder(1500),
    expected: 150 // 10% sleva
  },
  {
    name: 'Objednávka pod 1000 Kč',
    data: RuleTestUtils.createTestOrder(800),
    expected: 0 // žádná sleva
  },
  {
    name: 'Hraničí případ',
    data: RuleTestUtils.createTestOrder(1000),
    expected: 0 // žádná sleva (> 1000, ne >= 1000)
  }
];

RuleTestUtils.testRuleWithScenarios('rule-id', scenarios)
  .then(results => {
    console.log('Výsledky testů:', results);
    const passed = results.filter(r => r.passed).length;
    console.log(`Prošlo ${passed}/${results.length} testů`);
  });
```

---

## Závěr

Rule Engine poskytuje mocný nástroj pro vytváření a správu obchodních pravidel. Klíčem k úspěšnému použití je:

1. **Důkladné plánování** - Před implementací si jasně definujte požadavky
2. **Postupná implementace** - Začněte s jednoduchými pravidly a postupně přidávejte složitost
3. **Testování** - Vždy otestujte pravidla na různých scénářích
4. **Dokumentace** - Dokumentujte každé pravidlo pro budoucí údržbu
5. **Monitoring** - Sledujte výkon a chování pravidel v produkci

Pro další otázky nebo podporu kontaktujte vývojový tým.
```
