using Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Persistence.Seeders;

/// <summary>
/// Seeder pro vytvoření testovacích objedná<PERSON>k a faktur.
/// </summary>
public static class OrderAndInvoiceSeeder
{
    /// <summary>
    /// Naplní databázi testovacími objedn<PERSON>vka<PERSON> a fakturami.
    /// </summary>
    /// <param name="context">Databázový kontext</param>
    public static async Task SeedAsync(ApplicationDbContext context)
    {
        // Zkontrolujeme, zda už existují data
        if (await context.Set<Order>().AnyAsync())
        {
            return; // Data už existují
        }

        var orders = CreateTestOrders();
        await context.Set<Order>().AddRangeAsync(orders);
        await context.SaveChangesAsync();

        var invoices = CreateTestInvoices(orders);
        await context.Set<Invoice>().AddRangeAsync(invoices);
        await context.SaveChangesAsync();
    }

    /// <summary>
    /// Vytvoří testovací objednávky.
    /// </summary>
    private static List<Order> CreateTestOrders()
    {
        var orders = new List<Order>();

        // Objednávka 1 - Malá objednávka
        var order1 = new Order
        {
            Id = Guid.NewGuid(),
            OrderNumber = "ORD-2024-001",
            OrderDate = DateTime.Now.AddDays(-30),
            CustomerId = Guid.NewGuid(),
            CustomerName = "Jan Novák",
            CustomerEmail = "<EMAIL>",
            Status = OrderStatus.Delivered,
            SubTotal = 2500m,
            TaxAmount = 525m,
            DiscountPercentage = 0m,
            DiscountAmount = 0m,
            ShippingCost = 150m,
            TotalAmount = 3175m,
            Currency = "CZK",
            Notes = "Standardní objednávka",
            ShippingAddress = "Václavské náměstí 1",
            ShippingCity = "Praha",
            ShippingPostalCode = "11000",
            ShippingCountry = "CZ",
            ExpectedDeliveryDate = DateTime.Now.AddDays(-25),
            ActualDeliveryDate = DateTime.Now.AddDays(-23),
            CreatedAt = DateTime.Now.AddDays(-30),
            ModifiedAt = DateTime.Now.AddDays(-23)
        };

        order1.Items = new List<OrderItem>
        {
            new OrderItem
            {
                Id = Guid.NewGuid(),
                OrderId = order1.Id,
                ProductCode = "LAPTOP-001",
                ProductName = "Notebook Dell Inspiron",
                ProductDescription = "15.6\" notebook s Intel Core i5",
                Category = "Elektronika",
                UnitPrice = 20000m,
                Quantity = 1,
                Unit = "ks",
                Weight = 2.5m,
                TaxRate = 21m,
                DiscountPercentage = 0m,
                LineTotal = 20000m,
                LineTaxAmount = 4200m,
                LineTotalWithTax = 24200m,
                IsInStock = true,
                CreatedAt = DateTime.Now.AddDays(-30)
            },
            new OrderItem
            {
                Id = Guid.NewGuid(),
                OrderId = order1.Id,
                ProductCode = "MOUSE-001",
                ProductName = "Optická myš Logitech",
                ProductDescription = "Bezdrátová optická myš",
                Category = "Příslušenství",
                UnitPrice = 500m,
                Quantity = 1,
                Unit = "ks",
                Weight = 0.1m,
                TaxRate = 21m,
                DiscountPercentage = 0m,
                LineTotal = 500m,
                LineTaxAmount = 105m,
                LineTotalWithTax = 605m,
                IsInStock = true,
                CreatedAt = DateTime.Now.AddDays(-30)
            }
        };

        // Přepočítáme totaly
        order1.SubTotal = order1.Items.Sum(i => i.LineTotal);
        order1.TaxAmount = order1.Items.Sum(i => i.LineTaxAmount);
        order1.TotalAmount = order1.SubTotal + order1.TaxAmount + order1.ShippingCost;

        orders.Add(order1);

        // Objednávka 2 - Velká objednávka se slevou
        var order2 = new Order
        {
            Id = Guid.NewGuid(),
            OrderNumber = "ORD-2024-002",
            OrderDate = DateTime.Now.AddDays(-15),
            CustomerId = Guid.NewGuid(),
            CustomerName = "Marie Svobodová",
            CustomerEmail = "<EMAIL>",
            Status = OrderStatus.Shipped,
            SubTotal = 45000m,
            TaxAmount = 9450m,
            DiscountPercentage = 10m,
            DiscountAmount = 5000m,
            ShippingCost = 0m, // Doprava zdarma
            TotalAmount = 49450m,
            Currency = "CZK",
            Notes = "Firemní objednávka - sleva 10%",
            ShippingAddress = "Náměstí Míru 15",
            ShippingCity = "Brno",
            ShippingPostalCode = "60200",
            ShippingCountry = "CZ",
            ExpectedDeliveryDate = DateTime.Now.AddDays(-10),
            CreatedAt = DateTime.Now.AddDays(-15),
            ModifiedAt = DateTime.Now.AddDays(-14)
        };

        order2.Items = new List<OrderItem>
        {
            new OrderItem
            {
                Id = Guid.NewGuid(),
                OrderId = order2.Id,
                ProductCode = "SERVER-001",
                ProductName = "Server HP ProLiant",
                ProductDescription = "Rack server 1U s Intel Xeon",
                Category = "Servery",
                UnitPrice = 50000m,
                Quantity = 1,
                Unit = "ks",
                Weight = 15.0m,
                TaxRate = 21m,
                DiscountPercentage = 10m,
                LineTotal = 45000m,
                LineTaxAmount = 9450m,
                LineTotalWithTax = 54450m,
                IsInStock = true,
                CreatedAt = DateTime.Now.AddDays(-15)
            }
        };

        orders.Add(order2);

        // Objednávka 3 - Nová objednávka
        var order3 = new Order
        {
            Id = Guid.NewGuid(),
            OrderNumber = "ORD-2024-003",
            OrderDate = DateTime.Now.AddDays(-5),
            CustomerId = Guid.NewGuid(),
            CustomerName = "Petr Dvořák",
            CustomerEmail = "<EMAIL>",
            Status = OrderStatus.Processing,
            SubTotal = 8000m,
            TaxAmount = 1680m,
            DiscountPercentage = 5m,
            DiscountAmount = 400m,
            ShippingCost = 200m,
            TotalAmount = 9480m,
            Currency = "CZK",
            Notes = "Rychlé dodání požadováno",
            ShippingAddress = "Karlova 25",
            ShippingCity = "Ostrava",
            ShippingPostalCode = "70200",
            ShippingCountry = "CZ",
            ExpectedDeliveryDate = DateTime.Now.AddDays(2),
            CreatedAt = DateTime.Now.AddDays(-5),
            ModifiedAt = DateTime.Now.AddDays(-4)
        };

        order3.Items = new List<OrderItem>
        {
            new OrderItem
            {
                Id = Guid.NewGuid(),
                OrderId = order3.Id,
                ProductCode = "TABLET-001",
                ProductName = "Tablet Samsung Galaxy",
                ProductDescription = "10.1\" tablet s Android",
                Category = "Elektronika",
                UnitPrice = 8000m,
                Quantity = 1,
                Unit = "ks",
                Weight = 0.5m,
                TaxRate = 21m,
                DiscountPercentage = 5m,
                LineTotal = 7600m,
                LineTaxAmount = 1596m,
                LineTotalWithTax = 9196m,
                IsInStock = true,
                CreatedAt = DateTime.Now.AddDays(-5)
            }
        };

        // Přepočítáme totaly
        order3.SubTotal = order3.Items.Sum(i => i.LineTotal);
        order3.TaxAmount = order3.Items.Sum(i => i.LineTaxAmount);
        order3.TotalAmount = order3.SubTotal + order3.TaxAmount + order3.ShippingCost;

        orders.Add(order3);

        return orders;
    }

    /// <summary>
    /// Vytvoří testovací faktury pro objednávky.
    /// </summary>
    private static List<Invoice> CreateTestInvoices(List<Order> orders)
    {
        var invoices = new List<Invoice>();

        // Faktura pro objednávku 1 - zaplacená
        var invoice1 = new Invoice
        {
            Id = Guid.NewGuid(),
            InvoiceNumber = "FAK-2024-001",
            IssueDate = orders[0].OrderDate.AddDays(1),
            DueDate = orders[0].OrderDate.AddDays(15),
            OrderId = orders[0].Id,
            CustomerId = orders[0].CustomerId,
            CustomerName = orders[0].CustomerName,
            CustomerEmail = orders[0].CustomerEmail,
            CustomerAddress = "Václavské náměstí 1, Praha 11000",
            CustomerTaxId = "********",
            CustomerVatId = "CZ********",
            Type = InvoiceType.Standard,
            Status = InvoiceStatus.Paid,
            SubTotal = orders[0].SubTotal,
            TaxAmount = orders[0].TaxAmount,
            TotalAmount = orders[0].TotalAmount,
            PaidAmount = orders[0].TotalAmount,
            RemainingAmount = 0m,
            Currency = "CZK",
            PaymentDate = orders[0].OrderDate.AddDays(10),
            PaymentMethod = PaymentMethod.BankTransfer,
            VariableSymbol = "2024001",
            CreatedAt = orders[0].OrderDate.AddDays(1),
            ModifiedAt = orders[0].OrderDate.AddDays(10)
        };

        invoice1.Items = orders[0].Items.Select(oi => new InvoiceItem
        {
            Id = Guid.NewGuid(),
            InvoiceId = invoice1.Id,
            ProductCode = oi.ProductCode,
            ProductName = oi.ProductName,
            ProductDescription = oi.ProductDescription,
            UnitPrice = oi.UnitPrice,
            Quantity = oi.Quantity,
            Unit = oi.Unit,
            TaxRate = oi.TaxRate,
            DiscountPercentage = oi.DiscountPercentage,
            LineTotal = oi.LineTotal,
            LineTaxAmount = oi.LineTaxAmount,
            LineTotalWithTax = oi.LineTotalWithTax,
            CreatedAt = invoice1.CreatedAt
        }).ToList();

        invoices.Add(invoice1);

        // Faktura pro objednávku 2 - částečně zaplacená
        var invoice2 = new Invoice
        {
            Id = Guid.NewGuid(),
            InvoiceNumber = "FAK-2024-002",
            IssueDate = orders[1].OrderDate.AddDays(2),
            DueDate = orders[1].OrderDate.AddDays(30),
            OrderId = orders[1].Id,
            CustomerId = orders[1].CustomerId,
            CustomerName = orders[1].CustomerName,
            CustomerEmail = orders[1].CustomerEmail,
            CustomerAddress = "Náměstí Míru 15, Brno 60200",
            CustomerTaxId = "********",
            CustomerVatId = "CZ********",
            Type = InvoiceType.Standard,
            Status = InvoiceStatus.PartiallyPaid,
            SubTotal = orders[1].SubTotal,
            TaxAmount = orders[1].TaxAmount,
            TotalAmount = orders[1].TotalAmount,
            PaidAmount = 25000m,
            RemainingAmount = orders[1].TotalAmount - 25000m,
            Currency = "CZK",
            PaymentMethod = PaymentMethod.BankTransfer,
            VariableSymbol = "2024002",
            CreatedAt = orders[1].OrderDate.AddDays(2),
            ModifiedAt = orders[1].OrderDate.AddDays(5)
        };

        invoice2.Items = orders[1].Items.Select(oi => new InvoiceItem
        {
            Id = Guid.NewGuid(),
            InvoiceId = invoice2.Id,
            ProductCode = oi.ProductCode,
            ProductName = oi.ProductName,
            ProductDescription = oi.ProductDescription,
            UnitPrice = oi.UnitPrice,
            Quantity = oi.Quantity,
            Unit = oi.Unit,
            TaxRate = oi.TaxRate,
            DiscountPercentage = oi.DiscountPercentage,
            LineTotal = oi.LineTotal,
            LineTaxAmount = oi.LineTaxAmount,
            LineTotalWithTax = oi.LineTotalWithTax,
            CreatedAt = invoice2.CreatedAt
        }).ToList();

        invoices.Add(invoice2);

        return invoices;
    }
}
