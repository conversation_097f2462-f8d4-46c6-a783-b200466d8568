using Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations;

/// <summary>
/// Konfigurace Entity Framework pro entitu Order.
/// </summary>
public class OrderConfiguration : IEntityTypeConfiguration<Order>
{
    public void Configure(EntityTypeBuilder<Order> builder)
    {
        // Tabulka
        builder.ToTable("Orders", "dbo");

        // Primární kl<PERSON>č
        builder.HasKey(o => o.Id);

        // Vlastnosti
        builder.Property(o => o.OrderNumber)
            .IsRequired()
            .HasMaxLength(50)
            .HasComment("Číslo objednávky");

        builder.Property(o => o.OrderDate)
            .IsRequired()
            .HasComment("Datum vytvoření objednávky");

        builder.Property(o => o.CustomerId)
            .IsRequired()
            .HasComment("ID zákazníka");

        builder.Property(o => o.CustomerName)
            .IsRequired()
            .HasMaxLength(200)
            .HasComment("Jméno zákazníka");

        builder.Property(o => o.CustomerEmail)
            .IsRequired()
            .HasMaxLength(100)
            .HasComment("Email zákazníka");

        builder.Property(o => o.Status)
            .IsRequired()
            .HasConversion<int>()
            .HasComment("Stav objednávky");

        builder.Property(o => o.SubTotal)
            .IsRequired()
            .HasColumnType("decimal(18,2)")
            .HasComment("Celková částka bez DPH");

        builder.Property(o => o.TaxAmount)
            .IsRequired()
            .HasColumnType("decimal(18,2)")
            .HasComment("Výše DPH");

        builder.Property(o => o.DiscountPercentage)
            .HasColumnType("decimal(5,2)")
            .HasComment("Sleva v procentech");

        builder.Property(o => o.DiscountAmount)
            .HasColumnType("decimal(18,2)")
            .HasComment("Částka slevy");

        builder.Property(o => o.ShippingCost)
            .HasColumnType("decimal(18,2)")
            .HasComment("Poštovné");

        builder.Property(o => o.TotalAmount)
            .IsRequired()
            .HasColumnType("decimal(18,2)")
            .HasComment("Celková částka včetně DPH");

        builder.Property(o => o.Currency)
            .IsRequired()
            .HasMaxLength(3)
            .HasDefaultValue("CZK")
            .HasComment("Měna objednávky");

        builder.Property(o => o.Notes)
            .HasMaxLength(1000)
            .HasComment("Poznámky k objednávce");

        builder.Property(o => o.ShippingAddress)
            .IsRequired()
            .HasMaxLength(500)
            .HasComment("Dodací adresa");

        builder.Property(o => o.ShippingCity)
            .IsRequired()
            .HasMaxLength(100)
            .HasComment("Město dodání");

        builder.Property(o => o.ShippingPostalCode)
            .IsRequired()
            .HasMaxLength(20)
            .HasComment("PSČ dodání");

        builder.Property(o => o.ShippingCountry)
            .IsRequired()
            .HasMaxLength(2)
            .HasDefaultValue("CZ")
            .HasComment("Země dodání");

        builder.Property(o => o.ExpectedDeliveryDate)
            .HasComment("Očekávané datum dodání");

        builder.Property(o => o.ActualDeliveryDate)
            .HasComment("Skutečné datum dodání");

        // Indexy
        builder.HasIndex(o => o.OrderNumber)
            .IsUnique()
            .HasDatabaseName("IX_Orders_OrderNumber");

        builder.HasIndex(o => o.CustomerId)
            .HasDatabaseName("IX_Orders_CustomerId");

        builder.HasIndex(o => o.OrderDate)
            .HasDatabaseName("IX_Orders_OrderDate");

        builder.HasIndex(o => o.Status)
            .HasDatabaseName("IX_Orders_Status");

        // Vztahy
        builder.HasMany(o => o.Items)
            .WithOne(i => i.Order)
            .HasForeignKey(i => i.OrderId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(o => o.Invoices)
            .WithOne(i => i.Order)
            .HasForeignKey(i => i.OrderId)
            .OnDelete(DeleteBehavior.SetNull);

        // Vypočítané vlastnosti (ignored - pouze pro business logiku)
        builder.Ignore(o => o.TotalItemCount);
        builder.Ignore(o => o.TotalWeight);
        builder.Ignore(o => o.IsCompleted);
        builder.Ignore(o => o.IsCancelled);
        builder.Ignore(o => o.DaysFromOrder);
    }
}
