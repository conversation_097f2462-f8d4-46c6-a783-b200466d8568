using Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations;

/// <summary>
/// Konfigurace Entity Framework pro entitu InvoiceItem.
/// </summary>
public class InvoiceItemConfiguration : IEntityTypeConfiguration<InvoiceItem>
{
    public void Configure(EntityTypeBuilder<InvoiceItem> builder)
    {
        // Tabulka
        builder.ToTable("InvoiceItems", "dbo");

        // Primární kl<PERSON>
        builder.HasKey(ii => ii.Id);

        // Vlastnosti
        builder.Property(ii => ii.InvoiceId)
            .IsRequired()
            .HasComment("ID faktury");

        builder.Property(ii => ii.ProductCode)
            .IsRequired()
            .HasMaxLength(50)
            .HasComment("Kód produktu/služby");

        builder.Property(ii => ii.ProductName)
            .IsRequired()
            .HasMaxLength(200)
            .HasComment("Název produktu/služby");

        builder.Property(ii => ii.ProductDescription)
            .HasMaxLength(1000)
            .HasComment("Popis produktu/služby");

        builder.Property(ii => ii.UnitPrice)
            .IsRequired()
            .HasColumnType("decimal(18,2)")
            .HasComment("Jednotková cena bez DPH");

        builder.Property(ii => ii.Quantity)
            .IsRequired()
            .HasColumnType("decimal(10,3)")
            .HasComment("Množství");

        builder.Property(ii => ii.Unit)
            .IsRequired()
            .HasMaxLength(10)
            .HasDefaultValue("ks")
            .HasComment("Jednotka měření");

        builder.Property(ii => ii.TaxRate)
            .IsRequired()
            .HasColumnType("decimal(5,2)")
            .HasDefaultValue(21)
            .HasComment("Sazba DPH v procentech");

        builder.Property(ii => ii.DiscountPercentage)
            .HasColumnType("decimal(5,2)")
            .HasComment("Sleva na položku v procentech");

        builder.Property(ii => ii.LineTotal)
            .IsRequired()
            .HasColumnType("decimal(18,2)")
            .HasComment("Celková cena bez DPH");

        builder.Property(ii => ii.LineTaxAmount)
            .IsRequired()
            .HasColumnType("decimal(18,2)")
            .HasComment("Výše DPH pro tuto položku");

        builder.Property(ii => ii.LineTotalWithTax)
            .IsRequired()
            .HasColumnType("decimal(18,2)")
            .HasComment("Celková cena včetně DPH");

        builder.Property(ii => ii.Notes)
            .HasMaxLength(500)
            .HasComment("Poznámky k položce");

        // Indexy
        builder.HasIndex(ii => ii.InvoiceId)
            .HasDatabaseName("IX_InvoiceItems_InvoiceId");

        builder.HasIndex(ii => ii.ProductCode)
            .HasDatabaseName("IX_InvoiceItems_ProductCode");

        // Vypočítané vlastnosti (ignored - pouze pro business logiku)
        builder.Ignore(ii => ii.DiscountAmount);
        builder.Ignore(ii => ii.IsExpensive);
        builder.Ignore(ii => ii.HasDiscount);
    }
}
