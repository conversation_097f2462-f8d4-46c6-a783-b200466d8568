using Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations;

/// <summary>
/// Konfigurace Entity Framework pro entitu OrderItem.
/// </summary>
public class OrderItemConfiguration : IEntityTypeConfiguration<OrderItem>
{
    public void Configure(EntityTypeBuilder<OrderItem> builder)
    {
        // Tabulka
        builder.ToTable("OrderItems", "dbo");

        // Primární klíč
        builder.HasKey(oi => oi.Id);

        // Vlastnosti
        builder.Property(oi => oi.OrderId)
            .IsRequired()
            .HasComment("ID objednávky");

        builder.Property(oi => oi.ProductCode)
            .IsRequired()
            .HasMaxLength(50)
            .HasComment("Kód produktu");

        builder.Property(oi => oi.ProductName)
            .IsRequired()
            .HasMaxLength(200)
            .HasComment("Název produktu");

        builder.Property(oi => oi.ProductDescription)
            .HasMaxLength(1000)
            .HasComment("Popis produktu");

        builder.Property(oi => oi.Category)
            .IsRequired()
            .HasMaxLength(100)
            .HasComment("Kategorie produktu");

        builder.Property(oi => oi.UnitPrice)
            .IsRequired()
            .HasColumnType("decimal(18,2)")
            .HasComment("Jednotková cena bez DPH");

        builder.Property(oi => oi.Quantity)
            .IsRequired()
            .HasComment("Množství");

        builder.Property(oi => oi.Unit)
            .IsRequired()
            .HasMaxLength(10)
            .HasDefaultValue("ks")
            .HasComment("Jednotka měření");

        builder.Property(oi => oi.Weight)
            .HasColumnType("decimal(10,3)")
            .HasComment("Hmotnost jedné jednotky v kg");

        builder.Property(oi => oi.TaxRate)
            .IsRequired()
            .HasColumnType("decimal(5,2)")
            .HasDefaultValue(21)
            .HasComment("Sazba DPH v procentech");

        builder.Property(oi => oi.DiscountPercentage)
            .HasColumnType("decimal(5,2)")
            .HasComment("Sleva na položku v procentech");

        builder.Property(oi => oi.LineTotal)
            .IsRequired()
            .HasColumnType("decimal(18,2)")
            .HasComment("Celková cena bez DPH");

        builder.Property(oi => oi.LineTaxAmount)
            .IsRequired()
            .HasColumnType("decimal(18,2)")
            .HasComment("Výše DPH pro tuto položku");

        builder.Property(oi => oi.LineTotalWithTax)
            .IsRequired()
            .HasColumnType("decimal(18,2)")
            .HasComment("Celková cena včetně DPH");

        builder.Property(oi => oi.Notes)
            .HasMaxLength(500)
            .HasComment("Poznámky k položce");

        builder.Property(oi => oi.IsInStock)
            .IsRequired()
            .HasDefaultValue(true)
            .HasComment("Určuje, zda je produkt na skladě");

        builder.Property(oi => oi.ExpectedDeliveryDate)
            .HasComment("Očekávané datum dodání této položky");

        // Indexy
        builder.HasIndex(oi => oi.OrderId)
            .HasDatabaseName("IX_OrderItems_OrderId");

        builder.HasIndex(oi => oi.ProductCode)
            .HasDatabaseName("IX_OrderItems_ProductCode");

        builder.HasIndex(oi => oi.Category)
            .HasDatabaseName("IX_OrderItems_Category");

        // Vypočítané vlastnosti (ignored - pouze pro business logiku)
        builder.Ignore(oi => oi.TotalWeight);
        builder.Ignore(oi => oi.DiscountAmount);
        builder.Ignore(oi => oi.IsExpensive);
        builder.Ignore(oi => oi.IsHeavy);
    }
}
