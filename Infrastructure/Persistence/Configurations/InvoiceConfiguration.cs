using Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations;

/// <summary>
/// Konfigurace Entity Framework pro entitu Invoice.
/// </summary>
public class InvoiceConfiguration : IEntityTypeConfiguration<Invoice>
{
    public void Configure(EntityTypeBuilder<Invoice> builder)
    {
        // Tabulka
        builder.ToTable("Invoices", "dbo");

        // Primární klíč
        builder.HasKey(i => i.Id);

        // Vlastnosti
        builder.Property(i => i.InvoiceNumber)
            .IsRequired()
            .HasMaxLength(50)
            .HasComment("Číslo faktury");

        builder.Property(i => i.IssueDate)
            .IsRequired()
            .HasComment("Datum vystavení faktury");

        builder.Property(i => i.DueDate)
            .IsRequired()
            .HasComment("Datum splatnosti");

        builder.Property(i => i.OrderId)
            .HasComment("ID objednávky");

        builder.Property(i => i.CustomerId)
            .IsRequired()
            .HasComment("ID zákazníka");

        builder.Property(i => i.CustomerName)
            .IsRequired()
            .HasMaxLength(200)
            .HasComment("Jméno zákazníka");

        builder.Property(i => i.CustomerEmail)
            .IsRequired()
            .HasMaxLength(100)
            .HasComment("Email zákazníka");

        builder.Property(i => i.CustomerAddress)
            .IsRequired()
            .HasMaxLength(500)
            .HasComment("Adresa zákazníka");

        builder.Property(i => i.CustomerTaxId)
            .HasMaxLength(20)
            .HasComment("IČO zákazníka");

        builder.Property(i => i.CustomerVatId)
            .HasMaxLength(20)
            .HasComment("DIČ zákazníka");

        builder.Property(i => i.Type)
            .IsRequired()
            .HasConversion<int>()
            .HasComment("Typ faktury");

        builder.Property(i => i.Status)
            .IsRequired()
            .HasConversion<int>()
            .HasComment("Stav faktury");

        builder.Property(i => i.SubTotal)
            .IsRequired()
            .HasColumnType("decimal(18,2)")
            .HasComment("Celková částka bez DPH");

        builder.Property(i => i.TaxAmount)
            .IsRequired()
            .HasColumnType("decimal(18,2)")
            .HasComment("Výše DPH");

        builder.Property(i => i.TotalAmount)
            .IsRequired()
            .HasColumnType("decimal(18,2)")
            .HasComment("Celková částka včetně DPH");

        builder.Property(i => i.PaidAmount)
            .HasColumnType("decimal(18,2)")
            .HasComment("Zaplacená částka");

        builder.Property(i => i.RemainingAmount)
            .HasColumnType("decimal(18,2)")
            .HasComment("Zbývající částka k doplacení");

        builder.Property(i => i.Currency)
            .IsRequired()
            .HasMaxLength(3)
            .HasDefaultValue("CZK")
            .HasComment("Měna faktury");

        builder.Property(i => i.Notes)
            .HasMaxLength(1000)
            .HasComment("Poznámky k faktuře");

        builder.Property(i => i.PaymentDate)
            .HasComment("Datum zaplacení");

        builder.Property(i => i.PaymentMethod)
            .IsRequired()
            .HasConversion<int>()
            .HasComment("Způsob platby");

        builder.Property(i => i.VariableSymbol)
            .HasMaxLength(20)
            .HasComment("Variabilní symbol");

        builder.Property(i => i.ConstantSymbol)
            .HasMaxLength(10)
            .HasComment("Konstantní symbol");

        builder.Property(i => i.SpecificSymbol)
            .HasMaxLength(20)
            .HasComment("Specifický symbol");

        // Indexy
        builder.HasIndex(i => i.InvoiceNumber)
            .IsUnique()
            .HasDatabaseName("IX_Invoices_InvoiceNumber");

        builder.HasIndex(i => i.CustomerId)
            .HasDatabaseName("IX_Invoices_CustomerId");

        builder.HasIndex(i => i.OrderId)
            .HasDatabaseName("IX_Invoices_OrderId");

        builder.HasIndex(i => i.IssueDate)
            .HasDatabaseName("IX_Invoices_IssueDate");

        builder.HasIndex(i => i.DueDate)
            .HasDatabaseName("IX_Invoices_DueDate");

        builder.HasIndex(i => i.Status)
            .HasDatabaseName("IX_Invoices_Status");

        // Vztahy
        builder.HasMany(i => i.Items)
            .WithOne(ii => ii.Invoice)
            .HasForeignKey(ii => ii.InvoiceId)
            .OnDelete(DeleteBehavior.Cascade);

        // Vypočítané vlastnosti (ignored - pouze pro business logiku)
        builder.Ignore(i => i.IsPaid);
        builder.Ignore(i => i.IsOverdue);
        builder.Ignore(i => i.DaysOverdue);
        builder.Ignore(i => i.DaysUntilDue);
        builder.Ignore(i => i.IsPartiallyPaid);
        builder.Ignore(i => i.PaymentPercentage);
    }
}
