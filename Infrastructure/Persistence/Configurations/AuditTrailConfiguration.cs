using Domain.System;
using Infrastructure.Conversions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations;

/// <summary>
/// Konfigurační třída pro entitu AuditTrail.
/// Definuje mapování mezi objektovým modelem a databázovým schématem.
/// </summary>
public class AuditTrailConfiguration : IEntityTypeConfiguration<AuditTrail>
{
    /// <summary>
    /// Konfiguruje mapování entity AuditTrail do databáze.
    /// </summary>
    /// <param name="builder">Builder pro konfiguraci entity</param>
    public void Configure(EntityTypeBuilder<AuditTrail> builder)
    {

        builder.Property(x => x.ErrorMessage).HasMaxLength(int.MaxValue); // Případná chybová zpráva
    }
}