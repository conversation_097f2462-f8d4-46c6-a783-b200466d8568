using Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Persistence.Configurations;

/// <summary>
/// Konfigurace entity SampleEntity pro Entity Framework Core.
/// </summary>
public class SampleEntityConfiguration : IEntityTypeConfiguration<SampleEntity>
{
    /// <summary>
    /// Konfiguruje mapování entity SampleEntity na databázovou tabulku.
    /// </summary>
    /// <param name="builder">Builder pro konfiguraci entity.</param>
    public void Configure(EntityTypeBuilder<SampleEntity> builder)
    {
        // Název tabulky
        builder.ToTable("SampleEntity");

        // Primární klíč
        builder.HasKey(e => e.Id);
        builder.Property(e => e.Id)
            .ValueGeneratedOnAdd();

        // Vlastnosti s různými validačními pravidly
        builder.Property(e => e.Name)
            .IsRequired()
            .HasMaxLength(200)
            .HasAnnotation("MinLength", 3)
            .HasAnnotation("Pattern", @"^[A-Za-zÀ-ÿ\s]+$")
            .HasAnnotation("ErrorMessage", "Jméno musí obsahovat pouze písmena a mezery")
            .HasAnnotation("Display", "Jméno")
            .HasAnnotation("Placeholder", "Zadejte jméno...")
            .HasAnnotation("Required", true);

        builder.Property(e => e.Description)
            .HasMaxLength(1000)
            .HasAnnotation("MinLength", 10)
            .HasAnnotation("MaxLength", 1000)
            .HasAnnotation("Pattern", @"^[\w\s\.,!?-]+$")
            .HasAnnotation("ErrorMessage", "Popis může obsahovat pouze písmena, číslice a základní interpunkci")
            .HasAnnotation("Display", "Popis")
            .HasAnnotation("Placeholder", "Zadejte popis entity...")
            .HasAnnotation("Multiline", true)
            .HasAnnotation("Rows", 4);

        builder.Property(e => e.Age)
            .IsRequired()
            .HasAnnotation("Range", "0,150")
            .HasAnnotation("Min", 0)
            .HasAnnotation("Max", 150)
            .HasAnnotation("Step", 1)
            .HasAnnotation("ErrorMessage", "Věk musí být mezi 0 a 150 lety")
            .HasAnnotation("Display", "Věk")
            .HasAnnotation("Placeholder", "Zadejte věk...")
            .HasAnnotation("Required", true)
            .HasAnnotation("Type", "number");

        builder.Property(e => e.DateOfBirth)
            .IsRequired()
            .HasAnnotation("DataType", "Date")
            .HasAnnotation("DisplayFormat", "dd.MM.yyyy")
            .HasAnnotation("ErrorMessage", "Datum narození je povinné")
            .HasAnnotation("Display", "Datum narození")
            .HasAnnotation("Min", "1900-01-01")
            .HasAnnotation("Max", DateTime.Today.ToString("yyyy-MM-dd"))
            .HasAnnotation("Required", true)
            .HasAnnotation("Type", "date");

        builder.Property(e => e.IsActive)
            .IsRequired()
            .HasAnnotation("Display", "Aktivní")
            .HasAnnotation("ErrorMessage", "Stav aktivity musí být specifikován")
            .HasAnnotation("Required", true)
            .HasAnnotation("Type", "checkbox")
            .HasAnnotation("DefaultValue", true);

        // RowVersion konfigurace pro SQLite
        // SQLite nepodporuje automatické timestamp, takže nakonfigurujeme jako běžný byte array
        builder.Property(e => e.RowVersion)
            .IsRequired()
            .IsRowVersion()
            .HasDefaultValue(new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 });

        // Auditovací pole
        builder.Property(e => e.CreatedAt)
            .IsRequired();

        builder.Property(e => e.CreatedBy)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(e => e.ModifiedAt);

        builder.Property(e => e.ModifiedBy)
            .HasMaxLength(100);

        // InternalNotes s validačními pravidly
        builder.Property(e => e.InternalNotes)
            .HasMaxLength(500)
            .HasAnnotation("MinLength", 5)
            .HasAnnotation("MaxLength", 500)
            .HasAnnotation("Pattern", @"^[\w\s\.,!?;:-]+$")
            .HasAnnotation("ErrorMessage", "Interní poznámky mohou obsahovat pouze alfanumerické znaky a základní interpunkci")
            .HasAnnotation("Display", "Interní poznámky")
            .HasAnnotation("Placeholder", "Zadejte interní poznámky...")
            .HasAnnotation("Multiline", true)
            .HasAnnotation("Rows", 3)
            .HasAnnotation("Required", false)
            .HasAnnotation("Confidential", true)
            .HasAnnotation("AccessLevel", "Internal");

        // Entity-level validační pravidla a omezení
        builder.HasAnnotation("EntityValidation", "CustomValidation")
            .HasAnnotation("BusinessRules", new[]
            {
                "Age must be consistent with DateOfBirth",
                "Active entities must have Name",
                "Description required for entities older than 18"
            })
            .HasAnnotation("CrossFieldValidation", new[]
            {
                "Age|DateOfBirth|AgeConsistency",
                "IsActive|Name|ActiveEntityName",
                "Age|Description|AdultDescription"
            })
            .HasAnnotation("ConditionalValidation", new[]
            {
                "IsActive=true:Name.Required=true",
                "Age>18:Description.Required=true",
                "Name.Length>50:Description.Required=true"
            })
            .HasAnnotation("CustomValidators", new[]
            {
                "UniqueNameValidator",
                "DateRangeValidator",
                "BusinessLogicValidator"
            })
            .HasAnnotation("ValidationGroups", new[]
            {
                "Basic",
                "Extended",
                "Administrative"
            })
            .HasAnnotation("ValidationOrder", new[]
            {
                "Required",
                "Format",
                "Range",
                "CrossField",
                "Business"
            });

        // Indexy s validačními omezeními
        builder.HasIndex(e => e.Name)
            .IsUnique()
            .HasDatabaseName("IX_SampleEntity_Name_Unique")
            .HasAnnotation("ErrorMessage", "Jméno musí být jedinečné");

        builder.HasIndex(e => new { e.Name, e.Age })
            .HasDatabaseName("IX_SampleEntity_Name_Age_Composite")
            .HasAnnotation("ErrorMessage", "Kombinace jména a věku musí být jedinečná");

        // Check constraints (pro databáze které je podporují)
        builder.HasCheckConstraint("CK_SampleEntity_Age_Range", "[Age] >= 0 AND [Age] <= 150")
            .HasAnnotation("ErrorMessage", "Věk musí být v rozmezí 0-150 let");

        builder.HasCheckConstraint("CK_SampleEntity_DateOfBirth_Range",
            "[DateOfBirth] >= '1900-01-01' AND [DateOfBirth] <= '2100-12-31'")
            .HasAnnotation("ErrorMessage", "Datum narození musí být mezi 1900 a 2100");

        builder.HasCheckConstraint("CK_SampleEntity_Name_NotEmpty",
            "([IsActive] = 0 OR ([IsActive] = 1 AND [Name] IS NOT NULL AND LENGTH(TRIM([Name])) > 0))")
            .HasAnnotation("ErrorMessage", "Aktivní entity musí mít vyplněné jméno");
    }
}
