using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Application.Abstraction;
using SharedKernel.Domain;

namespace Infrastructure.Persistence.Interceptors;

/// <summary>
/// Interceptor pro automatické nastavování sledovacích polí u entit implementujících ITrackableEntity.
/// Zajišťuje nastavení CreatedAt, CreatedBy, ModifiedAt, ModifiedBy při ukládání změn.
/// </summary>
public class TrackableEntityInterceptor : SaveChangesInterceptor
{
    private readonly ICurrentUserService _currentUserService;

    /// <summary>
    /// Inicializuje novou instanci TrackableEntityInterceptor.
    /// </summary>
    /// <param name="currentUserService">Služba pro získání aktuálního uživatele</param>
    public TrackableEntityInterceptor(ICurrentUserService currentUserService)
    {
        _currentUserService = currentUserService;
    }

    /// <summary>
    /// Asynchronní zpracován<PERSON> před uložením změn.
    /// </summary>
    public override async ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData,
        InterceptionResult<int> result,
        CancellationToken cancellationToken = default)
    {
        var context = eventData.Context;
        if (context == null)
            return result;

        var now = DateTimeOffset.UtcNow;
        var user = _currentUserService.UserId ?? "anonymous";

        // Zpracování entit implementujících ITrackableEntity
        foreach (var entry in context.ChangeTracker.Entries())
        {
            // Kontrola pro ITrackableEntity<int>
            if (entry.Entity is ITrackableEntity<int> trackableInt)
            {
                SetTrackingFields(entry, trackableInt, now, user);
            }
            else
            {
                // Kontrola pro jiné generické parametry pomocí reflexe
                var entityType = entry.Entity.GetType();
                var trackableInterface = entityType.GetInterfaces()
                    .FirstOrDefault(i => i.IsGenericType &&
                                   i.GetGenericTypeDefinition() == typeof(ITrackableEntity<>));

                if (trackableInterface != null)
                {
                    SetTrackingFieldsReflection(entry, now, user);
                }
            }
        }

        return await base.SavingChangesAsync(eventData, result, cancellationToken);
    }

    /// <summary>
    /// Synchronní zpracování před uložením změn.
    /// </summary>
    public override InterceptionResult<int> SavingChanges(
        DbContextEventData eventData,
        InterceptionResult<int> result)
    {
        var context = eventData.Context;
        if (context == null)
            return result;

        var now = DateTimeOffset.UtcNow;
        var user = _currentUserService.UserId ?? "anonymous";

        // Zpracování entit implementujících ITrackableEntity
        foreach (var entry in context.ChangeTracker.Entries())
        {
            // Kontrola pro ITrackableEntity<int>
            if (entry.Entity is ITrackableEntity<int> trackableInt)
            {
                SetTrackingFields(entry, trackableInt, now, user);
            }
            else
            {
                // Kontrola pro jiné generické parametry pomocí reflexe
                var entityType = entry.Entity.GetType();
                var trackableInterface = entityType.GetInterfaces()
                    .FirstOrDefault(i => i.IsGenericType &&
                                   i.GetGenericTypeDefinition() == typeof(ITrackableEntity<>));

                if (trackableInterface != null)
                {
                    SetTrackingFieldsReflection(entry, now, user);
                }
            }
        }

        return base.SavingChanges(eventData, result);
    }

    /// <summary>
    /// Nastavení sledovacích polí pro ITrackableEntity<int>.
    /// </summary>
    private static void SetTrackingFields(Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry entry,
        ITrackableEntity<int> trackable, DateTimeOffset now, string user)
    {
        switch (entry.State)
        {
            case EntityState.Added:
                trackable.CreatedAt = now;
                trackable.CreatedBy = user;
                trackable.ModifiedAt = now;
                trackable.ModifiedBy = user;
                break;
            case EntityState.Modified:
                trackable.ModifiedAt = now;
                trackable.ModifiedBy = user;
                break;
        }
    }

    /// <summary>
    /// Nastavení sledovacích polí pomocí reflexe pro jiné generické parametry.
    /// </summary>
    private static void SetTrackingFieldsReflection(Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry entry,
        DateTimeOffset now, string user)
    {
        var entity = entry.Entity;
        var entityType = entity.GetType();

        switch (entry.State)
        {
            case EntityState.Added:
                SetPropertyValue(entityType, entity, "CreatedAt", now);
                SetPropertyValue(entityType, entity, "CreatedBy", user);
                SetPropertyValue(entityType, entity, "ModifiedAt", now);
                SetPropertyValue(entityType, entity, "ModifiedBy", user);
                break;
            case EntityState.Modified:
                SetPropertyValue(entityType, entity, "ModifiedAt", now);
                SetPropertyValue(entityType, entity, "ModifiedBy", user);
                break;
        }
    }

    /// <summary>
    /// Pomocná metoda pro nastavení hodnoty vlastnosti pomocí reflexe.
    /// </summary>
    private static void SetPropertyValue(Type entityType, object entity, string propertyName, object value)
    {
        var property = entityType.GetProperty(propertyName);
        if (property != null && property.CanWrite)
        {
            property.SetValue(entity, value);
        }
    }
}
