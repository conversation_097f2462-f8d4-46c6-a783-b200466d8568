<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
      <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.7" />
      <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.7" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.7" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.7">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.Identity.Core" Version="9.0.7" />
      <PackageReference Include="Microsoft.Identity.Web" Version="3.10.0" />
      <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
      <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Application\Application.csproj" />
      <ProjectReference Include="..\Domain\Domain.csproj" />
      <ProjectReference Include="..\SharedKernel\SharedKernel.csproj" />
    </ItemGroup>

</Project>
