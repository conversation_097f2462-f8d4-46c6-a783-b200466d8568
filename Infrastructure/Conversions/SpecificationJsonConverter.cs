using System;
using System.Text.Json;
using System.Text.Json.Serialization;
using Application.Features.Generic;

namespace Infrastructure.Conversions;

/// <summary>
/// JSON konvertor pro deserializaci ISpecification<T> objektů z JSON payloadu
/// Umožňuje deserializaci specifikací bez problémů s Expression objekty
/// </summary>
public class SpecificationJsonConverter : JsonConverterFactory
{
    /// <summary>
    /// Určuje, zda konvertor může zpracovat daný typ
    /// </summary>
    public override bool CanConvert(Type typeToConvert)
    {
        if (!typeToConvert.IsGenericType)
            return false;

        var genericTypeDefinition = typeToConvert.GetGenericTypeDefinition();
        return genericTypeDefinition == typeof(ISpecification<>);
    }

    /// <summary>
    /// Vytvoří konvertor pro konkrétní typ
    /// </summary>
    public override JsonConverter CreateConverter(Type typeToConvert, JsonSerializerOptions options)
    {
        var entityType = typeToConvert.GetGenericArguments()[0];
        var converterType = typeof(SpecificationJsonConverter<>).MakeGenericType(entityType);
        
        return (JsonConverter)Activator.CreateInstance(converterType)!;
    }
}

/// <summary>
/// Generický JSON konvertor pro ISpecification<T>
/// </summary>
/// <typeparam name="T">Typ entity</typeparam>
public class SpecificationJsonConverter<T> : JsonConverter<ISpecification<T>> where T : class
{
    /// <summary>
    /// Deserializuje JSON na ISpecification<T>
    /// </summary>
    public override ISpecification<T>? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        // Pokud je hodnota null, vrátíme null
        if (reader.TokenType == JsonTokenType.Null)
        {
            return null;
        }

        // Pokud je to prázdný objekt {}, vrátíme prázdnou specifikaci
        if (reader.TokenType == JsonTokenType.StartObject)
        {
            // Načteme celý JSON objekt
            using var document = JsonDocument.ParseValue(ref reader);
            var root = document.RootElement;

            // Pokud je objekt prázdný, vrátíme prázdnou specifikaci
            if (root.EnumerateObject().Count() == 0)
            {
                return new JsonSpecification<T>();
            }

            try
            {
                // Pokusíme se deserializovat jako JsonSpecification<T>
                var jsonSpec = JsonSerializer.Deserialize<JsonSpecification<T>>(root.GetRawText(), options);
                
                if (jsonSpec != null)
                {
                    // Sestavíme Expression objekty z JSON dat
                    jsonSpec.BuildExpressions();
                    return jsonSpec;
                }
            }
            catch (JsonException ex)
            {
                // Logování chyby (můžeme přidat ILogger později)
                Console.WriteLine($"Chyba při deserializaci specifikace: {ex.Message}");
                
                // Vrátíme prázdnou specifikaci místo vyhození výjimky
                return new JsonSpecification<T>();
            }
        }

        // Pro ostatní případy vrátíme prázdnou specifikaci
        return new JsonSpecification<T>();
    }

    /// <summary>
    /// Serializuje ISpecification<T> do JSON
    /// </summary>
    public override void Write(Utf8JsonWriter writer, ISpecification<T> value, JsonSerializerOptions options)
    {
        if (value == null)
        {
            writer.WriteNullValue();
            return;
        }

        // Pokud je to JsonSpecification, serializujeme ji přímo
        if (value is JsonSpecification<T> jsonSpec)
        {
            JsonSerializer.Serialize(writer, jsonSpec, typeof(JsonSpecification<T>), options);
            return;
        }

        // Pro ostatní implementace ISpecification vytvoříme základní reprezentaci
        writer.WriteStartObject();
        
        writer.WriteNumber("skip", value.Skip);
        writer.WriteNumber("take", value.Take);
        writer.WriteBoolean("isPagingEnabled", value.IsPagingEnabled);
        
        // Expression objekty nemůžeme serializovat, takže je přeskočíme
        writer.WritePropertyName("note");
        writer.WriteStringValue("Expression objekty nelze serializovat do JSON. Použijte JsonSpecification pro plnou funkcionalnost.");
        
        writer.WriteEndObject();
    }
}

/// <summary>
/// Rozšiřující metody pro registraci SpecificationJsonConverter
/// </summary>
public static class SpecificationJsonConverterExtensions
{
    /// <summary>
    /// Přidá SpecificationJsonConverter do JsonSerializerOptions
    /// </summary>
    /// <param name="options">JsonSerializerOptions instance</param>
    /// <returns>JsonSerializerOptions pro fluent API</returns>
    public static JsonSerializerOptions AddSpecificationConverter(this JsonSerializerOptions options)
    {
        options.Converters.Add(new SpecificationJsonConverter());
        return options;
    }
}
