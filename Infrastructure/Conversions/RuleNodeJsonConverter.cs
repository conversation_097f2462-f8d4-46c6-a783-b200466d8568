using System;
using System.Text.Json;
using System.Text.Json.Serialization;
using Infrastructure.RuleEngine;

namespace Infrastructure.Conversions;

/// <summary>
/// JSON konvertor pro polymorfní serializaci a deserializaci RuleNode hierarchie.
/// Používá diskriminátor NodeType pro identifikaci konkrétního typu uzlu.
/// </summary>
public class RuleNodeJsonConverter : JsonConverter<RuleNode>
{
    /// <summary>
    /// Čte JSON a deserializuje ho na odpovídající typ RuleNode.
    /// </summary>
    public override RuleNode Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        // Načteme celý JSON objekt do JsonDocument
        using var document = JsonDocument.ParseValue(ref reader);
        var root = document.RootElement;

        // Získáme diskriminátor NodeType (používáme PascalCase)
        if (!root.TryGetProperty("NodeType", out var nodeTypeElement))
        {
            throw new JsonException("Chybí vlastnost 'NodeType' v RuleNode JSON objektu.");
        }

        var nodeType = nodeTypeElement.GetString();
        
        // Na základě NodeType deserializujeme na správný typ
        // Pro neúplná data vytvoříme základní objekty s výchozími hodnotami
        return nodeType switch
        {
            "Operation" => CreateOperationNode(root, options),
            "SourceValue" => CreateSourceValueNode(root, options),
            "Constant" => CreateConstantNode(root, options),
            "Aggregation" => CreateAggregationNode(root, options),
            "Lookup" => CreateLookupNode(root, options),
            "RelatedAggregation" => CreateRelatedAggregationNode(root, options),
            _ => throw new JsonException($"Neznámý typ RuleNode: {nodeType}")
        };
    }

    /// <summary>
    /// Serializuje RuleNode do JSON včetně diskriminátoru NodeType.
    /// </summary>
    public override void Write(Utf8JsonWriter writer, RuleNode value, JsonSerializerOptions options)
    {
        // Serializujeme objekt podle jeho skutečného typu
        JsonSerializer.Serialize(writer, value, value.GetType(), options);
    }

    /// <summary>
    /// Vytvoří OperationNode s výchozími hodnotami pro neúplná data.
    /// </summary>
    private static OperationNode CreateOperationNode(JsonElement root, JsonSerializerOptions options)
    {
        try
        {
            return JsonSerializer.Deserialize<OperationNode>(root.GetRawText(), options)!;
        }
        catch (JsonException)
        {
            // Pro neúplná data vytvoříme základní objekt
            return new OperationNode
            {
                Operator = OperatorType.Equal, // Výchozí operátor
                Operands = new List<RuleNode>()
            };
        }
    }

    /// <summary>
    /// Vytvoří SourceValueNode s výchozími hodnotami pro neúplná data.
    /// </summary>
    private static SourceValueNode CreateSourceValueNode(JsonElement root, JsonSerializerOptions options)
    {
        try
        {
            return JsonSerializer.Deserialize<SourceValueNode>(root.GetRawText(), options)!;
        }
        catch (JsonException)
        {
            return new SourceValueNode { SourcePath = "Unknown" };
        }
    }

    /// <summary>
    /// Vytvoří ConstantNode s výchozími hodnotami pro neúplná data.
    /// </summary>
    private static ConstantNode CreateConstantNode(JsonElement root, JsonSerializerOptions options)
    {
        try
        {
            return JsonSerializer.Deserialize<ConstantNode>(root.GetRawText(), options)!;
        }
        catch (JsonException)
        {
            return new ConstantNode
            {
                DataType = Infrastructure.RuleEngine.ValueType.String,
                Value = "Unknown"
            };
        }
    }

    /// <summary>
    /// Vytvoří AggregationNode s výchozími hodnotami pro neúplná data.
    /// </summary>
    private static AggregationNode CreateAggregationNode(JsonElement root, JsonSerializerOptions options)
    {
        try
        {
            return JsonSerializer.Deserialize<AggregationNode>(root.GetRawText(), options)!;
        }
        catch (JsonException)
        {
            return new AggregationNode
            {
                AggregationType = AggregationType.Sum,
                CollectionPath = "Unknown"
            };
        }
    }

    /// <summary>
    /// Vytvoří LookupNode s výchozími hodnotami pro neúplná data.
    /// </summary>
    private static LookupNode CreateLookupNode(JsonElement root, JsonSerializerOptions options)
    {
        try
        {
            return JsonSerializer.Deserialize<LookupNode>(root.GetRawText(), options)!;
        }
        catch (JsonException)
        {
            return new LookupNode
            {
                TargetEntityName = "Unknown",
                ReturnFieldPath = "Unknown",
                Condition = new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Boolean, Value = "true" }
            };
        }
    }

    /// <summary>
    /// Vytvoří RelatedAggregationNode s výchozími hodnotami pro neúplná data.
    /// </summary>
    private static RelatedAggregationNode CreateRelatedAggregationNode(JsonElement root, JsonSerializerOptions options)
    {
        try
        {
            return JsonSerializer.Deserialize<RelatedAggregationNode>(root.GetRawText(), options)!;
        }
        catch (JsonException)
        {
            return new RelatedAggregationNode
            {
                SourceEntityName = "Unknown",
                TargetEntityName = "Unknown",
                RelationshipProperty = "Unknown",
                AggregationType = AggregationType.Sum
            };
        }
    }
}
