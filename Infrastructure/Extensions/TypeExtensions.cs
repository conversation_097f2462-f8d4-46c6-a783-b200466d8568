using System.Reflection;

namespace Infrastructure.Extensions;

/// <summary>
/// Extension metody pro práci s typy a reflection.
/// Poskytuje utility funkce pro kontrolu dědičnosti a implementace rozhraní.
/// </summary>
public static class TypeExtensions
{
    /// <summary>
    /// Zkontroluje, zda typ dědí z generické základní třídy.
    /// Například: typeof(MyClass).IsAssignableFromGeneric(typeof(BaseClass<>))
    /// </summary>
    /// <param name="type">Typ k ověření</param>
    /// <param name="genericType">Generický typ základní třídy</param>
    /// <returns>True, pokud typ dědí z generické základní třídy</returns>
    public static bool IsAssignableFromGeneric(this Type type, Type genericType)
    {
        if (type == null || genericType == null)
            return false;

        // Pokud není generický typ, použijeme standardní IsAssignableFrom
        if (!genericType.IsGenericTypeDefinition)
            return genericType.IsAssignableFrom(type);

        // Procházíme hierarchii typů
        var currentType = type;
        while (currentType != null)
        {
            // Zkontrolujeme aktuální typ
            if (currentType.IsGenericType &&
                currentType.GetGenericTypeDefinition() == genericType)
            {
                return true;
            }

            // Přejdeme na základní typ
            currentType = currentType.BaseType;
        }

        // Zkontrolujeme implementovaná rozhraní
        foreach (var interfaceType in type.GetInterfaces())
        {
            if (interfaceType.IsGenericType &&
                interfaceType.GetGenericTypeDefinition() == genericType)
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// Zkontroluje, zda typ implementuje generické rozhraní.
    /// </summary>
    /// <param name="type">Typ k ověření</param>
    /// <param name="genericInterface">Generické rozhraní</param>
    /// <returns>True, pokud typ implementuje generické rozhraní</returns>
    public static bool ImplementsGenericInterface(this Type type, Type genericInterface)
    {
        if (type == null || genericInterface == null || !genericInterface.IsInterface)
            return false;

        return type.GetInterfaces()
            .Any(i => i.IsGenericType && 
                     i.GetGenericTypeDefinition() == genericInterface);
    }

    /// <summary>
    /// Získá generický argument z implementovaného rozhraní.
    /// </summary>
    /// <param name="type">Typ implementující rozhraní</param>
    /// <param name="genericInterface">Generické rozhraní</param>
    /// <param name="argumentIndex">Index generického argumentu (výchozí 0)</param>
    /// <returns>Typ generického argumentu nebo null</returns>
    public static Type? GetGenericArgumentFromInterface(this Type type, Type genericInterface, int argumentIndex = 0)
    {
        var implementedInterface = type.GetInterfaces()
            .FirstOrDefault(i => i.IsGenericType && 
                               i.GetGenericTypeDefinition() == genericInterface);

        if (implementedInterface == null)
            return null;

        var genericArguments = implementedInterface.GetGenericArguments();
        return argumentIndex < genericArguments.Length ? genericArguments[argumentIndex] : null;
    }
}
