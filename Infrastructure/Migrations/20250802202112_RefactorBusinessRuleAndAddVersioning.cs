using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class RefactorBusinessRuleAndAddVersioning : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_BusinessRules_Name",
                table: "BusinessRules");

            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "BusinessRules");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "BusinessRules");

            migrationBuilder.DropColumn(
                name: "ModifiedAt",
                table: "BusinessRules");

            migrationBuilder.DropColumn(
                name: "ModifiedBy",
                table: "BusinessRules");

            migrationBuilder.AlterColumn<byte[]>(
                name: "RowVersion",
                table: "SystemLogs",
                type: "BLOB",
                rowVersion: true,
                nullable: false,
                defaultValue: new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 },
                comment: "Verze řádku pro optimistické <PERSON>",
                oldClrType: typeof(byte[]),
                oldType: "BLOB");

            migrationBuilder.AlterColumn<byte[]>(
                name: "RowVersion",
                table: "SampleEntity",
                type: "BLOB",
                rowVersion: true,
                nullable: false,
                defaultValue: new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 },
                comment: "Verze řádku pro optimistické zamykání",
                oldClrType: typeof(byte[]),
                oldType: "BLOB",
                oldRowVersion: true,
                oldDefaultValue: new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 });

            migrationBuilder.AlterColumn<byte[]>(
                name: "RowVersion",
                schema: "dbo",
                table: "Orders",
                type: "BLOB",
                rowVersion: true,
                nullable: false,
                defaultValue: new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 },
                comment: "Verze řádku pro optimistické zamykání",
                oldClrType: typeof(byte[]),
                oldType: "BLOB");

            migrationBuilder.AlterColumn<byte[]>(
                name: "RowVersion",
                schema: "dbo",
                table: "OrderItems",
                type: "BLOB",
                rowVersion: true,
                nullable: false,
                defaultValue: new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 },
                comment: "Verze řádku pro optimistické zamykání",
                oldClrType: typeof(byte[]),
                oldType: "BLOB");

            migrationBuilder.AlterColumn<byte[]>(
                name: "RowVersion",
                schema: "dbo",
                table: "Invoices",
                type: "BLOB",
                rowVersion: true,
                nullable: false,
                defaultValue: new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 },
                comment: "Verze řádku pro optimistické zamykání",
                oldClrType: typeof(byte[]),
                oldType: "BLOB");

            migrationBuilder.AlterColumn<byte[]>(
                name: "RowVersion",
                schema: "dbo",
                table: "InvoiceItems",
                type: "BLOB",
                rowVersion: true,
                nullable: false,
                defaultValue: new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 },
                comment: "Verze řádku pro optimistické zamykání",
                oldClrType: typeof(byte[]),
                oldType: "BLOB");

            migrationBuilder.AlterColumn<byte[]>(
                name: "RowVersion",
                table: "BusinessRules",
                type: "BLOB",
                rowVersion: true,
                nullable: false,
                comment: "Verze řádku pro optimistické zamykání",
                oldClrType: typeof(byte[]),
                oldType: "BLOB");

            migrationBuilder.AddColumn<DateTime>(
                name: "EffectiveFrom",
                table: "BusinessRules",
                type: "TEXT",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                comment: "Datum a čas, od kterého je tato verze pravidla platná");

            migrationBuilder.AddColumn<DateTime>(
                name: "EffectiveTo",
                table: "BusinessRules",
                type: "TEXT",
                nullable: true,
                comment: "Datum a čas, do kterého je tato verze pravidla platná");

            migrationBuilder.AddColumn<int>(
                name: "Version",
                table: "BusinessRules",
                type: "INTEGER",
                nullable: false,
                defaultValue: 1,
                comment: "Číslo verze pravidla pro podporu verzování");

            migrationBuilder.AlterColumn<byte[]>(
                name: "RowVersion",
                table: "AuditTrails",
                type: "BLOB",
                rowVersion: true,
                nullable: false,
                defaultValue: new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 },
                comment: "Verze řádku pro optimistické zamykání",
                oldClrType: typeof(byte[]),
                oldType: "BLOB");

            migrationBuilder.CreateIndex(
                name: "IX_SampleEntity_Name_Age_Composite",
                table: "SampleEntity",
                columns: new[] { "Name", "Age" });

            migrationBuilder.CreateIndex(
                name: "IX_SampleEntity_Name_Unique",
                table: "SampleEntity",
                column: "Name",
                unique: true);

            migrationBuilder.AddCheckConstraint(
                name: "CK_SampleEntity_Age_Range",
                table: "SampleEntity",
                sql: "[Age] >= 0 AND [Age] <= 150");

            migrationBuilder.AddCheckConstraint(
                name: "CK_SampleEntity_DateOfBirth_Range",
                table: "SampleEntity",
                sql: "[DateOfBirth] >= '1900-01-01' AND [DateOfBirth] <= '2100-12-31'");

            migrationBuilder.AddCheckConstraint(
                name: "CK_SampleEntity_Name_NotEmpty",
                table: "SampleEntity",
                sql: "([IsActive] = 0 OR ([IsActive] = 1 AND [Name] IS NOT NULL AND LENGTH(TRIM([Name])) > 0))");

            migrationBuilder.CreateIndex(
                name: "IX_BusinessRules_EffectiveFrom",
                table: "BusinessRules",
                column: "EffectiveFrom");

            migrationBuilder.CreateIndex(
                name: "IX_BusinessRules_EffectiveTo",
                table: "BusinessRules",
                column: "EffectiveTo");

            migrationBuilder.CreateIndex(
                name: "IX_BusinessRules_Name",
                table: "BusinessRules",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_BusinessRules_Name_Version",
                table: "BusinessRules",
                columns: new[] { "Name", "Version" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_BusinessRules_Version",
                table: "BusinessRules",
                column: "Version");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_SampleEntity_Name_Age_Composite",
                table: "SampleEntity");

            migrationBuilder.DropIndex(
                name: "IX_SampleEntity_Name_Unique",
                table: "SampleEntity");

            migrationBuilder.DropCheckConstraint(
                name: "CK_SampleEntity_Age_Range",
                table: "SampleEntity");

            migrationBuilder.DropCheckConstraint(
                name: "CK_SampleEntity_DateOfBirth_Range",
                table: "SampleEntity");

            migrationBuilder.DropCheckConstraint(
                name: "CK_SampleEntity_Name_NotEmpty",
                table: "SampleEntity");

            migrationBuilder.DropIndex(
                name: "IX_BusinessRules_EffectiveFrom",
                table: "BusinessRules");

            migrationBuilder.DropIndex(
                name: "IX_BusinessRules_EffectiveTo",
                table: "BusinessRules");

            migrationBuilder.DropIndex(
                name: "IX_BusinessRules_Name",
                table: "BusinessRules");

            migrationBuilder.DropIndex(
                name: "IX_BusinessRules_Name_Version",
                table: "BusinessRules");

            migrationBuilder.DropIndex(
                name: "IX_BusinessRules_Version",
                table: "BusinessRules");

            migrationBuilder.DropColumn(
                name: "EffectiveFrom",
                table: "BusinessRules");

            migrationBuilder.DropColumn(
                name: "EffectiveTo",
                table: "BusinessRules");

            migrationBuilder.DropColumn(
                name: "Version",
                table: "BusinessRules");

            migrationBuilder.AlterColumn<byte[]>(
                name: "RowVersion",
                table: "SystemLogs",
                type: "BLOB",
                nullable: false,
                oldClrType: typeof(byte[]),
                oldType: "BLOB",
                oldRowVersion: true,
                oldDefaultValue: new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 },
                oldComment: "Verze řádku pro optimistické zamykání");

            migrationBuilder.AlterColumn<byte[]>(
                name: "RowVersion",
                table: "SampleEntity",
                type: "BLOB",
                rowVersion: true,
                nullable: false,
                defaultValue: new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 },
                oldClrType: typeof(byte[]),
                oldType: "BLOB",
                oldRowVersion: true,
                oldDefaultValue: new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 },
                oldComment: "Verze řádku pro optimistické zamykání");

            migrationBuilder.AlterColumn<byte[]>(
                name: "RowVersion",
                schema: "dbo",
                table: "Orders",
                type: "BLOB",
                nullable: false,
                oldClrType: typeof(byte[]),
                oldType: "BLOB",
                oldRowVersion: true,
                oldDefaultValue: new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 },
                oldComment: "Verze řádku pro optimistické zamykání");

            migrationBuilder.AlterColumn<byte[]>(
                name: "RowVersion",
                schema: "dbo",
                table: "OrderItems",
                type: "BLOB",
                nullable: false,
                oldClrType: typeof(byte[]),
                oldType: "BLOB",
                oldRowVersion: true,
                oldDefaultValue: new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 },
                oldComment: "Verze řádku pro optimistické zamykání");

            migrationBuilder.AlterColumn<byte[]>(
                name: "RowVersion",
                schema: "dbo",
                table: "Invoices",
                type: "BLOB",
                nullable: false,
                oldClrType: typeof(byte[]),
                oldType: "BLOB",
                oldRowVersion: true,
                oldDefaultValue: new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 },
                oldComment: "Verze řádku pro optimistické zamykání");

            migrationBuilder.AlterColumn<byte[]>(
                name: "RowVersion",
                schema: "dbo",
                table: "InvoiceItems",
                type: "BLOB",
                nullable: false,
                oldClrType: typeof(byte[]),
                oldType: "BLOB",
                oldRowVersion: true,
                oldDefaultValue: new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 },
                oldComment: "Verze řádku pro optimistické zamykání");

            migrationBuilder.AlterColumn<byte[]>(
                name: "RowVersion",
                table: "BusinessRules",
                type: "BLOB",
                nullable: false,
                oldClrType: typeof(byte[]),
                oldType: "BLOB",
                oldRowVersion: true,
                oldComment: "Verze řádku pro optimistické zamykání");

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "BusinessRules",
                type: "TEXT",
                nullable: true,
                comment: "Datum a čas vytvoření záznamu");

            migrationBuilder.AddColumn<string>(
                name: "CreatedBy",
                table: "BusinessRules",
                type: "TEXT",
                maxLength: 100,
                nullable: true,
                comment: "Identifikátor uživatele, který záznam vytvořil");

            migrationBuilder.AddColumn<DateTime>(
                name: "ModifiedAt",
                table: "BusinessRules",
                type: "TEXT",
                nullable: true,
                comment: "Datum a čas poslední aktualizace záznamu");

            migrationBuilder.AddColumn<string>(
                name: "ModifiedBy",
                table: "BusinessRules",
                type: "TEXT",
                maxLength: 100,
                nullable: true,
                comment: "Identifikátor uživatele, který záznam naposledy aktualizoval");

            migrationBuilder.AlterColumn<byte[]>(
                name: "RowVersion",
                table: "AuditTrails",
                type: "BLOB",
                nullable: false,
                oldClrType: typeof(byte[]),
                oldType: "BLOB",
                oldRowVersion: true,
                oldDefaultValue: new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 },
                oldComment: "Verze řádku pro optimistické zamykání");

            migrationBuilder.CreateIndex(
                name: "IX_BusinessRules_Name",
                table: "BusinessRules",
                column: "Name",
                unique: true);
        }
    }
}
