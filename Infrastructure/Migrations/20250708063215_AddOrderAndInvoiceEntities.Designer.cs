// <auto-generated />
using System;
using Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Infrastructure.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250708063215_AddOrderAndInvoiceEntities")]
    partial class AddOrderAndInvoiceEntities
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.6");

            modelBuilder.Entity("Domain.Entities.Invoice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<string>("ConstantSymbol")
                        .HasMaxLength(10)
                        .HasColumnType("TEXT")
                        .HasComment("Konstantní symbol");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(3)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("CZK")
                        .HasComment("Měna faktury");

                    b.Property<string>("CustomerAddress")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT")
                        .HasComment("Adresa zákazníka");

                    b.Property<string>("CustomerEmail")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasComment("Email zákazníka");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("TEXT")
                        .HasComment("ID zákazníka");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT")
                        .HasComment("Jméno zákazníka");

                    b.Property<string>("CustomerTaxId")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasComment("IČO zákazníka");

                    b.Property<string>("CustomerVatId")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasComment("DIČ zákazníka");

                    b.Property<DateTime>("DueDate")
                        .HasColumnType("TEXT")
                        .HasComment("Datum splatnosti");

                    b.Property<string>("InvoiceNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasComment("Číslo faktury");

                    b.Property<DateTime>("IssueDate")
                        .HasColumnType("TEXT")
                        .HasComment("Datum vystavení faktury");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT")
                        .HasComment("Poznámky k faktuře");

                    b.Property<Guid?>("OrderId")
                        .HasColumnType("TEXT")
                        .HasComment("ID objednávky");

                    b.Property<decimal>("PaidAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Zaplacená částka");

                    b.Property<DateTime?>("PaymentDate")
                        .HasColumnType("TEXT")
                        .HasComment("Datum zaplacení");

                    b.Property<int>("PaymentMethod")
                        .HasColumnType("INTEGER")
                        .HasComment("Způsob platby");

                    b.Property<decimal>("RemainingAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Zbývající částka k doplacení");

                    b.Property<byte[]>("RowVersion")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<string>("SpecificSymbol")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasComment("Specifický symbol");

                    b.Property<int>("Status")
                        .HasColumnType("INTEGER")
                        .HasComment("Stav faktury");

                    b.Property<decimal>("SubTotal")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Celková částka bez DPH");

                    b.Property<decimal>("TaxAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Výše DPH");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Celková částka včetně DPH");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER")
                        .HasComment("Typ faktury");

                    b.Property<string>("VariableSymbol")
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasComment("Variabilní symbol");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId")
                        .HasDatabaseName("IX_Invoices_CustomerId");

                    b.HasIndex("DueDate")
                        .HasDatabaseName("IX_Invoices_DueDate");

                    b.HasIndex("InvoiceNumber")
                        .IsUnique()
                        .HasDatabaseName("IX_Invoices_InvoiceNumber");

                    b.HasIndex("IssueDate")
                        .HasDatabaseName("IX_Invoices_IssueDate");

                    b.HasIndex("OrderId")
                        .HasDatabaseName("IX_Invoices_OrderId");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_Invoices_Status");

                    b.ToTable("Invoices", "dbo");
                });

            modelBuilder.Entity("Domain.Entities.InvoiceItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("DiscountPercentage")
                        .HasColumnType("decimal(5,2)")
                        .HasComment("Sleva na položku v procentech");

                    b.Property<Guid>("InvoiceId")
                        .HasColumnType("TEXT")
                        .HasComment("ID faktury");

                    b.Property<decimal>("LineTaxAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Výše DPH pro tuto položku");

                    b.Property<decimal>("LineTotal")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Celková cena bez DPH");

                    b.Property<decimal>("LineTotalWithTax")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Celková cena včetně DPH");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT")
                        .HasComment("Poznámky k položce");

                    b.Property<string>("ProductCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasComment("Kód produktu/služby");

                    b.Property<string>("ProductDescription")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT")
                        .HasComment("Popis produktu/služby");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT")
                        .HasComment("Název produktu/služby");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(10,3)")
                        .HasComment("Množství");

                    b.Property<byte[]>("RowVersion")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<decimal>("TaxRate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(5,2)")
                        .HasDefaultValue(21m)
                        .HasComment("Sazba DPH v procentech");

                    b.Property<string>("Unit")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("ks")
                        .HasComment("Jednotka měření");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Jednotková cena bez DPH");

                    b.HasKey("Id");

                    b.HasIndex("InvoiceId")
                        .HasDatabaseName("IX_InvoiceItems_InvoiceId");

                    b.HasIndex("ProductCode")
                        .HasDatabaseName("IX_InvoiceItems_ProductCode");

                    b.ToTable("InvoiceItems", "dbo");
                });

            modelBuilder.Entity("Domain.Entities.Order", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("ActualDeliveryDate")
                        .HasColumnType("TEXT")
                        .HasComment("Skutečné datum dodání");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(3)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("CZK")
                        .HasComment("Měna objednávky");

                    b.Property<string>("CustomerEmail")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasComment("Email zákazníka");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("TEXT")
                        .HasComment("ID zákazníka");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT")
                        .HasComment("Jméno zákazníka");

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Částka slevy");

                    b.Property<decimal>("DiscountPercentage")
                        .HasColumnType("decimal(5,2)")
                        .HasComment("Sleva v procentech");

                    b.Property<DateTime?>("ExpectedDeliveryDate")
                        .HasColumnType("TEXT")
                        .HasComment("Očekávané datum dodání");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT")
                        .HasComment("Poznámky k objednávce");

                    b.Property<DateTime>("OrderDate")
                        .HasColumnType("TEXT")
                        .HasComment("Datum vytvoření objednávky");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasComment("Číslo objednávky");

                    b.Property<byte[]>("RowVersion")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<string>("ShippingAddress")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT")
                        .HasComment("Dodací adresa");

                    b.Property<string>("ShippingCity")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasComment("Město dodání");

                    b.Property<decimal>("ShippingCost")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Poštovné");

                    b.Property<string>("ShippingCountry")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(2)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("CZ")
                        .HasComment("Země dodání");

                    b.Property<string>("ShippingPostalCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasComment("PSČ dodání");

                    b.Property<int>("Status")
                        .HasColumnType("INTEGER")
                        .HasComment("Stav objednávky");

                    b.Property<decimal>("SubTotal")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Celková částka bez DPH");

                    b.Property<decimal>("TaxAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Výše DPH");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Celková částka včetně DPH");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId")
                        .HasDatabaseName("IX_Orders_CustomerId");

                    b.HasIndex("OrderDate")
                        .HasDatabaseName("IX_Orders_OrderDate");

                    b.HasIndex("OrderNumber")
                        .IsUnique()
                        .HasDatabaseName("IX_Orders_OrderNumber");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_Orders_Status");

                    b.ToTable("Orders", "dbo");
                });

            modelBuilder.Entity("Domain.Entities.OrderItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasComment("Kategorie produktu");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("TEXT");

                    b.Property<decimal>("DiscountPercentage")
                        .HasColumnType("decimal(5,2)")
                        .HasComment("Sleva na položku v procentech");

                    b.Property<DateTime?>("ExpectedDeliveryDate")
                        .HasColumnType("TEXT")
                        .HasComment("Očekávané datum dodání této položky");

                    b.Property<bool>("IsInStock")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(true)
                        .HasComment("Určuje, zda je produkt na skladě");

                    b.Property<decimal>("LineTaxAmount")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Výše DPH pro tuto položku");

                    b.Property<decimal>("LineTotal")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Celková cena bez DPH");

                    b.Property<decimal>("LineTotalWithTax")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Celková cena včetně DPH");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT")
                        .HasComment("Poznámky k položce");

                    b.Property<Guid>("OrderId")
                        .HasColumnType("TEXT")
                        .HasComment("ID objednávky");

                    b.Property<string>("ProductCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasComment("Kód produktu");

                    b.Property<string>("ProductDescription")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT")
                        .HasComment("Popis produktu");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT")
                        .HasComment("Název produktu");

                    b.Property<int>("Quantity")
                        .HasColumnType("INTEGER")
                        .HasComment("Množství");

                    b.Property<byte[]>("RowVersion")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<decimal>("TaxRate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(5,2)")
                        .HasDefaultValue(21m)
                        .HasComment("Sazba DPH v procentech");

                    b.Property<string>("Unit")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("ks")
                        .HasComment("Jednotka měření");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(18,2)")
                        .HasComment("Jednotková cena bez DPH");

                    b.Property<decimal>("Weight")
                        .HasColumnType("decimal(10,3)")
                        .HasComment("Hmotnost jedné jednotky v kg");

                    b.HasKey("Id");

                    b.HasIndex("Category")
                        .HasDatabaseName("IX_OrderItems_Category");

                    b.HasIndex("OrderId")
                        .HasDatabaseName("IX_OrderItems_OrderId");

                    b.HasIndex("ProductCode")
                        .HasDatabaseName("IX_OrderItems_ProductCode");

                    b.ToTable("OrderItems", "dbo");
                });

            modelBuilder.Entity("Domain.Entities.SampleEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("Age")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateOfBirth")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("InternalNotes")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("BLOB")
                        .HasDefaultValue(new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 });

                    b.HasKey("Id");

                    b.ToTable("SampleEntity", (string)null);
                });

            modelBuilder.Entity("Domain.Identity.UserProfile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.ToTable("UserProfiles");
                });

            modelBuilder.Entity("Domain.System.AuditTrail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ChangesJson")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("EntityId")
                        .HasColumnType("TEXT");

                    b.Property<string>("EntityName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2147483647)
                        .HasColumnType("TEXT");

                    b.Property<string>("Operation")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("RowVersion")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<string>("TableName")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("AuditTrails");
                });

            modelBuilder.Entity("Domain.System.SystemLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ClientAgent")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClientIP")
                        .HasColumnType("TEXT");

                    b.Property<string>("Exception")
                        .HasColumnType("TEXT");

                    b.Property<string>("Level")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("LogEvent")
                        .HasColumnType("TEXT");

                    b.Property<string>("Message")
                        .HasColumnType("TEXT");

                    b.Property<string>("MessageTemplate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Properties")
                        .HasColumnType("TEXT");

                    b.Property<byte[]>("RowVersion")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<DateTime>("TimeStamp")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserName")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("SystemLogs");
                });

            modelBuilder.Entity("Infrastructure.Identity.ApplicationUser", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("TEXT");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("TEXT");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("Infrastructure.RuleEngine.BusinessRule", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasComment("Datum a čas vytvoření záznamu");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasComment("Identifikátor uživatele, který záznam vytvořil");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT")
                        .HasComment("Popis účelu a použití pravidla");

                    b.Property<string>("InternalNotes")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT")
                        .HasComment("Interní poznámky pro vývojáře");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(true)
                        .HasComment("Určuje, zda je pravidlo aktivní");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("TEXT")
                        .HasComment("Datum a čas poslední aktualizace záznamu");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasComment("Identifikátor uživatele, který záznam naposledy aktualizoval");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT")
                        .HasComment("Název obchodního pravidla");

                    b.Property<string>("RootNode")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasComment("Kořenový uzel pravidla serializovaný jako JSON");

                    b.Property<byte[]>("RowVersion")
                        .IsRequired()
                        .HasColumnType("BLOB");

                    b.Property<string>("SchemaVersion")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("TEXT")
                        .HasDefaultValue("1.0")
                        .HasComment("Verze schématu pravidla pro kompatibilitu");

                    b.Property<string>("TargetEntityName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasComment("Název cílové entity pro aplikaci pravidla");

                    b.Property<string>("TargetProperty")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasComment("Název vlastnosti cílové entity pro výsledek pravidla");

                    b.HasKey("Id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_BusinessRules_IsActive");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("IX_BusinessRules_Name");

                    b.HasIndex("TargetEntityName")
                        .HasDatabaseName("IX_BusinessRules_TargetEntityName");

                    b.HasIndex("TargetEntityName", "IsActive")
                        .HasDatabaseName("IX_BusinessRules_TargetEntity_Active");

                    b.ToTable("BusinessRules", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ClaimType")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("TEXT");

                    b.Property<int>("RoleId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ClaimType")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("TEXT");

                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("RoleId")
                        .HasColumnType("INTEGER");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasColumnType("TEXT");

                    b.Property<string>("Value")
                        .HasColumnType("TEXT");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("Domain.Entities.Invoice", b =>
                {
                    b.HasOne("Domain.Entities.Order", "Order")
                        .WithMany("Invoices")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Order");
                });

            modelBuilder.Entity("Domain.Entities.InvoiceItem", b =>
                {
                    b.HasOne("Domain.Entities.Invoice", "Invoice")
                        .WithMany("Items")
                        .HasForeignKey("InvoiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Invoice");
                });

            modelBuilder.Entity("Domain.Entities.OrderItem", b =>
                {
                    b.HasOne("Domain.Entities.Order", "Order")
                        .WithMany("Items")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Order");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole<int>", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.HasOne("Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.HasOne("Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole<int>", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.HasOne("Infrastructure.Identity.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Domain.Entities.Invoice", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("Domain.Entities.Order", b =>
                {
                    b.Navigation("Invoices");

                    b.Navigation("Items");
                });
#pragma warning restore 612, 618
        }
    }
}
