using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddOrderAndInvoiceEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "dbo");

            migrationBuilder.CreateTable(
                name: "Orders",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    OrderNumber = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false, comment: "<PERSON><PERSON><PERSON> objedn<PERSON>vky"),
                    OrderDate = table.Column<DateTime>(type: "TEXT", nullable: false, comment: "Datum vytvoření objednávky"),
                    CustomerId = table.Column<Guid>(type: "TEXT", nullable: false, comment: "ID zákazníka"),
                    CustomerName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false, comment: "Jméno zákazníka"),
                    CustomerEmail = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false, comment: "Email zákazníka"),
                    Status = table.Column<int>(type: "INTEGER", nullable: false, comment: "Stav objednávky"),
                    SubTotal = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "Celková částka bez DPH"),
                    TaxAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "Výše DPH"),
                    DiscountPercentage = table.Column<decimal>(type: "decimal(5,2)", nullable: false, comment: "Sleva v procentech"),
                    DiscountAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "Částka slevy"),
                    ShippingCost = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "Poštovné"),
                    TotalAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "Celková částka včetně DPH"),
                    Currency = table.Column<string>(type: "TEXT", maxLength: 3, nullable: false, defaultValue: "CZK", comment: "Měna objednávky"),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true, comment: "Poznámky k objednávce"),
                    ShippingAddress = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false, comment: "Dodací adresa"),
                    ShippingCity = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false, comment: "Město dodání"),
                    ShippingPostalCode = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false, comment: "PSČ dodání"),
                    ShippingCountry = table.Column<string>(type: "TEXT", maxLength: 2, nullable: false, defaultValue: "CZ", comment: "Země dodání"),
                    ExpectedDeliveryDate = table.Column<DateTime>(type: "TEXT", nullable: true, comment: "Očekávané datum dodání"),
                    ActualDeliveryDate = table.Column<DateTime>(type: "TEXT", nullable: true, comment: "Skutečné datum dodání"),
                    RowVersion = table.Column<byte[]>(type: "BLOB", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    ModifiedBy = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Orders", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Invoices",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    InvoiceNumber = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false, comment: "Číslo faktury"),
                    IssueDate = table.Column<DateTime>(type: "TEXT", nullable: false, comment: "Datum vystavení faktury"),
                    DueDate = table.Column<DateTime>(type: "TEXT", nullable: false, comment: "Datum splatnosti"),
                    OrderId = table.Column<Guid>(type: "TEXT", nullable: true, comment: "ID objednávky"),
                    CustomerId = table.Column<Guid>(type: "TEXT", nullable: false, comment: "ID zákazníka"),
                    CustomerName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false, comment: "Jméno zákazníka"),
                    CustomerEmail = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false, comment: "Email zákazníka"),
                    CustomerAddress = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false, comment: "Adresa zákazníka"),
                    CustomerTaxId = table.Column<string>(type: "TEXT", maxLength: 20, nullable: true, comment: "IČO zákazníka"),
                    CustomerVatId = table.Column<string>(type: "TEXT", maxLength: 20, nullable: true, comment: "DIČ zákazníka"),
                    Type = table.Column<int>(type: "INTEGER", nullable: false, comment: "Typ faktury"),
                    Status = table.Column<int>(type: "INTEGER", nullable: false, comment: "Stav faktury"),
                    SubTotal = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "Celková částka bez DPH"),
                    TaxAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "Výše DPH"),
                    TotalAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "Celková částka včetně DPH"),
                    PaidAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "Zaplacená částka"),
                    RemainingAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "Zbývající částka k doplacení"),
                    Currency = table.Column<string>(type: "TEXT", maxLength: 3, nullable: false, defaultValue: "CZK", comment: "Měna faktury"),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true, comment: "Poznámky k faktuře"),
                    PaymentDate = table.Column<DateTime>(type: "TEXT", nullable: true, comment: "Datum zaplacení"),
                    PaymentMethod = table.Column<int>(type: "INTEGER", nullable: false, comment: "Způsob platby"),
                    VariableSymbol = table.Column<string>(type: "TEXT", maxLength: 20, nullable: true, comment: "Variabilní symbol"),
                    ConstantSymbol = table.Column<string>(type: "TEXT", maxLength: 10, nullable: true, comment: "Konstantní symbol"),
                    SpecificSymbol = table.Column<string>(type: "TEXT", maxLength: 20, nullable: true, comment: "Specifický symbol"),
                    RowVersion = table.Column<byte[]>(type: "BLOB", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    ModifiedBy = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Invoices", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Invoices_Orders_OrderId",
                        column: x => x.OrderId,
                        principalSchema: "dbo",
                        principalTable: "Orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "OrderItems",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    OrderId = table.Column<Guid>(type: "TEXT", nullable: false, comment: "ID objednávky"),
                    ProductCode = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false, comment: "Kód produktu"),
                    ProductName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false, comment: "Název produktu"),
                    ProductDescription = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true, comment: "Popis produktu"),
                    Category = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false, comment: "Kategorie produktu"),
                    UnitPrice = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "Jednotková cena bez DPH"),
                    Quantity = table.Column<int>(type: "INTEGER", nullable: false, comment: "Množství"),
                    Unit = table.Column<string>(type: "TEXT", maxLength: 10, nullable: false, defaultValue: "ks", comment: "Jednotka měření"),
                    Weight = table.Column<decimal>(type: "decimal(10,3)", nullable: false, comment: "Hmotnost jedné jednotky v kg"),
                    TaxRate = table.Column<decimal>(type: "decimal(5,2)", nullable: false, defaultValue: 21m, comment: "Sazba DPH v procentech"),
                    DiscountPercentage = table.Column<decimal>(type: "decimal(5,2)", nullable: false, comment: "Sleva na položku v procentech"),
                    LineTotal = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "Celková cena bez DPH"),
                    LineTaxAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "Výše DPH pro tuto položku"),
                    LineTotalWithTax = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "Celková cena včetně DPH"),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true, comment: "Poznámky k položce"),
                    IsInStock = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true, comment: "Určuje, zda je produkt na skladě"),
                    ExpectedDeliveryDate = table.Column<DateTime>(type: "TEXT", nullable: true, comment: "Očekávané datum dodání této položky"),
                    RowVersion = table.Column<byte[]>(type: "BLOB", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    ModifiedBy = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrderItems", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OrderItems_Orders_OrderId",
                        column: x => x.OrderId,
                        principalSchema: "dbo",
                        principalTable: "Orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "InvoiceItems",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    InvoiceId = table.Column<Guid>(type: "TEXT", nullable: false, comment: "ID faktury"),
                    ProductCode = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false, comment: "Kód produktu/služby"),
                    ProductName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false, comment: "Název produktu/služby"),
                    ProductDescription = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true, comment: "Popis produktu/služby"),
                    UnitPrice = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "Jednotková cena bez DPH"),
                    Quantity = table.Column<decimal>(type: "decimal(10,3)", nullable: false, comment: "Množství"),
                    Unit = table.Column<string>(type: "TEXT", maxLength: 10, nullable: false, defaultValue: "ks", comment: "Jednotka měření"),
                    TaxRate = table.Column<decimal>(type: "decimal(5,2)", nullable: false, defaultValue: 21m, comment: "Sazba DPH v procentech"),
                    DiscountPercentage = table.Column<decimal>(type: "decimal(5,2)", nullable: false, comment: "Sleva na položku v procentech"),
                    LineTotal = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "Celková cena bez DPH"),
                    LineTaxAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "Výše DPH pro tuto položku"),
                    LineTotalWithTax = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "Celková cena včetně DPH"),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true, comment: "Poznámky k položce"),
                    RowVersion = table.Column<byte[]>(type: "BLOB", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", nullable: true),
                    ModifiedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    ModifiedBy = table.Column<string>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_InvoiceItems", x => x.Id);
                    table.ForeignKey(
                        name: "FK_InvoiceItems_Invoices_InvoiceId",
                        column: x => x.InvoiceId,
                        principalSchema: "dbo",
                        principalTable: "Invoices",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceItems_InvoiceId",
                schema: "dbo",
                table: "InvoiceItems",
                column: "InvoiceId");

            migrationBuilder.CreateIndex(
                name: "IX_InvoiceItems_ProductCode",
                schema: "dbo",
                table: "InvoiceItems",
                column: "ProductCode");

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_CustomerId",
                schema: "dbo",
                table: "Invoices",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_DueDate",
                schema: "dbo",
                table: "Invoices",
                column: "DueDate");

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_InvoiceNumber",
                schema: "dbo",
                table: "Invoices",
                column: "InvoiceNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_IssueDate",
                schema: "dbo",
                table: "Invoices",
                column: "IssueDate");

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_OrderId",
                schema: "dbo",
                table: "Invoices",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_Status",
                schema: "dbo",
                table: "Invoices",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_OrderItems_Category",
                schema: "dbo",
                table: "OrderItems",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_OrderItems_OrderId",
                schema: "dbo",
                table: "OrderItems",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_OrderItems_ProductCode",
                schema: "dbo",
                table: "OrderItems",
                column: "ProductCode");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_CustomerId",
                schema: "dbo",
                table: "Orders",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_OrderDate",
                schema: "dbo",
                table: "Orders",
                column: "OrderDate");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_OrderNumber",
                schema: "dbo",
                table: "Orders",
                column: "OrderNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Orders_Status",
                schema: "dbo",
                table: "Orders",
                column: "Status");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "InvoiceItems",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "OrderItems",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "Invoices",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "Orders",
                schema: "dbo");
        }
    }
}
