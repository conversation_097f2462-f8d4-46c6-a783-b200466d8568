using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddBusinessRuleEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "BusinessRules",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    Name = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false, comment: "Název obchodního pravidla"),
                    Description = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true, comment: "Popis účelu a použití pravidla"),
                    TargetEntityName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false, comment: "Název cílové entity pro aplikaci pravidla"),
                    TargetProperty = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true, comment: "Název vlastnosti cílové entity pro výsledek pravidla"),
                    SchemaVersion = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false, defaultValue: "1.0", comment: "Verze schématu pravidla pro kompatibilitu"),
                    RootNode = table.Column<string>(type: "TEXT", nullable: false, comment: "Kořenový uzel pravidla serializovaný jako JSON"),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true, comment: "Určuje, zda je pravidlo aktivní"),
                    InternalNotes = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true, comment: "Interní poznámky pro vývojáře"),
                    RowVersion = table.Column<byte[]>(type: "BLOB", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: true, comment: "Datum a čas vytvoření záznamu"),
                    CreatedBy = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true, comment: "Identifikátor uživatele, který záznam vytvořil"),
                    ModifiedAt = table.Column<DateTime>(type: "TEXT", nullable: true, comment: "Datum a čas poslední aktualizace záznamu"),
                    ModifiedBy = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true, comment: "Identifikátor uživatele, který záznam naposledy aktualizoval")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BusinessRules", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_BusinessRules_IsActive",
                table: "BusinessRules",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_BusinessRules_Name",
                table: "BusinessRules",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_BusinessRules_TargetEntity_Active",
                table: "BusinessRules",
                columns: new[] { "TargetEntityName", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_BusinessRules_TargetEntityName",
                table: "BusinessRules",
                column: "TargetEntityName");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BusinessRules");
        }
    }
}
