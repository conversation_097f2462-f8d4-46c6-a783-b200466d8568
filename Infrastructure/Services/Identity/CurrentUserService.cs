using System.Security.Claims;
using Application.Abstraction;
using Domain.Identity;
using Microsoft.AspNetCore.Http;

namespace Infrastructure.Services.Identity;

public class CurrentUserService : ICurrentUserService
{
    private readonly IHttpContextAccessor _httpContext;

    public CurrentUserService(IHttpContextAccessor httpContext)
    {
        _httpContext = httpContext;
    }

    private ClaimsPrincipal? User => _httpContext.HttpContext?.User;

    public string? UserId =>
        User?.FindFirstValue(ClaimTypes.NameIdentifier);

    public string? Email =>
        User?.FindFirstValue(ClaimTypes.Email);

    public IEnumerable<string> Roles =>
        User?
            .FindAll(ClaimTypes.Role)
            .Select(c => c.Value)
        ?? Enumerable.Empty<string>();

    public UserProfile? Profile => null; // TODO: Implementovat získání profilu přes separátní službu
}