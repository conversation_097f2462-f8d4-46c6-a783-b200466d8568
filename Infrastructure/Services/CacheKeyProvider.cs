using Microsoft.Extensions.Configuration;
using Application.Abstraction;

namespace Infrastructure.Services;

/// <summary>
/// Implementace ICacheKeyProvider s konfigurovatelným prefixem.
/// Prefix se načítá z konfigurace aplikace.
/// </summary>
public class CacheKeyProvider : ICacheKeyProvider
{
    private readonly string _appPrefix;
    private readonly string _separator;

    /// <summary>
    /// Konstruktor načte konfiguraci cache prefixu.
    /// </summary>
    /// <param name="configuration">Konfigurace aplikace</param>
    public CacheKeyProvider(IConfiguration configuration)
    {
        _appPrefix = configuration["Cache:Prefix"] ?? "DataCapture";
        _separator = configuration["Cache:Separator"] ?? "_";
    }

    /// <summary>
    /// Prefix aplikace pro cache klíče.
    /// </summary>
    public string AppPrefix => _appPrefix;

    /// <summary>
    /// Separator používaný v cache klíčích.
    /// </summary>
    public string Separator => _separator;

    /// <summary>
    /// Vytvoří cache klíč s aplikačním prefixem.
    /// </summary>
    /// <param name="key">Základní klíč</param>
    /// <returns>Kompletní cache klíč s prefixem</returns>
    public string WithPrefix(string key) => $"{AppPrefix}{Separator}{key}";

    /// <summary>
    /// Vytvoří cache klíč pro entitu.
    /// </summary>
    /// <param name="entityName">Název entity</param>
    /// <param name="operation">Typ operace (GetAll, GetPaged, atd.)</param>
    /// <returns>Cache klíč pro entitu</returns>
    public string ForEntity(string entityName, string operation) 
        => WithPrefix($"{operation}{Separator}{entityName}");

    /// <summary>
    /// Vytvoří cache klíč pro stránkovaný dotaz.
    /// </summary>
    /// <param name="entityName">Název entity</param>
    /// <param name="pageNumber">Číslo stránky</param>
    /// <param name="pageSize">Velikost stránky</param>
    /// <param name="sortBy">Řazení podle</param>
    /// <param name="sortDescending">Sestupné řazení</param>
    /// <returns>Cache klíč pro stránkovaný dotaz</returns>
    public string ForPagedQuery(string entityName, int pageNumber, int pageSize, 
        string? sortBy = null, bool sortDescending = false)
    {
        var key = $"GetPaged{Separator}{entityName}{Separator}Page{pageNumber}{Separator}Size{pageSize}";
        
        if (!string.IsNullOrEmpty(sortBy))
        {
            key += $"{Separator}Sort{sortBy}";
            if (sortDescending)
                key += "Desc";
        }
        
        return WithPrefix(key);
    }

    /// <summary>
    /// Vytvoří cache klíč pro uživatelská data.
    /// </summary>
    /// <param name="userId">ID uživatele</param>
    /// <param name="dataType">Typ dat</param>
    /// <returns>Cache klíč pro uživatelská data</returns>
    public string ForUser(string userId, string dataType) 
        => WithPrefix($"User{Separator}{userId}{Separator}{dataType}");
}
