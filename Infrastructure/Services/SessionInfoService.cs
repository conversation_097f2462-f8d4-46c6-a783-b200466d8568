using Application.Abstraction;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;

namespace Infrastructure.Services;

/// <summary>
/// Implementace služby pro správu informací o relaci uživatele.
/// </summary>
public class SessionInfoService : ISessionInfoService
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private SessionInfo _session;

    /// <summary>
    /// Inicializuje novou instanci třídy SessionInfoService.
    /// </summary>
    /// <param name="httpContextAccessor">Accessor pro HTTP kontext.</param>
    public SessionInfoService(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
        _session = new SessionInfo();
    }

    /// <summary>
    /// Informace o aktuální relaci uživatele.
    /// </summary>
    public SessionInfo Session => _session;

    /// <summary>
    /// Inicializuje informace o relaci na základě aktuálního HTTP kontextu.
    /// </summary>
    /// <returns>Task reprezentující asynchronní operaci.</returns>
    public async Task InitializeAsync()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext?.User?.Identity?.IsAuthenticated == true)
        {
            var user = httpContext.User;
            
            _session.IsAuthenticated = true;
            _session.UserId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            _session.UserName = user.FindFirst(ClaimTypes.Name)?.Value;
            _session.Email = user.FindFirst(ClaimTypes.Email)?.Value;
            _session.Roles = user.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList();
        }
        else
        {
            _session.IsAuthenticated = false;
            _session.UserId = null;
            _session.UserName = null;
            _session.Email = null;
            _session.Roles.Clear();
        }

        await Task.CompletedTask;
    }
}
