using Domain.Entities;
using NetArchTest.Rules;
using Xunit;

namespace Architecture.Tests;

public class ArchitectureTests
{
    private const string DomainNamespace = "Domain";
    private const string ApplicationNamespace = "Application";
    private const string InfrastructureNamespace = "Infrastructure";
    private const string PresentationNamespace = "DataCapture";
    private const string SharedKernelNamespace = "SharedKernel";
    
    #region Základní testy Clean Architecture
    
    [Fact]
    public void Domain_Should_Not_DependOnOtherLayers()
    {
        // Arrange
        var assembly = typeof(SampleEntity).Assembly;

        var otherLayers = new[]
        {
            ApplicationNamespace,
            InfrastructureNamespace,
            PresentationNamespace
        };

        // Act
        var result = Types
            .InAssembly(assembly)
            .ShouldNot()
            .HaveDependencyOnAny(otherLayers)
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, $"Domain should not depend on other layers. Violations: {(result.FailingTypeNames != null ? string.Join(", ", result.FailingTypeNames) : "none")}");
    }

    [Fact]
    public void Application_Should_DependOnDomain_And_SharedKernel_Only()
    {
        // Arrange
        var assembly = typeof(Application.DependencyInjection).Assembly;

        var forbiddenLayers = new[]
        {
            InfrastructureNamespace,
            PresentationNamespace
        };

        // Act
        var result = Types
            .InAssembly(assembly)
            .ShouldNot()
            .HaveDependencyOnAny(forbiddenLayers)
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, $"Application should not depend on Infrastructure or Presentation. Violations: {(result.FailingTypeNames != null ? string.Join(", ", result.FailingTypeNames) : "none")}");
    }

    [Fact]
    public void Infrastructure_Should_Not_DependOnPresentation()
    {
        // Arrange
        var assembly = typeof(Infrastructure.DependencyInjection).Assembly;

        // Act
        var result = Types
            .InAssembly(assembly)
            .ShouldNot()
            .HaveDependencyOn(PresentationNamespace)
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, $"Infrastructure should not depend on Presentation. Violations: {(result.FailingTypeNames != null ? string.Join(", ", result.FailingTypeNames) : "none")}");
    }

    [Fact]
    public void Presentation_Should_Not_HaveDependencyOnInfrastructureImplementations()
    {
        var assembly = System.Reflection.Assembly.Load("DataCapture");
        
        // Act
        var result = Types
            .InAssembly(assembly)
            .That()
            .ResideInNamespace(PresentationNamespace)
            .And()
            .DoNotImplementInterface(typeof(Application.Abstraction.IApplicationDbContext))
            .Should()
            .OnlyHaveDependenciesOn($"{PresentationNamespace}.*", $"{ApplicationNamespace}.*", $"{DomainNamespace}.*", $"{SharedKernelNamespace}.*", "System.*", "Microsoft.*")
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, $"Presentation should only depend on abstractions, not implementations. Violations: {(result.FailingTypeNames != null ? string.Join(", ", result.FailingTypeNames) : "none")}");
    }
    
    #endregion
    
    #region SharedKernel testy
    
    [Fact]
    public void SharedKernel_Should_Not_DependOnAnyApplicationLayer()
    {
        // Arrange
        var assembly = typeof(SharedKernel.Models.Result<>).Assembly;

        var applicationLayers = new[]
        {
            DomainNamespace,
            ApplicationNamespace,
            InfrastructureNamespace,
            PresentationNamespace
        };

        // Act
        var result = Types
            .InAssembly(assembly)
            .ShouldNot()
            .HaveDependencyOnAny(applicationLayers)
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, 
            $"SharedKernel nesmí záviset na žádné vrstvě aplikace. Porušení: {(result.FailingTypeNames != null ? string.Join(", ", result.FailingTypeNames) : "žádné")}");
    }

    [Fact]
    public void SharedKernel_Should_Only_DependOn_SystemAndMicrosoft()
    {
        // Arrange
        var assembly = typeof(SharedKernel.Models.Result<>).Assembly;

        // Act - Kontrolujeme pouze externí závislosti (ne typy ze SharedKernel samotného)
        var result = Types
            .InAssembly(assembly)
            .Should()
            .OnlyHaveDependenciesOn(
                $"{SharedKernelNamespace}.*",
                "System.*",
                "Microsoft.*"
            )
            .GetResult();

        // Filtrujeme pouze skutečné externí závislosti (ne typy ze SharedKernel)
        var externalFailures = result.FailingTypeNames?
            .Where(typeName => !typeName.StartsWith("SharedKernel.") &&
                              !typeName.StartsWith("<PrivateImplementationDetails>") &&
                              !typeName.StartsWith("System.") &&
                              !typeName.StartsWith("Microsoft."))
            .ToList();

        // Assert
        Assert.True(externalFailures == null || !externalFailures.Any(),
            $"SharedKernel smí záviset pouze na System.*, Microsoft.* a sebe sama. Externí závislosti: {(externalFailures != null && externalFailures.Any() ? string.Join(", ", externalFailures) : "žádné")}");
    }

    [Fact]
    public void Domain_Can_DependOn_SharedKernel()
    {
        // Arrange
        var assembly = typeof(SampleEntity).Assembly;

        // Act - Ověříme, že Domain může používat SharedKernel
        var result = Types
            .InAssembly(assembly)
            .Should()
            .OnlyHaveDependenciesOn(
                $"{DomainNamespace}.*",
                $"{SharedKernelNamespace}.*",
                "System.*",
                "Microsoft.*"
            )
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, 
            $"Domain smí záviset na SharedKernel. Porušení: {(result.FailingTypeNames != null ? string.Join(", ", result.FailingTypeNames) : "žádné")}");
    }

    [Fact]
    public void Application_Can_DependOn_SharedKernel()
    {
        // Arrange
        var assembly = typeof(Application.DependencyInjection).Assembly;

        // Act
        var result = Types
            .InAssembly(assembly)
            .Should()
            .OnlyHaveDependenciesOn(
                $"{ApplicationNamespace}.*",
                $"{DomainNamespace}.*",
                $"{SharedKernelNamespace}.*",
                "System.*",
                "Microsoft.*"
            )
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, 
            $"Application smí záviset na Domain a SharedKernel. Porušení: {(result.FailingTypeNames != null ? string.Join(", ", result.FailingTypeNames) : "žádné")}");
    }

    [Fact]
    public void Infrastructure_Can_DependOn_SharedKernel()
    {
        // Arrange
        var assembly = typeof(Infrastructure.DependencyInjection).Assembly;

        // Act
        var result = Types
            .InAssembly(assembly)
            .Should()
            .OnlyHaveDependenciesOn(
                $"{InfrastructureNamespace}.*",
                $"{ApplicationNamespace}.*",
                $"{DomainNamespace}.*",
                $"{SharedKernelNamespace}.*",
                "System.*",
                "Microsoft.*"
            )
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, 
            $"Infrastructure smí záviset na Application, Domain a SharedKernel. Porušení: {(result.FailingTypeNames != null ? string.Join(", ", result.FailingTypeNames) : "žádné")}");
    }

    [Fact]
    public void Presentation_Can_DependOn_SharedKernel()
    {
        // Arrange
        var assembly = System.Reflection.Assembly.Load("DataCapture");

        // Act
        var result = Types
            .InAssembly(assembly)
            .Should()
            .OnlyHaveDependenciesOn(
                $"{PresentationNamespace}.*",
                $"{ApplicationNamespace}.*",
                $"{DomainNamespace}.*",
                $"{SharedKernelNamespace}.*",
                "System.*",
                "Microsoft.*"
            )
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, 
            $"Presentation smí záviset na Application, Domain a SharedKernel. Porušení: {(result.FailingTypeNames != null ? string.Join(", ", result.FailingTypeNames) : "žádné")}");
    }

    [Fact]
    public void SharedKernel_Classes_Should_Be_Public()
    {
        // Arrange
        var assembly = typeof(SharedKernel.Models.Result<>).Assembly;

        // Act
        var result = Types
            .InAssembly(assembly)
            .That()
            .AreClasses()
            .And()
            .DoNotHaveNameStartingWith("<")  // Vynecháme compiler-generated třídy
            .Should()
            .BePublic()
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, 
            $"Všechny třídy v SharedKernel by měly být veřejné pro použití v ostatních vrstvách. Porušení: {(result.FailingTypeNames != null ? string.Join(", ", result.FailingTypeNames) : "žádné")}");
    }
    
    #endregion
}
