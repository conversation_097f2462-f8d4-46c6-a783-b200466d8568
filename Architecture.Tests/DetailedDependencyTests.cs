using Domain.Entities;
using NetArchTest.Rules;
using Xunit;

namespace Architecture.Tests;

public class DetailedDependencyTests
{
    [Fact]
    public void Domain_Should_Not_Reference_EntityFramework()
    {
        // Arrange
        var assembly = typeof(SampleEntity).Assembly;

        // Act
        var result = Types
            .InAssembly(assembly)
            .ShouldNot()
            .HaveDependencyOn("Microsoft.EntityFrameworkCore")
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, "Domain should not reference Entity Framework");
    }

    [Fact]
    public void Domain_Should_Not_Reference_AspNetCore()
    {
        // Arrange
        var assembly = typeof(SampleEntity).Assembly;

        // Act
        var result = Types
            .InAssembly(assembly)
            .ShouldNot()
            .HaveDependencyOn("Microsoft.AspNetCore")
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, "Domain should not reference ASP.NET Core");
    }

    [Fact]
    public void Application_Should_Not_Reference_AspNetCore()
    {
        // Arrange
        var assembly = typeof(Application.DependencyInjection).Assembly;

        // Act
        var result = Types
            .InAssembly(assembly)
            .ShouldNot()
            .HaveDependencyOn("Microsoft.AspNetCore")
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, "Application should not reference ASP.NET Core");
    }

    [Fact]
    public void Application_Should_Not_Reference_Infrastructure_Implementations()
    {
        // Arrange
        var assembly = typeof(Application.DependencyInjection).Assembly;

        // Act
        var result = Types
            .InAssembly(assembly)
            .ShouldNot()
            .HaveDependencyOn("Infrastructure")
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, "Application should not reference Infrastructure implementations");
    }

    [Fact]
    public void Domain_Classes_Should_Be_Sealed_Or_Abstract()
    {
        // Arrange
        var assembly = typeof(SampleEntity).Assembly;

        // Act
        var result = Types
            .InAssembly(assembly)
            .That()
            .AreClasses()
            .And()
            .DoNotHaveNameEndingWith("Entity")
            .And()
            .DoNotHaveNameEndingWith("Exception")
            .Should()
            .BeSealed()
            .Or()
            .BeAbstract()
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, "Domain classes should be sealed or abstract to prevent unintended inheritance");
    }

    [Fact]
    public void Handlers_Should_Not_DependOn_Infrastructure()
    {
        // Arrange
        var assembly = typeof(Application.DependencyInjection).Assembly;

        // Act
        var result = Types
            .InAssembly(assembly)
            .That()
            .HaveNameEndingWith("Handler")
            .ShouldNot()
            .HaveDependencyOn("Infrastructure")
            .GetResult();

        // Assert
        Assert.True(result.IsSuccessful, "Handlers should not depend on Infrastructure");
    }
}
