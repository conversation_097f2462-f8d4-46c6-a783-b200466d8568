# Architecture.Tests

Tento projekt obsahuje testy pro ově<PERSON><PERSON><PERSON> správných závislostí mezi vrstvami Clean Architecture.

## Účel projektu

Cílem tohoto projektu je zajistit, že závislosti mezi vrstvami architektury odpovídají principům Clean Architecture:

1. **SharedKernel** - Nesmí být zá<PERSON>lá na žádné jiné vrstvě aplikace (pouze System.* a Microsoft.*)
2. **Domain** - Nesmí být zá<PERSON>lá na žádné jiné vrstvě (může používat SharedKernel)
3. **Application** - Smí být závislá pouze na vrstvách Domain a SharedKernel
4. **Infrastructure** - Smí být závislá na vrstvách Application, Domain a SharedKernel
5. **Presentation (DataCapture)** - Smí být závislá na vrstvách Application, Infrastructure, Domain a SharedKernel

## Struktura testů

Testy jsou implementovány pomocí knihovny NetArchTest.Rules a jsou rozděleny do několika kategorií:

### Základní testy Clean Architecture:
- **Domain_Should_Not_DependOnOtherLayers** - Ověřuje, že vrstva Domain nemá závislosti na jiných vrstvách
- **Application_Should_DependOnDomain_And_SharedKernel_Only** - Ověřuje, že vrstva Application má závislosti pouze na vrstvách Domain a SharedKernel
- **Infrastructure_Should_Not_DependOnPresentation** - Ověřuje, že vrstva Infrastructure nemá závislosti na vrstvě Presentation
- **Presentation_Should_Not_HaveDependencyOnInfrastructureImplementations** - Ověřuje, že vrstva Presentation používá pouze abstrakce, nikoliv konkrétní implementace z Infrastructure

### SharedKernel testy:
- **SharedKernel_Should_Not_DependOnAnyApplicationLayer** - Ověřuje, že SharedKernel nemá závislosti na žádné vrstvě aplikace
- **SharedKernel_Should_Only_DependOn_SystemAndMicrosoft** - Ověřuje, že SharedKernel závisí pouze na System.* a Microsoft.* namespace
- **Domain_Can_DependOn_SharedKernel** - Ověřuje, že Domain může používat SharedKernel
- **Application_Can_DependOn_SharedKernel** - Ověřuje, že Application může používat SharedKernel
- **Infrastructure_Can_DependOn_SharedKernel** - Ověřuje, že Infrastructure může používat SharedKernel
- **Presentation_Can_DependOn_SharedKernel** - Ověřuje, že Presentation může používat SharedKernel
- **SharedKernel_Classes_Should_Be_Public** - Ověřuje, že všechny třídy v SharedKernel jsou veřejné

## Spuštění testů

Testy můžete spustit pomocí následujících příkazů:

```bash
# Přejděte do adresáře s testy
cd Architecture.Tests

# Spusťte testy
dotnet test
```

Nebo můžete spustit testy přímo z Visual Studio nebo Rider.

## Interpretace výsledků

Pokud některý z testů selže, znamená to, že byla porušena pravidla Clean Architecture. V takovém případě je potřeba upravit kód tak, aby odpovídal principům Clean Architecture.

## Architektonický diagram

```
┌─────────────────┐
│   Presentation  │ ──┐
│   (DataCapture) │   │
└─────────────────┘   │
          │           │
          ▼           │
┌─────────────────┐   │
│ Infrastructure  │   │
└─────────────────┘   │
          │           │
          ▼           │
┌─────────────────┐   │
│   Application   │   │
└─────────────────┘   │
          │           │
          ▼           │
┌─────────────────┐   │
│     Domain      │   │
└─────────────────┘   │
          │           │
          ▼           │
┌─────────────────┐   │
│  SharedKernel   │ ◄─┘
│   (všemi lze    │
│    používat)    │
└─────────────────┘
```

SharedKernel je speciální vrstva, kterou mohou používat všechny ostatní vrstvy, ale sama nesmí záviset na žádné z nich.
