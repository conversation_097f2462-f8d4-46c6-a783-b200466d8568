using SharedKernel.Abstractions.Mapping;
using Application.Features.Sample;
using Application.Features.Orders;
using Application.Features.Invoices;
using Domain.Entities;
using SharedKernel.Infrastructure.Mapping;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace Infrastructure.Tests.DependencyInjection;

/// <summary>
/// Testy pro ověření automatické registrace mapperů
/// </summary>
[Trait("TestCategory", "Mapper")]
public class MapperRegistrationTests
{
    /// <summary>
    /// Ověří, že všechny mappery jsou automaticky registrovány v DI kontejneru
    /// </summary>
    [Fact]
    public void RegisterMappers_ShouldRegisterAllMappersAutomatically()
    {
        // Arrange
        var services = new ServiceCollection();

        // Registrujeme pouze mappery pomocí reflection
        var registerMappersMethod = typeof(Infrastructure.DependencyInjection)
            .GetMethod("RegisterMappers", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);

        // Act
        registerMappersMethod!.Invoke(null, new object[] { services });
        var serviceProvider = services.BuildServiceProvider();

        // Assert - SampleEntity mappery
        var sampleEntityToDto = serviceProvider.GetService<IUnifiedMapper<SampleEntity, SampleDto>>();
        var sampleAddEditToEntity = serviceProvider.GetService<IUnifiedMapper<SampleAddEdit, SampleEntity>>();
        
        Assert.NotNull(sampleEntityToDto);
        Assert.NotNull(sampleAddEditToEntity);
        Assert.IsType<UnifiedMapper<SampleEntity, SampleDto>>(sampleEntityToDto);
        Assert.IsType<UnifiedMapper<SampleAddEdit, SampleEntity>>(sampleAddEditToEntity);

        // Assert - Order mappery
        var orderEntityToDto = serviceProvider.GetService<IUnifiedMapper<Order, OrderDto>>();
        var orderAddEditToEntity = serviceProvider.GetService<IUnifiedMapper<OrderAddEdit, Order>>();
        
        Assert.NotNull(orderEntityToDto);
        Assert.NotNull(orderAddEditToEntity);
        Assert.IsType<UnifiedMapper<Order, OrderDto>>(orderEntityToDto);
        Assert.IsType<UnifiedMapper<OrderAddEdit, Order>>(orderAddEditToEntity);

        // Assert - OrderItem mappery
        var orderItemEntityToDto = serviceProvider.GetService<IUnifiedMapper<OrderItem, OrderItemDto>>();
        var orderItemAddEditToEntity = serviceProvider.GetService<IUnifiedMapper<OrderItemAddEdit, OrderItem>>();
        
        Assert.NotNull(orderItemEntityToDto);
        Assert.NotNull(orderItemAddEditToEntity);
        Assert.IsType<UnifiedMapper<OrderItem, OrderItemDto>>(orderItemEntityToDto);
        Assert.IsType<UnifiedMapper<OrderItemAddEdit, OrderItem>>(orderItemAddEditToEntity);

        // Assert - Invoice mappery
        var invoiceEntityToDto = serviceProvider.GetService<IUnifiedMapper<Invoice, InvoiceDto>>();
        var invoiceAddEditToEntity = serviceProvider.GetService<IUnifiedMapper<InvoiceAddEdit, Invoice>>();
        
        Assert.NotNull(invoiceEntityToDto);
        Assert.NotNull(invoiceAddEditToEntity);
        Assert.IsType<UnifiedMapper<Invoice, InvoiceDto>>(invoiceEntityToDto);
        Assert.IsType<UnifiedMapper<InvoiceAddEdit, Invoice>>(invoiceAddEditToEntity);

        // Assert - InvoiceItem mappery
        var invoiceItemEntityToDto = serviceProvider.GetService<IUnifiedMapper<InvoiceItem, InvoiceItemDto>>();
        var invoiceItemAddEditToEntity = serviceProvider.GetService<IUnifiedMapper<InvoiceItemAddEdit, InvoiceItem>>();
        
        Assert.NotNull(invoiceItemEntityToDto);
        Assert.NotNull(invoiceItemAddEditToEntity);
        Assert.IsType<UnifiedMapper<InvoiceItem, InvoiceItemDto>>(invoiceItemEntityToDto);
        Assert.IsType<UnifiedMapper<InvoiceItemAddEdit, InvoiceItem>>(invoiceItemAddEditToEntity);
    }

    /// <summary>
    /// Ověří, že mappery jsou registrovány jako Singleton
    /// </summary>
    [Fact]
    public void RegisterMappers_ShouldRegisterMappersAsSingleton()
    {
        // Arrange
        var services = new ServiceCollection();

        // Registrujeme pouze mappery pomocí reflection
        var registerMappersMethod = typeof(Infrastructure.DependencyInjection)
            .GetMethod("RegisterMappers", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);

        // Act
        registerMappersMethod!.Invoke(null, new object[] { services });
        var serviceProvider = services.BuildServiceProvider();

        // Assert - Ověří, že stejná instance je vrácena při opakovaném volání
        var mapper1 = serviceProvider.GetService<IUnifiedMapper<SampleEntity, SampleDto>>();
        var mapper2 = serviceProvider.GetService<IUnifiedMapper<SampleEntity, SampleDto>>();
        
        Assert.NotNull(mapper1);
        Assert.NotNull(mapper2);
        Assert.Same(mapper1, mapper2); // Stejná instance = Singleton
    }

    /// <summary>
    /// Ověří, že mappery fungují správně pro základní mapování
    /// </summary>
    [Fact]
    public void RegisteredMappers_ShouldWorkCorrectly()
    {
        // Arrange
        var services = new ServiceCollection();

        // Registrujeme pouze mappery pomocí reflection
        var registerMappersMethod = typeof(Infrastructure.DependencyInjection)
            .GetMethod("RegisterMappers", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);

        registerMappersMethod!.Invoke(null, new object[] { services });
        var serviceProvider = services.BuildServiceProvider();

        var mapper = serviceProvider.GetRequiredService<IUnifiedMapper<SampleAddEdit, SampleEntity>>();
        var sampleAddEdit = new SampleAddEdit
        {
            Name = "Test Sample",
            Description = "Test Description",
            Age = 25,
            IsActive = true
        };

        // Act
        var entity = mapper.Map(sampleAddEdit);

        // Assert
        Assert.NotNull(entity);
        Assert.Equal(sampleAddEdit.Name, entity.Name);
        Assert.Equal(sampleAddEdit.Description, entity.Description);
        Assert.Equal(sampleAddEdit.Age, entity.Age);
        Assert.Equal(sampleAddEdit.IsActive, entity.IsActive);
    }
}
