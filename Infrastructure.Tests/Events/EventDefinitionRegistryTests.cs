using Domain.Entities;
using Infrastructure.Events;
using System;
using System.Linq;
using Xunit;

namespace Infrastructure.Tests.Events;

/// <summary>
/// Testy pro EventDefinitionRegistry.
/// </summary>
public class EventDefinitionRegistryTests
{
    [Fact]
    public void Register_AddsDefinitionToRegistry()
    {
        // Arrange
        var registry = new EventDefinitionRegistry();
        var definition = new EventDefinition("TestEvent", typeof(SampleEntity), EntityOperation.Created);

        // Act
        registry.Register(definition);

        // Assert
        Assert.Equal(1, registry.Count);
        Assert.True(registry.Contains("TestEvent"));
    }

    [Fact]
    public void Register_ThrowsException_WhenDefinitionIsNull()
    {
        // Arrange
        var registry = new EventDefinitionRegistry();

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => registry.Register(null!));
    }

    [Fact]
    public void Register_ThrowsException_WhenEventNameIsEmpty()
    {
        // Arrange
        var registry = new EventDefinitionRegistry();
        var definition = new EventDefinition("", typeof(SampleEntity), EntityOperation.Created);

        // Act & Assert
        Assert.Throws<ArgumentException>(() => registry.Register(definition));
    }

    [Fact]
    public void Register_ThrowsException_WhenDuplicateEventName()
    {
        // Arrange
        var registry = new EventDefinitionRegistry();
        var definition1 = new EventDefinition("TestEvent", typeof(SampleEntity), EntityOperation.Created);
        var definition2 = new EventDefinition("TestEvent", typeof(SampleEntity), EntityOperation.Updated);

        registry.Register(definition1);

        // Act & Assert
        Assert.Throws<ArgumentException>(() => registry.Register(definition2));
    }

    [Fact]
    public void RegisterRange_AddsMultipleDefinitions()
    {
        // Arrange
        var registry = new EventDefinitionRegistry();
        var definitions = new[]
        {
            new EventDefinition("Event1", typeof(SampleEntity), EntityOperation.Created),
            new EventDefinition("Event2", typeof(SampleEntity), EntityOperation.Updated),
            new EventDefinition("Event3", typeof(SampleEntity), EntityOperation.Deleted)
        };

        // Act
        registry.RegisterRange(definitions);

        // Assert
        Assert.Equal(3, registry.Count);
        Assert.True(registry.Contains("Event1"));
        Assert.True(registry.Contains("Event2"));
        Assert.True(registry.Contains("Event3"));
    }

    [Fact]
    public void GetDefinitions_ReturnsCorrectDefinitions_ForEntityTypeAndOperation()
    {
        // Arrange
        var registry = new EventDefinitionRegistry();
        var createdDefinition = new EventDefinition("Created", typeof(SampleEntity), EntityOperation.Created);
        var updatedDefinition = new EventDefinition("Updated", typeof(SampleEntity), EntityOperation.Updated);
        var anyDefinition = new EventDefinition("Any", typeof(SampleEntity), EntityOperation.Any);
        var otherEntityDefinition = new EventDefinition("OtherCreated", typeof(object), EntityOperation.Created);

        registry.RegisterRange(new[] { createdDefinition, updatedDefinition, anyDefinition, otherEntityDefinition });

        // Act
        var createdDefinitions = registry.GetDefinitions(typeof(SampleEntity), EntityOperation.Created).ToList();

        // Assert
        Assert.Equal(2, createdDefinitions.Count); // Created + Any
        Assert.Contains(createdDefinition, createdDefinitions);
        Assert.Contains(anyDefinition, createdDefinitions);
        Assert.DoesNotContain(updatedDefinition, createdDefinitions);
        Assert.DoesNotContain(otherEntityDefinition, createdDefinitions);
    }

    [Fact]
    public void GetDefinitions_ReturnsEmpty_ForNullEntityType()
    {
        // Arrange
        var registry = new EventDefinitionRegistry();
        var definition = new EventDefinition("Test", typeof(SampleEntity), EntityOperation.Created);
        registry.Register(definition);

        // Act
        var definitions = registry.GetDefinitions(null!, EntityOperation.Created);

        // Assert
        Assert.Empty(definitions);
    }

    [Fact]
    public void GetDefinitions_ReturnsOnlyActiveDefinitions()
    {
        // Arrange
        var registry = new EventDefinitionRegistry();
        var activeDefinition = new EventDefinition("Active", typeof(SampleEntity), EntityOperation.Created);
        var inactiveDefinition = new EventDefinition("Inactive", typeof(SampleEntity), EntityOperation.Created)
            .Deactivate();

        registry.RegisterRange(new[] { activeDefinition, inactiveDefinition });

        // Act
        var definitions = registry.GetDefinitions(typeof(SampleEntity), EntityOperation.Created).ToList();

        // Assert
        Assert.Single(definitions);
        Assert.Contains(activeDefinition, definitions);
        Assert.DoesNotContain(inactiveDefinition, definitions);
    }

    [Fact]
    public void GetDefinition_ReturnsCorrectDefinition_ByName()
    {
        // Arrange
        var registry = new EventDefinitionRegistry();
        var definition = new EventDefinition("TestEvent", typeof(SampleEntity), EntityOperation.Created);
        registry.Register(definition);

        // Act
        var result = registry.GetDefinition("TestEvent");

        // Assert
        Assert.Equal(definition, result);
    }

    [Fact]
    public void GetDefinition_ReturnsNull_ForNonExistentName()
    {
        // Arrange
        var registry = new EventDefinitionRegistry();

        // Act
        var result = registry.GetDefinition("NonExistent");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public void Remove_RemovesDefinition_ReturnsTrue()
    {
        // Arrange
        var registry = new EventDefinitionRegistry();
        var definition = new EventDefinition("TestEvent", typeof(SampleEntity), EntityOperation.Created);
        registry.Register(definition);

        // Act
        var result = registry.Remove("TestEvent");

        // Assert
        Assert.True(result);
        Assert.Equal(0, registry.Count);
        Assert.False(registry.Contains("TestEvent"));
    }

    [Fact]
    public void Remove_ReturnsFalse_ForNonExistentName()
    {
        // Arrange
        var registry = new EventDefinitionRegistry();

        // Act
        var result = registry.Remove("NonExistent");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void Clear_RemovesAllDefinitions()
    {
        // Arrange
        var registry = new EventDefinitionRegistry();
        var definitions = new[]
        {
            new EventDefinition("Event1", typeof(SampleEntity), EntityOperation.Created),
            new EventDefinition("Event2", typeof(SampleEntity), EntityOperation.Updated)
        };
        registry.RegisterRange(definitions);

        // Act
        registry.Clear();

        // Assert
        Assert.Equal(0, registry.Count);
        Assert.Empty(registry.GetAllDefinitions());
    }

    [Fact]
    public void GetAllDefinitions_ReturnsAllRegisteredDefinitions()
    {
        // Arrange
        var registry = new EventDefinitionRegistry();
        var definitions = new[]
        {
            new EventDefinition("Event1", typeof(SampleEntity), EntityOperation.Created),
            new EventDefinition("Event2", typeof(SampleEntity), EntityOperation.Updated),
            new EventDefinition("Event3", typeof(SampleEntity), EntityOperation.Deleted)
        };
        registry.RegisterRange(definitions);

        // Act
        var allDefinitions = registry.GetAllDefinitions().ToList();

        // Assert
        Assert.Equal(3, allDefinitions.Count);
        Assert.Contains(definitions[0], allDefinitions);
        Assert.Contains(definitions[1], allDefinitions);
        Assert.Contains(definitions[2], allDefinitions);
    }
}
