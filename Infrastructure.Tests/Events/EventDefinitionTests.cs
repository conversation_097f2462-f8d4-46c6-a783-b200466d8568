using Domain.Entities;
using Infrastructure.Events;
using System;
using Xunit;

namespace Infrastructure.Tests.Events;

/// <summary>
/// Testy pro EventDefinition třídu.
/// </summary>
public class EventDefinitionTests
{
    [Fact]
    public void EventDefinition_Constructor_SetsPropertiesCorrectly()
    {
        // Arrange
        var eventName = "TestEvent";
        var entityType = typeof(SampleEntity);
        var operation = EntityOperation.Created;
        var description = "Test description";

        // Act
        var definition = new EventDefinition(eventName, entityType, operation, description);

        // Assert
        Assert.Equal(eventName, definition.EventName);
        Assert.Equal(entityType, definition.EntityType);
        Assert.Equal(operation, definition.Operation);
        Assert.Equal(description, definition.Description);
        Assert.True(definition.IsActive);
        Assert.Empty(definition.TrackedProperties);
        Assert.Null(definition.Condition);
    }

    [Fact]
    public void WithTrackedProperties_AddsPropertiesToList()
    {
        // Arrange
        var definition = new EventDefinition("TestEvent", typeof(SampleEntity), EntityOperation.Updated);
        var properties = new[] { "Name", "Description", "Age" };

        // Act
        var result = definition.WithTrackedProperties(properties);

        // Assert
        Assert.Same(definition, result); // Fluent API
        Assert.Equal(properties, definition.TrackedProperties);
    }

    [Fact]
    public void WithCondition_SetsCondition()
    {
        // Arrange
        var definition = new EventDefinition("TestEvent", typeof(SampleEntity), EntityOperation.Updated);
        Func<object, bool> condition = entity => entity is SampleEntity sample && sample.IsActive;

        // Act
        var result = definition.WithCondition(condition);

        // Assert
        Assert.Same(definition, result); // Fluent API
        Assert.Equal(condition, definition.Condition);
    }

    [Fact]
    public void ShouldTrigger_ReturnsFalse_WhenInactive()
    {
        // Arrange
        var definition = new EventDefinition("TestEvent", typeof(SampleEntity), EntityOperation.Created)
            .Deactivate();
        var entity = new SampleEntity { Id = 1 };

        // Act
        var result = definition.ShouldTrigger(entity);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void ShouldTrigger_ReturnsFalse_WhenWrongEntityType()
    {
        // Arrange
        var definition = new EventDefinition("TestEvent", typeof(SampleEntity), EntityOperation.Created);
        var entity = new object(); // Wrong type

        // Act
        var result = definition.ShouldTrigger(entity);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void ShouldTrigger_ReturnsTrue_WhenConditionIsNull()
    {
        // Arrange
        var definition = new EventDefinition("TestEvent", typeof(SampleEntity), EntityOperation.Created);
        var entity = new SampleEntity { Id = 1 };

        // Act
        var result = definition.ShouldTrigger(entity);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void ShouldTrigger_ReturnsConditionResult_WhenConditionIsSet()
    {
        // Arrange
        var definition = new EventDefinition("TestEvent", typeof(SampleEntity), EntityOperation.Updated)
            .WithCondition(entity => entity is SampleEntity sample && sample.IsActive);
        
        var activeEntity = new SampleEntity { Id = 1, IsActive = true };
        var inactiveEntity = new SampleEntity { Id = 2, IsActive = false };

        // Act & Assert
        Assert.True(definition.ShouldTrigger(activeEntity));
        Assert.False(definition.ShouldTrigger(inactiveEntity));
    }

    [Fact]
    public void HasTrackedPropertyChanged_ReturnsTrue_WhenNoTrackedPropertiesDefined()
    {
        // Arrange
        var definition = new EventDefinition("TestEvent", typeof(SampleEntity), EntityOperation.Updated);
        var changedProperties = new[] { "Name", "Description" };

        // Act
        var result = definition.HasTrackedPropertyChanged(changedProperties);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void HasTrackedPropertyChanged_ReturnsTrue_WhenTrackedPropertyChanged()
    {
        // Arrange
        var definition = new EventDefinition("TestEvent", typeof(SampleEntity), EntityOperation.Updated)
            .WithTrackedProperties("Name", "Age");
        var changedProperties = new[] { "Name", "Description" };

        // Act
        var result = definition.HasTrackedPropertyChanged(changedProperties);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void HasTrackedPropertyChanged_ReturnsFalse_WhenNoTrackedPropertyChanged()
    {
        // Arrange
        var definition = new EventDefinition("TestEvent", typeof(SampleEntity), EntityOperation.Updated)
            .WithTrackedProperties("Name", "Age");
        var changedProperties = new[] { "Description", "IsActive" };

        // Act
        var result = definition.HasTrackedPropertyChanged(changedProperties);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void EntityName_ReturnsCorrectName()
    {
        // Arrange
        var definition = new EventDefinition("TestEvent", typeof(SampleEntity), EntityOperation.Created);

        // Act
        var entityName = definition.EntityName;

        // Assert
        Assert.Equal("SampleEntity", entityName);
    }

    [Fact]
    public void Activate_Deactivate_WorksCorrectly()
    {
        // Arrange
        var definition = new EventDefinition("TestEvent", typeof(SampleEntity), EntityOperation.Created);

        // Act & Assert
        Assert.True(definition.IsActive); // Default is active

        var deactivated = definition.Deactivate();
        Assert.Same(definition, deactivated); // Fluent API
        Assert.False(definition.IsActive);

        var activated = definition.Activate();
        Assert.Same(definition, activated); // Fluent API
        Assert.True(definition.IsActive);
    }
}
