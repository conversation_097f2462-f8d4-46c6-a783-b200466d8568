using Application.Abstraction;
using Infrastructure.Services;
using Microsoft.Extensions.Configuration;
using Xunit;

namespace Infrastructure.Tests.Services;

/// <summary>
/// Testy pro konfigurovatelný cache key provider.
/// </summary>
public class CacheKeyProviderTests
{
    [Fact]
    public void CacheKeyProvider_WithDefaultConfiguration_ShouldUseDefaultPrefix()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>())
            .Build();
        
        var provider = new CacheKeyProvider(configuration);
        
        // Act & Assert
        Assert.Equal("DataCapture", provider.AppPrefix);
        Assert.Equal("_", provider.Separator);
    }

    [Fact]
    public void CacheKeyProvider_WithCustomConfiguration_ShouldUseCustomPrefix()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Cache:Prefix"] = "MyApp",
                ["Cache:Separator"] = "-"
            })
            .Build();
        
        var provider = new CacheKeyProvider(configuration);
        
        // Act & Assert
        Assert.Equal("MyApp", provider.AppPrefix);
        Assert.Equal("-", provider.Separator);
    }

    [Fact]
    public void WithPrefix_ShouldCreateCorrectKey()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Cache:Prefix"] = "TestApp"
            })
            .Build();
        
        var provider = new CacheKeyProvider(configuration);
        
        // Act
        var result = provider.WithPrefix("TestKey");
        
        // Assert
        Assert.Equal("TestApp_TestKey", result);
    }

    [Fact]
    public void ForEntity_ShouldCreateCorrectKey()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Cache:Prefix"] = "TestApp"
            })
            .Build();
        
        var provider = new CacheKeyProvider(configuration);
        
        // Act
        var result = provider.ForEntity("SampleEntity", "GetAll");
        
        // Assert
        Assert.Equal("TestApp_GetAll_SampleEntity", result);
    }

    [Fact]
    public void ForPagedQuery_ShouldCreateCorrectKey()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Cache:Prefix"] = "TestApp"
            })
            .Build();
        
        var provider = new CacheKeyProvider(configuration);
        
        // Act
        var result = provider.ForPagedQuery("SampleEntity", 2, 20, "Name", true);
        
        // Assert
        Assert.Equal("TestApp_GetPaged_SampleEntity_Page2_Size20_SortNameDesc", result);
    }

    [Fact]
    public void ForUser_ShouldCreateCorrectKey()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Cache:Prefix"] = "TestApp"
            })
            .Build();
        
        var provider = new CacheKeyProvider(configuration);
        
        // Act
        var result = provider.ForUser("user123", "Profile");
        
        // Assert
        Assert.Equal("TestApp_User_user123_Profile", result);
    }

    [Fact]
    public void ForPagedQuery_WithoutSorting_ShouldCreateCorrectKey()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Cache:Prefix"] = "TestApp"
            })
            .Build();
        
        var provider = new CacheKeyProvider(configuration);
        
        // Act
        var result = provider.ForPagedQuery("SampleEntity", 1, 10);
        
        // Assert
        Assert.Equal("TestApp_GetPaged_SampleEntity_Page1_Size10", result);
    }

    [Fact]
    public void ForPagedQuery_WithSortingAscending_ShouldCreateCorrectKey()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Cache:Prefix"] = "TestApp"
            })
            .Build();
        
        var provider = new CacheKeyProvider(configuration);
        
        // Act
        var result = provider.ForPagedQuery("SampleEntity", 1, 10, "Name", false);
        
        // Assert
        Assert.Equal("TestApp_GetPaged_SampleEntity_Page1_Size10_SortName", result);
    }
}
