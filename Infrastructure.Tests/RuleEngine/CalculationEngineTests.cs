using Infrastructure.RuleEngine;
using Infrastructure.RuleEngine.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Infrastructure.Tests.RuleEngine;

/// <summary>
/// Testy pro ověření základní funkcionality CalculationEngine.
/// Testuje schopnost vytvářet a vykonávat obchodní pravidla za běhu.
/// </summary>
public class CalculationEngineTests : IDisposable
{
    private readonly CalculationEngine _engine;
    private readonly TestRuleDataProvider _dataProvider;
    private readonly IReadOnlyDictionary<string, Type> _entityTypeMap;
    private readonly RuleCacheService _cacheService;

    public CalculationEngineTests()
    {
        _entityTypeMap = new Dictionary<string, Type>
        {
            ["TestEntity"] = typeof(TestEntity),
            ["RelatedEntity"] = typeof(RelatedEntity)
        };

        _dataProvider = new TestRuleDataProvider();
        var expressionBuilder = TestExpressionBuilderFactory.Create(_dataProvider, _entityTypeMap);
        var mockLogger = new Mock<ILogger<CalculationEngine>>();
        var mockCacheLogger = new Mock<ILogger<RuleCacheService>>();

        _cacheService = new RuleCacheService(mockCacheLogger.Object);
        _engine = new CalculationEngine(expressionBuilder, _entityTypeMap, _cacheService, mockLogger.Object);
    }

    [Fact]
    public async Task ExecuteAsync_SimpleArithmeticRule_ReturnsCorrectResult()
    {
        // Arrange - Pravidlo: entity.Value + 10
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Add Ten",
            TargetEntityName = "TestEntity",
            RootNode = new OperationNode
            {
                Operator = OperatorType.Add,
                Operands = new List<RuleNode>
                {
                    new SourceValueNode { SourcePath = "Value" },
                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Integer, Value = "10" }
                }
            }
        };

        var entity = new TestEntity { Value = 5 };

        // Act
        var result = await _engine.ExecuteAsync(rule, entity);

        // Assert
        Assert.Equal(15, result);
    }

    [Fact]
    public async Task ExecuteAsync_LogicalRule_ReturnsCorrectResult()
    {
        // Arrange - Pravidlo: entity.Value > 10
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Greater Than Ten",
            TargetEntityName = "TestEntity",
            RootNode = new OperationNode
            {
                Operator = OperatorType.GreaterThan,
                Operands = new List<RuleNode>
                {
                    new SourceValueNode { SourcePath = "Value" },
                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Integer, Value = "10" }
                }
            }
        };

        var entity = new TestEntity { Value = 15 };

        // Act
        var result = await _engine.ExecuteAsync(rule, entity);

        // Assert
        Assert.Equal(true, result);
    }

    [Fact]
    public async Task ExecuteAsync_ConditionalRule_ReturnsCorrectResult()
    {
        // Arrange - Pravidlo: entity.Value > 10 ? entity.Value * 2 : entity.Value
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Conditional Double",
            TargetEntityName = "TestEntity",
            RootNode = new OperationNode
            {
                Operator = OperatorType.If,
                Operands = new List<RuleNode>
                {
                    // Condition: Value > 10
                    new OperationNode
                    {
                        Operator = OperatorType.GreaterThan,
                        Operands = new List<RuleNode>
                        {
                            new SourceValueNode { SourcePath = "Value" },
                            new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Integer, Value = "10" }
                        }
                    },
                    // True: Value * 2
                    new OperationNode
                    {
                        Operator = OperatorType.Multiply,
                        Operands = new List<RuleNode>
                        {
                            new SourceValueNode { SourcePath = "Value" },
                            new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Integer, Value = "2" }
                        }
                    },
                    // False: Value
                    new SourceValueNode { SourcePath = "Value" }
                }
            }
        };

        var entity = new TestEntity { Value = 15 };

        // Act
        var result = await _engine.ExecuteAsync(rule, entity);

        // Assert
        Assert.Equal(30, result);
    }

    [Fact]
    public async Task ExecuteAsync_PropertyNavigationRule_ReturnsCorrectResult()
    {
        // Arrange - Pravidlo: entity.Related.Name
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Get Related Name",
            TargetEntityName = "TestEntity",
            RootNode = new SourceValueNode { SourcePath = "Related.Name" }
        };

        var entity = new TestEntity 
        { 
            Value = 5,
            Related = new RelatedEntity { Name = "Test Name" }
        };

        // Act
        var result = await _engine.ExecuteAsync(rule, entity);

        // Assert
        Assert.Equal("Test Name", result);
    }

    [Fact]
    public async Task ExecuteAsync_CacheTest_UsesCompiledDelegate()
    {
        // Arrange
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Cache Test",
            TargetEntityName = "TestEntity",
            RootNode = new SourceValueNode { SourcePath = "Value" }
        };

        var entity1 = new TestEntity { Value = 10 };
        var entity2 = new TestEntity { Value = 20 };

        // Act - První volání zkompiluje pravidlo
        var result1 = await _engine.ExecuteAsync(rule, entity1);

        // Act - Druhé volání by mělo použít cache
        var result2 = await _engine.ExecuteAsync(rule, entity2);

        // Assert
        Assert.Equal(10, result1);
        Assert.Equal(20, result2);
    }

    [Fact]
    public async Task ExecuteAsync_AggregationRule_CountItems()
    {
        // Arrange - Pravidlo: Count(entity.Items)
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Count Items",
            TargetEntityName = "TestEntity",
            RootNode = new AggregationNode
            {
                AggregationType = AggregationType.Count,
                CollectionPath = "Items"
            }
        };

        var entity = new TestEntity
        {
            Value = 5,
            Items = new List<RelatedEntity>
            {
                new RelatedEntity { Name = "Item1", Count = 10 },
                new RelatedEntity { Name = "Item2", Count = 20 },
                new RelatedEntity { Name = "Item3", Count = 30 }
            }
        };

        // Act
        var result = await _engine.ExecuteAsync(rule, entity);

        // Assert
        Assert.Equal(3, result);
    }

    [Fact]
    public async Task ExecuteAsync_AggregationRule_SumWithField()
    {
        // Arrange - Pravidlo: Sum(entity.Items.Count)
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Sum Item Counts",
            TargetEntityName = "TestEntity",
            RootNode = new AggregationNode
            {
                AggregationType = AggregationType.Sum,
                CollectionPath = "Items",
                FieldPath = "Count"
            }
        };

        var entity = new TestEntity
        {
            Value = 5,
            Items = new List<RelatedEntity>
            {
                new RelatedEntity { Name = "Item1", Count = 10 },
                new RelatedEntity { Name = "Item2", Count = 20 },
                new RelatedEntity { Name = "Item3", Count = 30 }
            }
        };

        // Act
        var result = await _engine.ExecuteAsync(rule, entity);

        // Assert
        Assert.Equal(60, result);
    }

    [Fact]
    public async Task ExecuteAsync_ComplexRule_NestedOperations()
    {
        // Arrange - Pravidlo: (entity.Value * 2) + Count(entity.Items)
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Complex Calculation",
            TargetEntityName = "TestEntity",
            RootNode = new OperationNode
            {
                Operator = OperatorType.Add,
                Operands = new List<RuleNode>
                {
                    // entity.Value * 2
                    new OperationNode
                    {
                        Operator = OperatorType.Multiply,
                        Operands = new List<RuleNode>
                        {
                            new SourceValueNode { SourcePath = "Value" },
                            new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Integer, Value = "2" }
                        }
                    },
                    // Count(entity.Items)
                    new AggregationNode
                    {
                        AggregationType = AggregationType.Count,
                        CollectionPath = "Items"
                    }
                }
            }
        };

        var entity = new TestEntity
        {
            Value = 15,
            Items = new List<RelatedEntity>
            {
                new RelatedEntity { Name = "Item1", Count = 10 },
                new RelatedEntity { Name = "Item2", Count = 20 }
            }
        };

        // Act
        var result = await _engine.ExecuteAsync(rule, entity);

        // Assert - (15 * 2) + 2 = 32
        Assert.Equal(32, result);
    }

    [Fact]
    public async Task ValidateRuleAsync_ValidRule_ReturnsValid()
    {
        // Arrange - Platné pravidlo
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Valid Rule",
            TargetEntityName = "TestEntity",
            RootNode = new SourceValueNode { SourcePath = "Value" }
        };

        // Act
        var result = await _engine.ValidateRuleAsync(rule);

        // Assert
        Assert.True(result.IsValid);
        Assert.Empty(result.Errors);
    }

    [Fact]
    public async Task ValidateRuleAsync_InvalidRule_ReturnsInvalid()
    {
        // Arrange - Neplatné pravidlo (null RootNode)
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Invalid Rule",
            TargetEntityName = "TestEntity",
            RootNode = null!
        };

        // Act
        var result = await _engine.ValidateRuleAsync(rule);

        // Assert
        Assert.False(result.IsValid);
        Assert.NotEmpty(result.Errors);
        Assert.Contains("kořenový uzel", result.ErrorMessage);
    }

    [Fact]
    public async Task ValidateRuleAsync_UnknownEntity_ReturnsInvalid()
    {
        // Arrange - Neznámá entita
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Unknown Entity Rule",
            TargetEntityName = "UnknownEntity",
            RootNode = new SourceValueNode { SourcePath = "Value" }
        };

        // Act
        var result = await _engine.ValidateRuleAsync(rule);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains("Neznámá cílová entita", result.ErrorMessage);
    }

    [Fact]
    public async Task InvalidateRuleAsync_RemovesFromCache()
    {
        // Arrange
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Cache Test",
            TargetEntityName = "TestEntity",
            RootNode = new SourceValueNode { SourcePath = "Value" }
        };

        var entity = new TestEntity { Value = 10 };

        // Act - První volání zkompiluje pravidlo
        var result1 = await _engine.ExecuteAsync(rule, entity);

        // Invalidace cache
        await _engine.InvalidateRuleAsync(rule.Id);

        // Druhé volání by mělo znovu zkompilovat
        var result2 = await _engine.ExecuteAsync(rule, entity);

        // Assert
        Assert.Equal(10, result1);
        Assert.Equal(10, result2);
        // Test prošel, pokud nedošlo k výjimce
    }

    [Fact]
    public async Task ExecuteAsync_NullRule_ThrowsArgumentNullException()
    {
        // Arrange
        var entity = new TestEntity { Value = 10 };

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _engine.ExecuteAsync(null!, entity));
    }

    [Fact]
    public async Task ExecuteAsync_NullEntity_ThrowsArgumentNullException()
    {
        // Arrange
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Test Rule",
            TargetEntityName = "TestEntity",
            RootNode = new SourceValueNode { SourcePath = "Value" }
        };

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _engine.ExecuteAsync(rule, null!));
    }

    public void Dispose()
    {
        _cacheService?.Dispose();
    }
}

/// <summary>
/// Testovací entita pro ověření funkcionality RuleEngine.
/// </summary>
public class TestEntity
{
    public int Value { get; set; }
    public RelatedEntity? Related { get; set; }
    public List<RelatedEntity> Items { get; set; } = new();
}

/// <summary>
/// Testovací související entita.
/// </summary>
public class RelatedEntity
{
    public string Name { get; set; } = string.Empty;
    public int Count { get; set; }
}

/// <summary>
/// Testovací implementace IRuleDataProvider pro unit testy.
/// </summary>
public class TestRuleDataProvider : IRuleDataProvider
{
    private readonly List<object> _entities = new();

    public void AddEntity(object entity)
    {
        _entities.Add(entity);
    }

    public object FindSingle(string entityName, System.Linq.Expressions.Expression filterExpression)
    {
        // Jednoduchá implementace pro testy
        // V reálné aplikaci by toto používalo Entity Framework
        return _entities.FirstOrDefault() ?? throw new InvalidOperationException("Entity not found");
    }

    public IEnumerable<object> FindMany(string entityName, System.Linq.Expressions.Expression filterExpression)
    {
        // Jednoduchá implementace pro testy
        // V reálné aplikaci by toto používalo Entity Framework
        return _entities;
    }
}
