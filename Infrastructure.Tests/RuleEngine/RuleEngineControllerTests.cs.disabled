using Infrastructure.RuleEngine;
using Infrastructure.RuleEngine.API;
using Infrastructure.RuleEngine.Mapping;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Infrastructure.Tests.RuleEngine;

/// <summary>
/// Testy pro RuleEngine API endpointy.
/// Ově<PERSON><PERSON><PERSON> správn<PERSON> chování REST endpointů a error handling.
/// Poznámka: Testy jsou dočasně zakázány kvůli přechodu na minimal API.
/// TODO: Přepsat testy pro minimal API endpointy.
/// </summary>
public class RuleEngineControllerTests_Disabled
{
    private readonly Mock<IRuleRepository> _mockRepository;
    private readonly Mock<IRuleDiagramMapper> _mockMapper;
    // private readonly RuleEngineController _controller; // Odstraněno - přechod na minimal API

    public RuleEngineControllerTests_Disabled()
    {
        _mockRepository = new Mock<IRuleRepository>();

        // Vytvoříme reálný CalculationEngine s mock logger
        var entityTypeMap = new Dictionary<string, Type> { ["TestEntity"] = typeof(TestEntity) };
        var dataProvider = new TestRuleDataProvider();
        var expressionBuilder = new ExpressionBuilder(dataProvider, entityTypeMap);
        var mockLogger = new Mock<ILogger<CalculationEngine>>();
        var realEngine = new CalculationEngine(expressionBuilder, entityTypeMap, mockLogger.Object);

        _mockMapper = new Mock<IRuleDiagramMapper>();
        // _controller = new RuleEngineController(_mockRepository.Object, realEngine, _mockMapper.Object); // Odstraněno
    }

    [Fact]
    public async Task GetAllRules_ReturnsOkWithRules()
    {
        // Arrange
        var rules = new List<BusinessRule>
        {
            new BusinessRule
            {
                Id = Guid.NewGuid(),
                Name = "Test Rule 1",
                TargetEntityName = "TestEntity",
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                RootNode = new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.String, Value = "test" }
            },
            new BusinessRule
            {
                Id = Guid.NewGuid(),
                Name = "Test Rule 2",
                TargetEntityName = "TestEntity",
                IsActive = false,
                CreatedAt = DateTime.UtcNow,
                RootNode = new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.String, Value = "test2" }
            }
        };

        _mockRepository.Setup(r => r.GetAllAsync()).ReturnsAsync(rules);

        // Act
        var result = await _controller.GetAllRules();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedRules = Assert.IsAssignableFrom<IEnumerable<BusinessRuleDto>>(okResult.Value);
        Assert.Equal(2, returnedRules.Count());
    }

    [Fact]
    public async Task GetRule_ExistingId_ReturnsOkWithRule()
    {
        // Arrange
        var ruleId = Guid.NewGuid();
        var rule = new BusinessRule
        {
            Id = ruleId,
            Name = "Test Rule",
            TargetEntityName = "TestEntity",
            IsActive = true,
            RootNode = new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.String, Value = "test" }
        };

        _mockRepository.Setup(r => r.GetByIdAsync(ruleId)).ReturnsAsync(rule);

        // Act
        var result = await _controller.GetRule(ruleId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedRule = Assert.IsType<BusinessRule>(okResult.Value);
        Assert.Equal(ruleId, returnedRule.Id);
    }

    [Fact]
    public async Task GetRule_NonExistingId_ReturnsNotFound()
    {
        // Arrange
        var ruleId = Guid.NewGuid();
        _mockRepository.Setup(r => r.GetByIdAsync(ruleId)).ReturnsAsync((BusinessRule?)null);

        // Act
        var result = await _controller.GetRule(ruleId);

        // Assert
        var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
        Assert.Contains(ruleId.ToString(), notFoundResult.Value?.ToString());
    }

    [Fact]
    public async Task CreateRule_ValidRule_ReturnsCreated()
    {
        // Arrange
        var createDto = new CreateRuleDto
        {
            Name = "New Test Rule",
            Description = "Test description",
            TargetEntityName = "TestEntity",
            TargetProperty = "Value",
            RootNode = new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Integer, Value = "42" },
            IsActive = true
        };

        _mockRepository.Setup(r => r.ExistsWithNameAsync(createDto.Name, null)).ReturnsAsync(false);
        _mockRepository.Setup(r => r.AddAsync(It.IsAny<BusinessRule>())).Returns(Task.CompletedTask);

        // Act
        var result = await _controller.CreateRule(createDto);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var createdRule = Assert.IsType<BusinessRule>(createdResult.Value);
        Assert.Equal(createDto.Name, createdRule.Name);
        Assert.Equal(createDto.TargetEntityName, createdRule.TargetEntityName);
    }

    [Fact]
    public async Task CreateRule_DuplicateName_ReturnsBadRequest()
    {
        // Arrange
        var createDto = new CreateRuleDto
        {
            Name = "Existing Rule",
            TargetEntityName = "TestEntity",
            RootNode = new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.String, Value = "test" }
        };

        _mockRepository.Setup(r => r.ExistsWithNameAsync(createDto.Name, null)).ReturnsAsync(true);

        // Act
        var result = await _controller.CreateRule(createDto);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
        Assert.Contains("již existuje", badRequestResult.Value?.ToString());
    }

    [Fact]
    public async Task CreateRule_InvalidSyntax_ReturnsBadRequest()
    {
        // Arrange - Pravidlo s neplatnou syntaxí (neexistující entita)
        var createDto = new CreateRuleDto
        {
            Name = "Invalid Rule",
            TargetEntityName = "NonExistentEntity",
            RootNode = new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.String, Value = "test" }
        };

        _mockRepository.Setup(r => r.ExistsWithNameAsync(createDto.Name, null)).ReturnsAsync(false);

        // Act
        var result = await _controller.CreateRule(createDto);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
        var errorResponse = badRequestResult.Value;
        Assert.NotNull(errorResponse);
    }

    [Fact]
    public async Task DeleteRule_ExistingRule_ReturnsNoContent()
    {
        // Arrange
        var ruleId = Guid.NewGuid();
        var rule = new BusinessRule
        {
            Id = ruleId,
            Name = "Rule to Delete",
            TargetEntityName = "TestEntity",
            RootNode = new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.String, Value = "test" }
        };

        _mockRepository.Setup(r => r.GetByIdAsync(ruleId)).ReturnsAsync(rule);
        _mockRepository.Setup(r => r.DeleteAsync(ruleId)).Returns(Task.CompletedTask);

        // Act
        var result = await _controller.DeleteRule(ruleId);

        // Assert
        Assert.IsType<NoContentResult>(result);
        _mockRepository.Verify(r => r.DeleteAsync(ruleId), Times.Once);
    }

    [Fact]
    public async Task DeleteRule_NonExistingRule_ReturnsNotFound()
    {
        // Arrange
        var ruleId = Guid.NewGuid();
        _mockRepository.Setup(r => r.GetByIdAsync(ruleId)).ReturnsAsync((BusinessRule?)null);

        // Act
        var result = await _controller.DeleteRule(ruleId);

        // Assert
        var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
        Assert.Contains(ruleId.ToString(), notFoundResult.Value?.ToString());
    }

    [Fact]
    public void GetMetadata_ReturnsCompleteMetadata()
    {
        // Act
        var result = _controller.GetMetadata();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var metadata = Assert.IsType<RuleMetadataDto>(okResult.Value);
        
        Assert.NotEmpty(metadata.AvailableEntities);
        Assert.NotEmpty(metadata.AvailableOperators);
        Assert.NotEmpty(metadata.AvailableAggregations);
        Assert.NotEmpty(metadata.AvailableValueTypes);
        Assert.Equal("1.0", metadata.SchemaVersion);
    }

    [Fact]
    public void GetEntityProperties_SupportedEntity_ReturnsProperties()
    {
        // Act
        var result = _controller.GetEntityProperties("SampleEntity");

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var properties = Assert.IsAssignableFrom<IEnumerable<PropertyMetadataDto>>(okResult.Value);
        Assert.NotEmpty(properties);
    }

    [Fact]
    public void GetEntityProperties_UnsupportedEntity_ReturnsNotFound()
    {
        // Act
        var result = _controller.GetEntityProperties("NonExistentEntity");

        // Assert
        var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
        Assert.Contains("není podporována", notFoundResult.Value?.ToString());
    }

    [Fact]
    public void ValidateRule_ValidRule_ReturnsValid()
    {
        // Arrange
        var validateDto = new ValidateRuleDto
        {
            Name = "Test Validation",
            TargetEntityName = "TestEntity",
            RootNode = new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Integer, Value = "42" }
        };

        // Act
        var result = _controller.ValidateRule(validateDto);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = okResult.Value;
        Assert.NotNull(response);
        
        // Ověříme, že response obsahuje IsValid property
        var responseType = response.GetType();
        var isValidProperty = responseType.GetProperty("IsValid");
        Assert.NotNull(isValidProperty);
        Assert.True((bool)isValidProperty.GetValue(response)!);
    }

    [Fact]
    public void ValidateRule_InvalidRule_ReturnsInvalid()
    {
        // Arrange - Neplatné pravidlo (neexistující entita)
        var validateDto = new ValidateRuleDto
        {
            Name = "Invalid Test",
            TargetEntityName = "NonExistentEntity",
            RootNode = new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.String, Value = "test" }
        };

        // Act
        var result = _controller.ValidateRule(validateDto);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = okResult.Value;
        Assert.NotNull(response);
        
        // Ověříme, že response obsahuje IsValid property a je false
        var responseType = response.GetType();
        var isValidProperty = responseType.GetProperty("IsValid");
        Assert.NotNull(isValidProperty);
        Assert.False((bool)isValidProperty.GetValue(response)!);
    }

    [Fact]
    public async Task TestRule_ValidRule_ReturnsSuccess()
    {
        // Arrange
        var ruleId = Guid.NewGuid();
        var rule = new BusinessRule
        {
            Id = ruleId,
            Name = "Test Rule",
            TargetEntityName = "TestEntity",
            RootNode = new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Integer, Value = "42" }
        };

        var testData = new { Value = 10 };

        _mockRepository.Setup(r => r.GetByIdAsync(ruleId)).ReturnsAsync(rule);

        // Act
        var result = await _controller.TestRule(ruleId, testData);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var response = okResult.Value;
        Assert.NotNull(response);
        
        // Ověříme, že response obsahuje Success property
        var responseType = response.GetType();
        var successProperty = responseType.GetProperty("Success");
        Assert.NotNull(successProperty);
        Assert.True((bool)successProperty.GetValue(response)!);
    }
}

// Testovací třídy jsou definovány v CalculationEngineTests.cs
