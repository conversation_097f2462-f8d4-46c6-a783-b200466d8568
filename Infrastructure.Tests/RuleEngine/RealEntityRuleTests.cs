using Domain.Entities;
using Infrastructure.RuleEngine;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Infrastructure.Tests.RuleEngine;

/// <summary>
/// Testy pro RuleEngine s reálnými entitami (Order, Invoice).
/// Ověřuje funkčnost obchodních pravidel na skutečných datech.
/// </summary>
public class RealEntityRuleTests
{
    private readonly CalculationEngine _engine;
    private readonly IReadOnlyDictionary<string, Type> _entityTypeMap;

    public RealEntityRuleTests()
    {
        _entityTypeMap = new Dictionary<string, Type>
        {
            ["Order"] = typeof(Order),
            ["OrderItem"] = typeof(OrderItem),
            ["Invoice"] = typeof(Invoice),
            ["InvoiceItem"] = typeof(InvoiceItem)
        };

        var dataProvider = new TestRuleDataProvider();
        var expressionBuilder = TestExpressionBuilderFactory.Create(dataProvider, _entityTypeMap);
        var mockLogger = new Mock<ILogger<CalculationEngine>>();
        _engine = new CalculationEngine(expressionBuilder, _entityTypeMap, mockLogger.Object);
    }

    [Fact]
    public void Execute_OrderTotalAmountRule_ReturnsCorrectValue()
    {
        // Arrange - Pravidlo: Celková částka objednávky
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Celková částka objednávky",
            TargetEntityName = "Order",
            RootNode = new SourceValueNode { SourcePath = "TotalAmount" }
        };

        var order = CreateTestOrder();

        // Act
        var result = _engine.Execute(rule, order);

        // Assert
        Assert.Equal(order.TotalAmount, result);
    }

    [Fact]
    public void Execute_OrderDiscountCalculation_ReturnsCorrectValue()
    {
        // Arrange - Pravidlo: Výpočet slevy 10% pro objednávky nad 5000 Kč
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Sleva 10% nad 5000 Kč",
            TargetEntityName = "Order",
            RootNode = new OperationNode
            {
                Operator = OperatorType.If,
                Operands = new List<RuleNode>
                {
                    // Podmínka: TotalAmount > 5000
                    new OperationNode
                    {
                        Operator = OperatorType.GreaterThan,
                        Operands = new List<RuleNode>
                        {
                            new SourceValueNode { SourcePath = "TotalAmount" },
                            new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "5000" }
                        }
                    },
                    // Pak: TotalAmount * 0.1
                    new OperationNode
                    {
                        Operator = OperatorType.Multiply,
                        Operands = new List<RuleNode>
                        {
                            new SourceValueNode { SourcePath = "TotalAmount" },
                            new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "0.1" }
                        }
                    },
                    // Jinak: 0
                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "0" }
                }
            }
        };

        var order = CreateTestOrder();

        // Act
        var result = _engine.Execute(rule, order);

        // Assert - Objednávka má 25000 Kč, takže sleva by měla být 2500 Kč
        Assert.Equal(2500m, result);
    }

    [Fact]
    public void Execute_OrderItemCountRule_ReturnsCorrectValue()
    {
        // Arrange - Pravidlo: Počet položek v objednávce
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Počet položek",
            TargetEntityName = "Order",
            RootNode = new SourceValueNode { SourcePath = "TotalItemCount" }
        };

        var order = CreateTestOrder();

        // Act
        var result = _engine.Execute(rule, order);

        // Assert
        Assert.Equal(order.TotalItemCount, result);
    }

    [Fact]
    public void Execute_InvoiceOverdueRule_ReturnsCorrectValue()
    {
        // Arrange - Pravidlo: Je faktura po splatnosti?
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Faktura po splatnosti",
            TargetEntityName = "Invoice",
            RootNode = new SourceValueNode { SourcePath = "IsOverdue" }
        };

        var invoice = CreateTestInvoice();

        // Act
        var result = _engine.Execute(rule, invoice);

        // Assert
        Assert.Equal(invoice.IsOverdue, result);
    }

    [Fact]
    public void Execute_InvoicePaymentPercentageRule_ReturnsCorrectValue()
    {
        // Arrange - Pravidlo: Procento zaplacené částky
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Procento zaplaceno",
            TargetEntityName = "Invoice",
            RootNode = new SourceValueNode { SourcePath = "PaymentPercentage" }
        };

        var invoice = CreateTestInvoice();

        // Act
        var result = _engine.Execute(rule, invoice);

        // Assert
        Assert.Equal(invoice.PaymentPercentage, result);
    }

    [Fact]
    public void Execute_OrderItemExpensiveRule_ReturnsCorrectValue()
    {
        // Arrange - Pravidlo: Je položka drahá?
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Drahá položka",
            TargetEntityName = "OrderItem",
            RootNode = new SourceValueNode { SourcePath = "IsExpensive" }
        };

        var orderItem = CreateTestOrderItem();

        // Act
        var result = _engine.Execute(rule, orderItem);

        // Assert
        Assert.Equal(orderItem.IsExpensive, result);
    }

    [Fact]
    public void Execute_ComplexBusinessRule_ReturnsCorrectValue()
    {
        // Arrange - Složité pravidlo: Poplatek za expresní doručení
        // Pokud je objednávka nad 10000 Kč a má více než 3 položky, poplatek je 0
        // Jinak je poplatek 500 Kč
        var rule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Poplatek za expresní doručení",
            TargetEntityName = "Order",
            RootNode = new OperationNode
            {
                Operator = OperatorType.If,
                Operands = new List<RuleNode>
                {
                    // Podmínka: TotalAmount > 10000 AND TotalItemCount > 3
                    new OperationNode
                    {
                        Operator = OperatorType.And,
                        Operands = new List<RuleNode>
                        {
                            new OperationNode
                            {
                                Operator = OperatorType.GreaterThan,
                                Operands = new List<RuleNode>
                                {
                                    new SourceValueNode { SourcePath = "TotalAmount" },
                                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "10000" }
                                }
                            },
                            new OperationNode
                            {
                                Operator = OperatorType.GreaterThan,
                                Operands = new List<RuleNode>
                                {
                                    new SourceValueNode { SourcePath = "TotalItemCount" },
                                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Integer, Value = "3" }
                                }
                            }
                        }
                    },
                    // Pak: 0
                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "0" },
                    // Jinak: 500
                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "500" }
                }
            }
        };

        var order = CreateTestOrder();

        // Act
        var result = _engine.Execute(rule, order);

        // Assert - Objednávka má 25000 Kč a 3 položky, takže poplatek by měl být 500 Kč
        Assert.Equal(500m, result);
    }

    /// <summary>
    /// Vytvoří testovací objednávku.
    /// </summary>
    private Order CreateTestOrder()
    {
        var order = new Order
        {
            Id = Guid.NewGuid(),
            OrderNumber = "TEST-001",
            OrderDate = DateTime.Now.AddDays(-10),
            CustomerName = "Test Customer",
            CustomerEmail = "<EMAIL>",
            Status = OrderStatus.Processing,
            SubTotal = 20661.16m,
            TaxAmount = 4338.84m,
            DiscountPercentage = 0m,
            DiscountAmount = 0m,
            ShippingCost = 0m,
            TotalAmount = 25000m,
            Currency = "CZK"
        };

        order.Items = new List<OrderItem>
        {
            CreateTestOrderItem(),
            new OrderItem
            {
                Id = Guid.NewGuid(),
                OrderId = order.Id,
                ProductCode = "PROD-002",
                ProductName = "Test Product 2",
                Category = "Test",
                UnitPrice = 5000m,
                Quantity = 1,
                TaxRate = 21m,
                LineTotal = 5000m,
                LineTaxAmount = 1050m,
                LineTotalWithTax = 6050m
            },
            new OrderItem
            {
                Id = Guid.NewGuid(),
                OrderId = order.Id,
                ProductCode = "PROD-003",
                ProductName = "Test Product 3",
                Category = "Test",
                UnitPrice = 3000m,
                Quantity = 1,
                TaxRate = 21m,
                LineTotal = 3000m,
                LineTaxAmount = 630m,
                LineTotalWithTax = 3630m
            }
        };

        return order;
    }

    /// <summary>
    /// Vytvoří testovací položku objednávky.
    /// </summary>
    private OrderItem CreateTestOrderItem()
    {
        return new OrderItem
        {
            Id = Guid.NewGuid(),
            ProductCode = "PROD-001",
            ProductName = "Test Product 1",
            Category = "Test",
            UnitPrice = 12000m,
            Quantity = 1,
            Weight = 2.5m,
            TaxRate = 21m,
            DiscountPercentage = 0m,
            LineTotal = 12000m,
            LineTaxAmount = 2520m,
            LineTotalWithTax = 14520m
        };
    }

    /// <summary>
    /// Vytvoří testovací fakturu.
    /// </summary>
    private Invoice CreateTestInvoice()
    {
        return new Invoice
        {
            Id = Guid.NewGuid(),
            InvoiceNumber = "INV-001",
            IssueDate = DateTime.Now.AddDays(-30),
            DueDate = DateTime.Now.AddDays(-15), // Po splatnosti
            CustomerName = "Test Customer",
            CustomerEmail = "<EMAIL>",
            Type = InvoiceType.Standard,
            Status = InvoiceStatus.PartiallyPaid,
            SubTotal = 10000m,
            TaxAmount = 2100m,
            TotalAmount = 12100m,
            PaidAmount = 6050m, // 50% zaplaceno
            RemainingAmount = 6050m,
            Currency = "CZK",
            PaymentMethod = PaymentMethod.BankTransfer
        };
    }
}

// TestRuleDataProvider je již definován v CalculationEngineTests.cs
