using Infrastructure.RuleEngine;
using Infrastructure.RuleEngine.Services;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Infrastructure.Tests.RuleEngine;

/// <summary>
/// Unit testy pro RuleCacheService.
/// </summary>
public class RuleCacheServiceTests : IDisposable
{
    private readonly RuleCacheService _cacheService;
    private readonly Mock<ILogger<RuleCacheService>> _mockLogger;

    public RuleCacheServiceTests()
    {
        _mockLogger = new Mock<ILogger<RuleCacheService>>();
        _cacheService = new RuleCacheService(_mockLogger.Object);
    }

    [Fact]
    public async Task GetOrSetAsync_WithAsyncFactory_ShouldCacheValue()
    {
        // Arrange
        const string key = "test-key";
        const string expectedValue = "test-value";
        var factoryCallCount = 0;

        // Act
        var result1 = await _cacheService.GetOrSetAsync(key, async () =>
        {
            factoryCallCount++;
            await Task.Delay(10); // Simulace async operace
            return expectedValue;
        });

        var result2 = await _cacheService.GetOrSetAsync(key, async () =>
        {
            factoryCallCount++;
            await Task.Delay(10);
            return "different-value";
        });

        // Assert
        Assert.Equal(expectedValue, result1);
        Assert.Equal(expectedValue, result2); // Měla by být cachovaná hodnota
        Assert.Equal(1, factoryCallCount); // Factory by měla být volána pouze jednou
    }

    [Fact]
    public async Task GetOrSetAsync_WithSyncFactory_ShouldCacheValue()
    {
        // Arrange
        const string key = "sync-test-key";
        const int expectedValue = 42;
        var factoryCallCount = 0;

        // Act
        var result1 = await _cacheService.GetOrSetAsync(key, () =>
        {
            factoryCallCount++;
            return expectedValue;
        });

        var result2 = await _cacheService.GetOrSetAsync(key, () =>
        {
            factoryCallCount++;
            return 999;
        });

        // Assert
        Assert.Equal(expectedValue, result1);
        Assert.Equal(expectedValue, result2);
        Assert.Equal(1, factoryCallCount);
    }

    [Fact]
    public async Task GetOrSetAsync_WithExpiredEntry_ShouldCallFactoryAgain()
    {
        // Arrange
        const string key = "expiry-test-key";
        const string value1 = "first-value";
        const string value2 = "second-value";
        var factoryCallCount = 0;

        // Act
        var result1 = await _cacheService.GetOrSetAsync(key, () =>
        {
            factoryCallCount++;
            return value1;
        }, TimeSpan.FromMilliseconds(50));

        // Počkáme na expiraci
        await Task.Delay(100);

        var result2 = await _cacheService.GetOrSetAsync(key, () =>
        {
            factoryCallCount++;
            return value2;
        }, TimeSpan.FromMilliseconds(50));

        // Assert
        Assert.Equal(value1, result1);
        Assert.Equal(value2, result2); // Měla by být nová hodnota po expiraci
        Assert.Equal(2, factoryCallCount);
    }

    [Fact]
    public async Task InvalidateAsync_WithWildcardPattern_ShouldRemoveMatchingKeys()
    {
        // Arrange
        await _cacheService.GetOrSetAsync("rules:entity:User", () => "user-rules");
        await _cacheService.GetOrSetAsync("rules:entity:Order", () => "order-rules");
        await _cacheService.GetOrSetAsync("compiled:123:abc", () => "compiled-rule");
        await _cacheService.GetOrSetAsync("other:key", () => "other-value");

        // Act
        var invalidatedCount = await _cacheService.InvalidateAsync("rules:entity:*");

        // Assert
        Assert.Equal(2, invalidatedCount);

        // Ověříme, že správné klíče byly odstraněny
        var userRules = await _cacheService.GetOrSetAsync("rules:entity:User", () => "new-user-rules");
        var orderRules = await _cacheService.GetOrSetAsync("rules:entity:Order", () => "new-order-rules");
        var compiledRule = await _cacheService.GetOrSetAsync("compiled:123:abc", () => "new-compiled-rule");
        var otherValue = await _cacheService.GetOrSetAsync("other:key", () => "new-other-value");

        Assert.Equal("new-user-rules", userRules); // Byl invalidován
        Assert.Equal("new-order-rules", orderRules); // Byl invalidován
        Assert.Equal("compiled-rule", compiledRule); // Nebyl invalidován
        Assert.Equal("other-value", otherValue); // Nebyl invalidován
    }

    [Fact]
    public async Task InvalidateAsync_WithQuestionMarkPattern_ShouldMatchSingleCharacter()
    {
        // Arrange
        await _cacheService.GetOrSetAsync("test1", () => "value1");
        await _cacheService.GetOrSetAsync("test2", () => "value2");
        await _cacheService.GetOrSetAsync("test10", () => "value10");

        // Act
        var invalidatedCount = await _cacheService.InvalidateAsync("test?");

        // Assert
        Assert.Equal(2, invalidatedCount); // test1 a test2, ale ne test10

        // Ověření
        var value1 = await _cacheService.GetOrSetAsync("test1", () => "new-value1");
        var value2 = await _cacheService.GetOrSetAsync("test2", () => "new-value2");
        var value10 = await _cacheService.GetOrSetAsync("test10", () => "new-value10");

        Assert.Equal("new-value1", value1);
        Assert.Equal("new-value2", value2);
        Assert.Equal("value10", value10); // Nebyl invalidován
    }

    [Fact]
    public async Task RemoveAsync_ShouldRemoveSpecificKey()
    {
        // Arrange
        const string key = "remove-test-key";
        const string value = "test-value";
        await _cacheService.GetOrSetAsync(key, () => value);

        // Act
        var removed = await _cacheService.RemoveAsync(key);

        // Assert
        Assert.True(removed);

        // Ověříme, že klíč byl skutečně odstraněn
        var newValue = await _cacheService.GetOrSetAsync(key, () => "new-value");
        Assert.Equal("new-value", newValue);
    }

    [Fact]
    public async Task RemoveAsync_WithNonExistentKey_ShouldReturnFalse()
    {
        // Act
        var removed = await _cacheService.RemoveAsync("non-existent-key");

        // Assert
        Assert.False(removed);
    }

    [Fact]
    public async Task ClearAsync_ShouldRemoveAllEntries()
    {
        // Arrange
        await _cacheService.GetOrSetAsync("key1", () => "value1");
        await _cacheService.GetOrSetAsync("key2", () => "value2");
        await _cacheService.GetOrSetAsync("key3", () => "value3");

        // Act
        await _cacheService.ClearAsync();

        // Assert
        var stats = _cacheService.GetStatistics();
        Assert.Equal(0, stats.TotalEntries);
        Assert.Equal(0, stats.HitCount);
        Assert.Equal(0, stats.MissCount);

        // Ověříme, že všechny klíče byly odstraněny
        var value1 = await _cacheService.GetOrSetAsync("key1", () => "new-value1");
        var value2 = await _cacheService.GetOrSetAsync("key2", () => "new-value2");
        var value3 = await _cacheService.GetOrSetAsync("key3", () => "new-value3");

        Assert.Equal("new-value1", value1);
        Assert.Equal("new-value2", value2);
        Assert.Equal("new-value3", value3);
    }

    [Fact]
    public async Task GetStatistics_ShouldTrackHitsAndMisses()
    {
        // Arrange
        const string key = "stats-test-key";
        const string value = "test-value";

        // Act
        // První volání - miss
        await _cacheService.GetOrSetAsync(key, () => value);
        
        // Druhé volání - hit
        await _cacheService.GetOrSetAsync(key, () => "different-value");
        
        // Třetí volání - hit
        await _cacheService.GetOrSetAsync(key, () => "another-value");

        var stats = _cacheService.GetStatistics();

        // Assert
        Assert.Equal(1, stats.TotalEntries);
        Assert.Equal(2, stats.HitCount);
        Assert.Equal(1, stats.MissCount);
        Assert.Equal(3, stats.TotalRequests);
        Assert.Equal(66.67, Math.Round(stats.HitRate, 2)); // 2/3 * 100
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public async Task GetOrSetAsync_WithInvalidKey_ShouldThrowArgumentException(string invalidKey)
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() =>
            _cacheService.GetOrSetAsync(invalidKey, () => "value"));
    }

    [Fact]
    public async Task GetOrSetAsync_WithNullFactory_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _cacheService.GetOrSetAsync<string>("key", (Func<Task<string>>)null!));
    }

    public void Dispose()
    {
        _cacheService?.Dispose();
    }
}
