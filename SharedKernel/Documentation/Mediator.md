# Dokumentace Mediator komponenty

## Přehled

Mediator komponenta implementuje návrhový vzor **Mediator** v aplikaci DataCapture. S<PERSON>ž<PERSON> jako centrální bod pro zpracování po<PERSON>ů (requests) a publikování notifikací (notifications) mezi různými částmi aplikace. Hlavním cílem je snížení propojení mezi komponentami a centralizace komunikační logiky.

## Architektura

### Umístění v projektu

```
SharedKernel/
├── Abstractions/Mediator/          # Rozhraní a abstrakce
│   ├── IMediator.cs               # Hlavní rozhraní mediátoru
│   ├── IRequest.cs                # Rozhraní pro požadavky
│   ├── IRequestHandler.cs         # Rozhraní pro handlery požadavků
│   ├── INotification.cs           # Rozhraní pro notifikace
│   ├── INotificationHandler.cs    # Rozhraní pro handlery notifika<PERSON><PERSON>
│   ├── INotificationPublisher.cs  # Rozhraní pro publisher notif<PERSON><PERSON><PERSON>
│   ├── IPipelineBehavior.cs       # Rozhraní pro pipeline behaviors
│   └── RequestHandlerDelegate.cs  # Delegát pro pipeline
└── Infrastructure/Mediator/        # Implementace
    ├── Mediator.cs                # Hlavní implementace mediátoru
    └── ParallelNotificationPublisher.cs  # Paralelní publisher
```

## Hlavní komponenty

### 1. IMediator

Hlavní rozhraní mediátoru poskytující dvě základní operace:

```csharp
public interface IMediator
{
    Task<TResponse> Send<TResponse>(IRequest<TResponse> request, CancellationToken cancellationToken = default);
    Task Publish<TNotification>(TNotification notification, CancellationToken cancellationToken = default) where TNotification : INotification;
}
```

**Metody:**
- `Send<TResponse>()` - Odešle požadavek příslušnému handleru a vrátí výsledek
- `Publish<TNotification>()` - Publikuje notifikaci všem registrovaným handlerům

### 2. Mediator (implementace)

Konkrétní implementace rozhraní `IMediator` s podporou pipeline behaviors.

**Závislosti:**
- `IServiceProvider` - Pro získání handlerů a behaviors z DI kontejneru
- `INotificationPublisher` - Pro distribuci notifikací

**Klíčové funkce:**
- **Pipeline zpracování** - Automatické řetězení pipeline behaviors před a po zpracování požadavku
- **Dynamické získání handlerů** - Použití reflection pro nalezení správného handleru
- **Podpora behaviors** - Možnost přidat cross-cutting concerns (cache, validace, logování)

### 3. Request/Response pattern

#### IRequest<TResponse>
Marker interface pro požadavky, které očekávají odpověď určitého typu.

#### IRequestHandler<TRequest, TResponse>
Rozhraní pro handlery zpracovávající konkrétní typ požadavku:

```csharp
public interface IRequestHandler<in TRequest, TResponse> where TRequest : IRequest<TResponse>
{
    Task<TResponse> Handle(TRequest request, CancellationToken cancellationToken);
}
```

### 4. Notification pattern

#### INotification
Marker interface pro notifikace, které mohou být zpracovány více handlery současně.

#### INotificationHandler<TNotification>
Rozhraní pro handlery zpracovávající notifikace:

```csharp
public interface INotificationHandler<TNotification>
{
    Task Handle(TNotification notification, CancellationToken cancellationToken);
}
```

### 5. Pipeline Behaviors

#### IPipelineBehavior<TRequest, TResponse>
Umožňuje přidat dodatečnou logiku před a po zpracování požadavku (Decorator pattern):

```csharp
public interface IPipelineBehavior<in TRequest, TResponse> where TRequest : IRequest<TResponse>
{
    Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken);
}
```

**Příklady použití:**
- Cache behavior - Ukládání a načítání z cache
- Validation behavior - Validace vstupních dat
- Logging behavior - Logování požadavků a odpovědí
- Performance behavior - Měření výkonu

### 6. Notification Publisher

#### INotificationPublisher
Rozhraní pro různé strategie publikování notifikací:

```csharp
public interface INotificationPublisher
{
    Task Publish<TNotification>(IEnumerable<INotificationHandler<TNotification>> handlers, TNotification notification, CancellationToken cancellationToken) where TNotification : INotification;
}
```

#### ParallelNotificationPublisher
Implementace, která spouští všechny handlery paralelně pomocí `Task.WhenAll()`.

## Tok zpracování

### Request zpracování

1. **Volání Send()** - Klient volá `mediator.Send(request)`
2. **Nalezení handleru** - Mediator najde odpovídající `IRequestHandler<TRequest, TResponse>`
3. **Získání behaviors** - Načtení všech registrovaných `IPipelineBehavior<TRequest, TResponse>`
4. **Vytvoření pipeline** - Řetězení behaviors v opačném pořadí registrace
5. **Spuštění pipeline** - Postupné volání behaviors až k finálnímu handleru
6. **Vrácení výsledku** - Výsledek prochází zpět přes všechny behaviors

### Notification zpracování

1. **Volání Publish()** - Klient volá `mediator.Publish(notification)`
2. **Nalezení handlerů** - Mediator najde všechny `INotificationHandler<TNotification>`
3. **Delegování na publisher** - Předání handlerů a notifikace na `INotificationPublisher`
4. **Paralelní zpracování** - Publisher spustí všechny handlery současně

## Příklady použití

### Základní Request/Response

```csharp
// Definice požadavku
public class GetUserQuery : IRequest<UserDto>
{
    public int UserId { get; set; }
}

// Handler
public class GetUserQueryHandler : IRequestHandler<GetUserQuery, UserDto>
{
    public async Task<UserDto> Handle(GetUserQuery request, CancellationToken cancellationToken)
    {
        // Logika získání uživatele
        return new UserDto();
    }
}

// Použití
var user = await mediator.Send(new GetUserQuery { UserId = 1 });
```

### Notifikace

```csharp
// Definice notifikace
public class UserCreatedNotification : INotification
{
    public int UserId { get; set; }
    public string Email { get; set; }
}

// Handler 1 - Email
public class SendWelcomeEmailHandler : INotificationHandler<UserCreatedNotification>
{
    public async Task Handle(UserCreatedNotification notification, CancellationToken cancellationToken)
    {
        // Odeslání uvítacího emailu
    }
}

// Handler 2 - Audit
public class LogUserCreationHandler : INotificationHandler<UserCreatedNotification>
{
    public async Task Handle(UserCreatedNotification notification, CancellationToken cancellationToken)
    {
        // Zalogování vytvoření uživatele
    }
}

// Použití
await mediator.Publish(new UserCreatedNotification { UserId = 1, Email = "<EMAIL>" });
```

### Pipeline Behavior

```csharp
public class LoggingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly ILogger<LoggingBehavior<TRequest, TResponse>> _logger;

    public LoggingBehavior(ILogger<LoggingBehavior<TRequest, TResponse>> logger)
    {
        _logger = logger;
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Zpracování požadavku {RequestType}", typeof(TRequest).Name);
        
        var response = await next();
        
        _logger.LogInformation("Požadavek {RequestType} zpracován", typeof(TRequest).Name);
        
        return response;
    }
}
```

## Výhody použití

1. **Snížení propojení** - Komponenty komunikují přes mediátor místo přímých závislostí
2. **Centralizace logiky** - Veškerá komunikační logika na jednom místě
3. **Rozšiřitelnost** - Snadné přidání nových handlerů a behaviors
4. **Testovatelnost** - Jednodušší mockování a unit testování
5. **Cross-cutting concerns** - Elegantní řešení pro cache, validaci, logování
6. **Konzistence** - Jednotný způsob zpracování požadavků napříč aplikací

## Registrace v DI kontejneru

Mediator komponenty se registrují v Infrastructure vrstvě:

```csharp
services.AddScoped<IMediator, Mediator>();
services.AddScoped<INotificationPublisher, ParallelNotificationPublisher>();

// Automatická registrace handlerů
services.AddMediatR(typeof(AssemblyMarker));

// Registrace behaviors
services.AddScoped(typeof(IPipelineBehavior<,>), typeof(LoggingBehavior<,>));
services.AddScoped(typeof(IPipelineBehavior<,>), typeof(CacheBehavior<,>));
```

## Testování

Mediator komponenty jsou navrženy pro snadné testování:

```csharp
[Test]
public async Task Should_Handle_Request_Through_Mediator()
{
    // Arrange
    var serviceProvider = CreateServiceProvider();
    var mediator = new Mediator(serviceProvider, new ParallelNotificationPublisher());
    var request = new TestRequest { Data = "test" };

    // Act
    var response = await mediator.Send<TestResponse>(request);

    // Assert
    Assert.That(response.Result, Is.EqualTo("Handled: test"));
}
```

## Závěr

Mediator komponenta poskytuje robustní a flexibilní řešení pro komunikaci mezi komponentami aplikace. Díky podpoře pipeline behaviors umožňuje elegantní implementaci cross-cutting concerns a zachovává čistou architekturu aplikace.
