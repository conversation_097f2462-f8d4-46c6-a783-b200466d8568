using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using SharedKernel.Abstractions.Mediator;

namespace SharedKernel.Infrastructure.Mediator;

/// <summary>
/// Implementace INotificationPublisher, kter<PERSON> publikuje notifikace paralelně všem handlerům současně.
/// Tato strategie je vhodná, když handlery jsou nezá<PERSON> a mohou běžet současně.
/// </summary>
public class ParallelNotificationPublisher : INotificationPublisher
{
    /// <summary>
    /// Publikuje notifikaci paralelně všem zadaným handlerům.
    /// Všechny handlery jsou spuštěny současně a metoda čeká na dokončení všech.
    /// </summary>
    /// <typeparam name="TNotification">Typ notifikace.</typeparam>
    /// <param name="handlers"><PERSON><PERSON><PERSON> handler<PERSON>, kter<PERSON> mají notifika<PERSON> zpracovat.</param>
    /// <param name="notification">Notif<PERSON><PERSON>, kter<PERSON> má být publikována.</param>
    /// <param name="cancellationToken">Token pro zrušení operace.</param>
    /// <returns>Task reprezentující asynchronní operaci, která je dokončena, když všechny handlery dokončí zpracování.</returns>
    public Task Publish<TNotification>(
        IEnumerable<INotificationHandler<TNotification>> handlers,
        TNotification notification,
        CancellationToken cancellationToken)
        where TNotification : INotification
    {
        // Vytvoření tasků pro všechny handlery
        var tasks = handlers.Select(h => h.Handle(notification, cancellationToken));

        // Čekání na dokončení všech tasků
        return Task.WhenAll(tasks);
    }
}
