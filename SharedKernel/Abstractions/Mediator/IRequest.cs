namespace SharedKernel.Abstractions.Mediator;

/// <summary>
/// Rozhraní pro požadavek, kter<PERSON> je zpracován mediátorem a vrací výsledek.
/// Slouží jako marker interface pro identifika<PERSON> tříd, kter<PERSON> p<PERSON>tavují po<PERSON>.
/// </summary>
/// <typeparam name="TResponse"><PERSON><PERSON> odpovědi, který bude vrácen po zpracování požadavku.</typeparam>
public interface IRequest<TResponse> { }
