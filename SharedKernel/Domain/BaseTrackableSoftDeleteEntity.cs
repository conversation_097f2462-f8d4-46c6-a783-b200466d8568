using System;

namespace SharedKernel.Domain;

/// <summary>
/// Abstraktní třída kombinuj<PERSON><PERSON><PERSON> sledování změn a měkké mazání.
/// Poskytuje vlastnosti pro sledování vytvoření, modifikace i smazání entity.
/// Používá DateTimeOffset pro správnou práci s časovými pásmy.
/// </summary>
/// <typeparam name="T">Typ identifikátoru entity (např. int, Guid, string)</typeparam>
public abstract class BaseTrackableSoftDeleteEntity<T> : BaseTrackableEntity<T>, ISoftDelete
{
    /// <summary>
    /// Datum a čas smazání entity.
    /// Null hodnota znamená, že entita nebyla smazána.
    /// Používá DateTimeOffset pro správnou práci s časovými pásmy.
    /// </summary>
    public DateTimeOffset? DeletedAt { get; set; }

    /// <summary>
    /// Identifik<PERSON><PERSON> už<PERSON>, kter<PERSON> entitu smazal.
    /// </summary>
    public string? DeletedBy { get; set; }
}