using System;
using System.Collections.Generic;
using System.Linq;

namespace SharedKernel.Domain;
/// <summary>
/// Jednoduchá základní třída pro entity s int ID.
/// Pokrývá většinu případů použití a zjednodušuje deklaraci entit.
/// Pro speciální případy (Guid, string ID) použijte BaseEntity<T>.
/// </summary>
public abstract class BaseEntity : IEntity<int>
{
    // Dědí vše z IEntity<int>, ale poskytuje jednodušší syntaxi
    public int Id { get; set; }
    public byte[] RowVersion { get; set; }
}

/// <summary>
/// Základní abstraktní třída pro všechny entity v doméně.
/// Poskytuje základní funkcionalitu pro identifikaci entity a práci s doménovými událostmi.
/// </summary>
/// <typeparam name="T">Typ identifikátoru entity (např. int, Guid, string)</typeparam>
public abstract class BaseEntity<T> : IEntity<T> 
{
    /// <summary>
    /// Privátní kolekce doménových událostí spojených s touto entitou.
    /// </summary>
    private readonly List<DomainEvent> _domainEvents = new();

    /// <summary>
    /// Veřejná kolekce doménových událostí pro čtení.
    /// </summary>
    public IReadOnlyCollection<DomainEvent> DomainEvents => _domainEvents.AsReadOnly();

    /// <summary>
    /// Jedinečný identifikátor entity.
    /// </summary>
    public virtual required T Id { get; set; }
    
    /// <summary>
    /// Verze řádku pro optimistické zamykání.
    /// Pro SQLite je hodnota nastavována manuálně v ApplicationDbContext.
    /// </summary>
    public byte[] RowVersion { get; set; } = new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 };

    /// <summary>
    /// Přidá novou doménovou událost do kolekce událostí entity.
    /// </summary>
    /// <param name="domainEvent">Doménová událost k přidání</param>
    public void AddDomainEvent(DomainEvent domainEvent)
    {
        if (domainEvent == null)
            throw new ArgumentNullException(nameof(domainEvent));

        _domainEvents.Add(domainEvent);
    }

    /// <summary>
    /// Přidá typovanou doménovou událost do kolekce událostí entity.
    /// </summary>
    /// <typeparam name="TEvent">Typ doménové události</typeparam>
    /// <param name="domainEvent">Doménová událost k přidání</param>
    public void AddDomainEvent<TEvent>(TEvent domainEvent) where TEvent : DomainEvent
    {
        if (domainEvent == null)
            throw new ArgumentNullException(nameof(domainEvent));

        _domainEvents.Add(domainEvent);
    }

    /// <summary>
    /// Odstraní doménovou událost z kolekce událostí entity.
    /// </summary>
    /// <param name="domainEvent">Doménová událost k odstranění</param>
    public void RemoveDomainEvent(DomainEvent domainEvent)
    {
        if (domainEvent != null)
            _domainEvents.Remove(domainEvent);
    }

    /// <summary>
    /// Získá všechny události určitého typu.
    /// </summary>
    /// <typeparam name="TEvent">Typ doménové události</typeparam>
    /// <returns>Kolekce událostí zadaného typu</returns>
    public IEnumerable<TEvent> GetDomainEvents<TEvent>() where TEvent : DomainEvent
    {
        return _domainEvents.OfType<TEvent>();
    }

    /// <summary>
    /// Kontroluje, zda entita obsahuje událost určitého typu.
    /// </summary>
    /// <typeparam name="TEvent">Typ doménové události</typeparam>
    /// <returns>True, pokud entita obsahuje událost zadaného typu</returns>
    public bool HasDomainEvent<TEvent>() where TEvent : DomainEvent
    {
        return _domainEvents.OfType<TEvent>().Any();
    }

    /// <summary>
    /// Vyčistí všechny doménové události z kolekce.
    /// Typicky voláno po zpracování všech událostí.
    /// </summary>
    public void ClearDomainEvents()
    {
        _domainEvents.Clear();
    }

    /// <summary>
    /// Vyčistí pouze události určitého typu.
    /// </summary>
    /// <typeparam name="TEvent">Typ doménové události k vyčištění</typeparam>
    public void ClearDomainEvents<TEvent>() where TEvent : DomainEvent
    {
        var eventsToRemove = _domainEvents.OfType<TEvent>().ToList();
        foreach (var eventToRemove in eventsToRemove)
        {
            _domainEvents.Remove(eventToRemove);
        }
    }

    /// <summary>
    /// Získá počet nepublikovaných událostí.
    /// </summary>
    public int UnpublishedEventsCount => _domainEvents.Count(e => !e.IsPublished);
}
