using Application.Abstraction;
using Application.Features.Generic;
using SharedKernel.Abstractions.Mediator;

namespace Application.Pipeline;

public class CacheInvalidationBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IInvalidateCache, IRequest<TResponse>
{
    private readonly ICacheService _cacheService;

    public CacheInvalidationBehavior(ICacheService cacheService)
    {
        _cacheService = cacheService;
    }

    public async Task<TResponse> Handle(
        TRequest request,
        RequestHandlerDelegate<TResponse> next,
        CancellationToken cancellationToken)
    {
        // Nejdřív vykonáme příkaz (změnu)
        var response = await next();

        // Poté smažeme všechny klíče, které příkaz deklaroval
        foreach (var key in request.CacheKeys)
        {
            if (!string.IsNullOrWhiteSpace(key))
                await _cacheService.RemoveAsync(key, cancellationToken);
        }

        // Smažeme také všechny cache záznamy podle tagů
        if (request.CacheTags != null)
        {
            foreach (var tag in request.CacheTags)
            {
                if (!string.IsNullOrWhiteSpace(tag))
                    await _cacheService.RemoveByTagAsync(tag, cancellationToken);
            }
        }

        return response;
    }
}