namespace Application.Abstraction;

/// <summary>
/// Služba pro správu informací o relaci uživatele.
/// </summary>
public interface ISessionInfoService
{
    /// <summary>
    /// Informace o aktuální relaci uživatele.
    /// </summary>
    SessionInfo Session { get; }

    /// <summary>
    /// Inicializuje informace o relaci.
    /// </summary>
    /// <returns>Task reprezentující asynchronní operaci.</returns>
    Task InitializeAsync();
}

/// <summary>
/// Informace o relaci uživatele.
/// </summary>
public class SessionInfo
{
    /// <summary>
    /// ID uživatele.
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// Jméno uživatele.
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// Email uživatele.
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Zda je uživatel přihl<PERSON>en.
    /// </summary>
    public bool IsAuthenticated { get; set; }

    /// <summary>
    /// Role uživatele.
    /// </summary>
    public List<string> Roles { get; set; } = new();
}
