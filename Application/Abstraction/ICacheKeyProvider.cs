namespace Application.Abstraction;

/// <summary>
/// Rozhraní pro poskytování konfigurovatelných cache klíčů.
/// Umožňuje dynamické nastavení cache prefixu podle konfigurace aplikace.
/// </summary>
public interface ICacheKeyProvider
{
    /// <summary>
    /// Prefix aplikace pro cache klíče.
    /// </summary>
    string AppPrefix { get; }

    /// <summary>
    /// Separator používaný v cache klíčích.
    /// </summary>
    string Separator { get; }

    /// <summary>
    /// Vytvoří cache klíč s aplikačním prefixem.
    /// </summary>
    /// <param name="key">Základní klíč</param>
    /// <returns>Kompletní cache klíč s prefixem</returns>
    string WithPrefix(string key);

    /// <summary>
    /// Vytvoří cache klíč pro entitu.
    /// </summary>
    /// <param name="entityName">Název entity</param>
    /// <param name="operation">Typ operace (GetAll, GetPaged, atd.)</param>
    /// <returns>Cache klíč pro entitu</returns>
    string ForEntity(string entityName, string operation);

    /// <summary>
    /// Vytvoří cache klíč pro stránkovaný dotaz.
    /// </summary>
    /// <param name="entityName">Název entity</param>
    /// <param name="pageNumber">Číslo stránky</param>
    /// <param name="pageSize">Velikost stránky</param>
    /// <param name="sortBy">Řazení podle</param>
    /// <param name="sortDescending">Sestupné řazení</param>
    /// <returns>Cache klíč pro stránkovaný dotaz</returns>
    string ForPagedQuery(string entityName, int pageNumber, int pageSize, 
        string? sortBy = null, bool sortDescending = false);

    /// <summary>
    /// Vytvoří cache klíč pro uživatelská data.
    /// </summary>
    /// <param name="userId">ID uživatele</param>
    /// <param name="dataType">Typ dat</param>
    /// <returns>Cache klíč pro uživatelská data</returns>
    string ForUser(string userId, string dataType);
}
