using Domain.Events;
using Microsoft.Extensions.Logging;
using SharedKernel.Abstractions.Mediator;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Application.Features.Events;

/// <summary>
/// Handler pro zpracování událostí změn entit.
/// Tento handler slou<PERSON><PERSON> jako ukázka zpracování doménových událostí generovaných systémem.
/// </summary>
public class EntityChangedEventHandler : INotificationHandler<DomainEventNotification<EntityChangedEvent>>
{
    private readonly ILogger<EntityChangedEventHandler> _logger;

    /// <summary>
    /// Inicializuje novou instanci EntityChangedEventHandler.
    /// </summary>
    /// <param name="logger">Logger</param>
    public EntityChangedEventHandler(ILogger<EntityChangedEventHandler> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Zpracuje notifikaci o změně entity.
    /// </summary>
    /// <param name="notification">Notifikace obsahující doménovou událost</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    public async Task Handle(DomainEventNotification<EntityChangedEvent> notification, CancellationToken cancellationToken)
    {
        var domainEvent = notification.DomainEvent;

        _logger.LogInformation(
            "Zpracovávám doménovou událost: {EventName} pro entitu {EntityType} (ID: {EntityId}) - operace: {Operation}",
            domainEvent.EventName,
            domainEvent.EntityTypeName,
            domainEvent.EntityId,
            domainEvent.Operation);

        // Zpracování podle typu operace
        switch (domainEvent.Operation)
        {
            case "Created":
                await HandleEntityCreated(domainEvent, cancellationToken);
                break;

            case "Updated":
                await HandleEntityUpdated(domainEvent, cancellationToken);
                break;

            case "Deleted":
                await HandleEntityDeleted(domainEvent, cancellationToken);
                break;

            default:
                _logger.LogWarning("Neznámý typ operace: {Operation}", domainEvent.Operation);
                break;
        }

        _logger.LogDebug("Dokončeno zpracování události {EventName}", domainEvent.EventName);
    }

    /// <summary>
    /// Zpracuje událost vytvoření entity.
    /// </summary>
    /// <param name="domainEvent">Doménová událost</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    private async Task HandleEntityCreated(EntityChangedEvent domainEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Nová entita {EntityType} byla vytvořena s ID: {EntityId}",
            domainEvent.EntityTypeName, domainEvent.EntityId);

        // Zde můžete implementovat specifickou logiku pro vytvoření entity
        // Například:
        // - Odeslání notifikace
        // - Aktualizace cache
        // - Spuštění workflow
        // - Audit logging

        await Task.CompletedTask;
    }

    /// <summary>
    /// Zpracuje událost aktualizace entity.
    /// </summary>
    /// <param name="domainEvent">Doménová událost</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    private async Task HandleEntityUpdated(EntityChangedEvent domainEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Entita {EntityType} (ID: {EntityId}) byla aktualizována. Změněné vlastnosti: {ChangedProperties}",
            domainEvent.EntityTypeName, domainEvent.EntityId, string.Join(", ", domainEvent.ChangedProperties));

        // Logování změn vlastností
        foreach (var propertyName in domainEvent.ChangedProperties)
        {
            var originalValue = domainEvent.GetOriginalValue(propertyName);
            var currentValue = domainEvent.GetCurrentValue(propertyName);

            _logger.LogDebug("Vlastnost {PropertyName} změněna z '{OriginalValue}' na '{CurrentValue}'",
                propertyName, originalValue, currentValue);
        }

        // Zde můžete implementovat specifickou logiku pro aktualizaci entity
        // Například:
        // - Invalidace cache pro konkrétní vlastnosti
        // - Spuštění business rules
        // - Synchronizace s externími systémy
        // - Notifikace uživatelů o změnách

        await Task.CompletedTask;
    }

    /// <summary>
    /// Zpracuje událost smazání entity.
    /// </summary>
    /// <param name="domainEvent">Doménová událost</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task</returns>
    private async Task HandleEntityDeleted(EntityChangedEvent domainEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Entita {EntityType} (ID: {EntityId}) byla smazána",
            domainEvent.EntityTypeName, domainEvent.EntityId);

        // Zde můžete implementovat specifickou logiku pro smazání entity
        // Například:
        // - Vyčištění cache
        // - Smazání souvisejících dat
        // - Archivace dat
        // - Notifikace o smazání

        await Task.CompletedTask;
    }
}
