namespace Application.Features.Orders;

/// <summary>
/// DTO pro čtení položky objednávky.
/// </summary>
public class OrderItemDto
{
    /// <summary>
    /// Jedinečný identifikátor polo<PERSON>.
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// ID objednávky.
    /// </summary>
    public Guid OrderId { get; set; }

    /// <summary>
    /// Kód produktu.
    /// </summary>
    public string ProductCode { get; set; } = string.Empty;

    /// <summary>
    /// Název produktu.
    /// </summary>
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// Popis produktu.
    /// </summary>
    public string? ProductDescription { get; set; }

    /// <summary>
    /// Kategorie produktu.
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// Jednotková cena bez DPH.
    /// </summary>
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// Množství.
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// Jednotka měření.
    /// </summary>
    public string Unit { get; set; } = "ks";

    /// <summary>
    /// Hmotnost jedné jednotky v kg.
    /// </summary>
    public decimal Weight { get; set; }

    /// <summary>
    /// Sazba DPH v procentech.
    /// </summary>
    public decimal TaxRate { get; set; } = 21;

    /// <summary>
    /// Sleva na položku v procentech.
    /// </summary>
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// Celková cena bez DPH.
    /// </summary>
    public decimal LineTotal { get; set; }

    /// <summary>
    /// Výše DPH pro tuto položku.
    /// </summary>
    public decimal LineTaxAmount { get; set; }

    /// <summary>
    /// Celková cena včetně DPH.
    /// </summary>
    public decimal LineTotalWithTax { get; set; }

    /// <summary>
    /// Poznámky k položce.
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Určuje, zda je produkt na skladě.
    /// </summary>
    public bool IsInStock { get; set; } = true;

    /// <summary>
    /// Očekávané datum dodání této položky.
    /// </summary>
    public DateTime? ExpectedDeliveryDate { get; set; }

    /// <summary>
    /// Datum vytvoření záznamu.
    /// </summary>
    public DateTime? CreatedAt { get; set; }

    /// <summary>
    /// Kdo vytvořil záznam.
    /// </summary>
    public string? CreatedBy { get; set; }

    /// <summary>
    /// Datum poslední úpravy záznamu.
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Kdo naposledy upravil záznam.
    /// </summary>
    public string? ModifiedBy { get; set; }

    /// <summary>
    /// Verze řádku pro optimistické zamykání.
    /// </summary>
    public byte[]? RowVersion { get; set; }

    /// <summary>
    /// Celková hmotnost této položky.
    /// </summary>
    public decimal TotalWeight { get; set; }

    /// <summary>
    /// Částka slevy.
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// Určuje, zda je položka drahá (nad 10 000 Kč).
    /// </summary>
    public bool IsExpensive { get; set; }

    /// <summary>
    /// Určuje, zda je položka těžká (nad 5 kg).
    /// </summary>
    public bool IsHeavy { get; set; }
}

/// <summary>
/// DTO pro editaci položky objednávky.
/// </summary>
public class OrderItemAddEdit
{
    /// <summary>
    /// ID objednávky.
    /// </summary>
    public Guid OrderId { get; set; }

    /// <summary>
    /// Kód produktu.
    /// </summary>
    public string ProductCode { get; set; } = string.Empty;

    /// <summary>
    /// Název produktu.
    /// </summary>
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// Popis produktu.
    /// </summary>
    public string? ProductDescription { get; set; }

    /// <summary>
    /// Kategorie produktu.
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// Jednotková cena bez DPH.
    /// </summary>
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// Množství.
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// Jednotka měření.
    /// </summary>
    public string Unit { get; set; } = "ks";

    /// <summary>
    /// Hmotnost jedné jednotky v kg.
    /// </summary>
    public decimal Weight { get; set; }

    /// <summary>
    /// Sazba DPH v procentech.
    /// </summary>
    public decimal TaxRate { get; set; } = 21;

    /// <summary>
    /// Sleva na položku v procentech.
    /// </summary>
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// Celková cena bez DPH.
    /// </summary>
    public decimal LineTotal { get; set; }

    /// <summary>
    /// Výše DPH pro tuto položku.
    /// </summary>
    public decimal LineTaxAmount { get; set; }

    /// <summary>
    /// Celková cena včetně DPH.
    /// </summary>
    public decimal LineTotalWithTax { get; set; }

    /// <summary>
    /// Poznámky k položce.
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Určuje, zda je produkt na skladě.
    /// </summary>
    public bool IsInStock { get; set; } = true;

    /// <summary>
    /// Očekávané datum dodání této položky.
    /// </summary>
    public DateTime? ExpectedDeliveryDate { get; set; }
}
