namespace Application.Features.Invoices;

/// <summary>
/// DTO pro čtení položky faktury.
/// </summary>
public class InvoiceItemDto
{
    /// <summary>
    /// Jedinečný identifikátor polo<PERSON>.
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// ID faktury.
    /// </summary>
    public Guid InvoiceId { get; set; }

    /// <summary>
    /// Kód produktu/služby.
    /// </summary>
    public string ProductCode { get; set; } = string.Empty;

    /// <summary>
    /// Název produktu/služby.
    /// </summary>
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// Popis produktu/služby.
    /// </summary>
    public string? ProductDescription { get; set; }

    /// <summary>
    /// Jednotková cena bez DPH.
    /// </summary>
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// Množství.
    /// </summary>
    public decimal Quantity { get; set; }

    /// <summary>
    /// Jednotka měření.
    /// </summary>
    public string Unit { get; set; } = "ks";

    /// <summary>
    /// Sazba DPH v procentech.
    /// </summary>
    public decimal TaxRate { get; set; } = 21;

    /// <summary>
    /// Sleva na položku v procentech.
    /// </summary>
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// Celková cena bez DPH.
    /// </summary>
    public decimal LineTotal { get; set; }

    /// <summary>
    /// Výše DPH pro tuto položku.
    /// </summary>
    public decimal LineTaxAmount { get; set; }

    /// <summary>
    /// Celková cena včetně DPH.
    /// </summary>
    public decimal LineTotalWithTax { get; set; }

    /// <summary>
    /// Poznámky k položce.
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Datum vytvoření záznamu.
    /// </summary>
    public DateTime? CreatedAt { get; set; }

    /// <summary>
    /// Kdo vytvořil záznam.
    /// </summary>
    public string? CreatedBy { get; set; }

    /// <summary>
    /// Datum poslední úpravy záznamu.
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Kdo naposledy upravil záznam.
    /// </summary>
    public string? ModifiedBy { get; set; }

    /// <summary>
    /// Verze řádku pro optimistické zamykání.
    /// </summary>
    public byte[]? RowVersion { get; set; }

    /// <summary>
    /// Částka slevy.
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// Určuje, zda je položka drahá (nad 5 000 Kč).
    /// </summary>
    public bool IsExpensive { get; set; }

    /// <summary>
    /// Určuje, zda má položka slevu.
    /// </summary>
    public bool HasDiscount { get; set; }
}

/// <summary>
/// DTO pro editaci položky faktury.
/// </summary>
public class InvoiceItemAddEdit
{
    /// <summary>
    /// ID faktury.
    /// </summary>
    public Guid InvoiceId { get; set; }

    /// <summary>
    /// Kód produktu/služby.
    /// </summary>
    public string ProductCode { get; set; } = string.Empty;

    /// <summary>
    /// Název produktu/služby.
    /// </summary>
    public string ProductName { get; set; } = string.Empty;

    /// <summary>
    /// Popis produktu/služby.
    /// </summary>
    public string? ProductDescription { get; set; }

    /// <summary>
    /// Jednotková cena bez DPH.
    /// </summary>
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// Množství.
    /// </summary>
    public decimal Quantity { get; set; }

    /// <summary>
    /// Jednotka měření.
    /// </summary>
    public string Unit { get; set; } = "ks";

    /// <summary>
    /// Sazba DPH v procentech.
    /// </summary>
    public decimal TaxRate { get; set; } = 21;

    /// <summary>
    /// Sleva na položku v procentech.
    /// </summary>
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// Celková cena bez DPH.
    /// </summary>
    public decimal LineTotal { get; set; }

    /// <summary>
    /// Výše DPH pro tuto položku.
    /// </summary>
    public decimal LineTaxAmount { get; set; }

    /// <summary>
    /// Celková cena včetně DPH.
    /// </summary>
    public decimal LineTotalWithTax { get; set; }

    /// <summary>
    /// Poznámky k položce.
    /// </summary>
    public string? Notes { get; set; }
}
