using Domain.Entities;

namespace Application.Features.Invoices;

/// <summary>
/// DTO pro čtení faktury.
/// </summary>
public class InvoiceDto
{
    /// <summary>
    /// Jedinečný identifikátor faktury.
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Číslo faktury.
    /// </summary>
    public string InvoiceNumber { get; set; } = string.Empty;

    /// <summary>
    /// Datum vystavení faktury.
    /// </summary>
    public DateTime IssueDate { get; set; }

    /// <summary>
    /// Datum splatnosti.
    /// </summary>
    public DateTime DueDate { get; set; }

    /// <summary>
    /// ID objednávky (mů<PERSON>e být null pro faktury bez objedn<PERSON>vky).
    /// </summary>
    public Guid? OrderId { get; set; }

    /// <summary>
    /// ID zákazníka.
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// J<PERSON><PERSON> zákazníka.
    /// </summary>
    public string CustomerName { get; set; } = string.Empty;

    /// <summary>
    /// Email zákazníka.
    /// </summary>
    public string CustomerEmail { get; set; } = string.Empty;

    /// <summary>
    /// Adresa zákazníka.
    /// </summary>
    public string CustomerAddress { get; set; } = string.Empty;

    /// <summary>
    /// IČO zákazníka.
    /// </summary>
    public string? CustomerTaxId { get; set; }

    /// <summary>
    /// DIČ zákazníka.
    /// </summary>
    public string? CustomerVatId { get; set; }

    /// <summary>
    /// Typ faktury.
    /// </summary>
    public InvoiceType Type { get; set; }

    /// <summary>
    /// Stav faktury.
    /// </summary>
    public InvoiceStatus Status { get; set; }

    /// <summary>
    /// Celková částka bez DPH.
    /// </summary>
    public decimal SubTotal { get; set; }

    /// <summary>
    /// Výše DPH.
    /// </summary>
    public decimal TaxAmount { get; set; }

    /// <summary>
    /// Celková částka včetně DPH.
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// Zaplacená částka.
    /// </summary>
    public decimal PaidAmount { get; set; }

    /// <summary>
    /// Zbývající částka k doplacení.
    /// </summary>
    public decimal RemainingAmount { get; set; }

    /// <summary>
    /// Měna faktury.
    /// </summary>
    public string Currency { get; set; } = "CZK";

    /// <summary>
    /// Poznámky k faktuře.
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Datum zaplacení.
    /// </summary>
    public DateTime? PaymentDate { get; set; }

    /// <summary>
    /// Způsob platby.
    /// </summary>
    public PaymentMethod PaymentMethod { get; set; }

    /// <summary>
    /// Variabilní symbol.
    /// </summary>
    public string? VariableSymbol { get; set; }

    /// <summary>
    /// Konstantní symbol.
    /// </summary>
    public string? ConstantSymbol { get; set; }

    /// <summary>
    /// Specifický symbol.
    /// </summary>
    public string? SpecificSymbol { get; set; }

    /// <summary>
    /// Datum vytvoření záznamu.
    /// </summary>
    public DateTime? CreatedAt { get; set; }

    /// <summary>
    /// Kdo vytvořil záznam.
    /// </summary>
    public string? CreatedBy { get; set; }

    /// <summary>
    /// Datum poslední úpravy záznamu.
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Kdo naposledy upravil záznam.
    /// </summary>
    public string? ModifiedBy { get; set; }

    /// <summary>
    /// Verze řádku pro optimistické zamykání.
    /// </summary>
    public byte[]? RowVersion { get; set; }

    /// <summary>
    /// Určuje, zda je faktura zaplacená.
    /// </summary>
    public bool IsPaid { get; set; }

    /// <summary>
    /// Určuje, zda je faktura po splatnosti.
    /// </summary>
    public bool IsOverdue { get; set; }

    /// <summary>
    /// Počet dní po splatnosti.
    /// </summary>
    public int DaysOverdue { get; set; }

    /// <summary>
    /// Počet dní do splatnosti.
    /// </summary>
    public int DaysUntilDue { get; set; }

    /// <summary>
    /// Určuje, zda je faktura částečně zaplacená.
    /// </summary>
    public bool IsPartiallyPaid { get; set; }

    /// <summary>
    /// Procento zaplacené částky.
    /// </summary>
    public decimal PaymentPercentage { get; set; }

    // Poznámka: Seznam položek faktury je dostupný přes samostatný endpoint /invoice-items
    // Pro základní CRUD operace s fakturami není vnořená kolekce nutná
}

/// <summary>
/// DTO pro editaci faktury.
/// </summary>
public class InvoiceAddEdit
{
    /// <summary>
    /// Číslo faktury.
    /// </summary>
    public string InvoiceNumber { get; set; } = string.Empty;

    /// <summary>
    /// Datum vystavení faktury.
    /// </summary>
    public DateTime IssueDate { get; set; }

    /// <summary>
    /// Datum splatnosti.
    /// </summary>
    public DateTime DueDate { get; set; }

    /// <summary>
    /// ID objednávky (může být null pro faktury bez objednávky).
    /// </summary>
    public Guid? OrderId { get; set; }

    /// <summary>
    /// ID zákazníka.
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// Jméno zákazníka.
    /// </summary>
    public string CustomerName { get; set; } = string.Empty;

    /// <summary>
    /// Email zákazníka.
    /// </summary>
    public string CustomerEmail { get; set; } = string.Empty;

    /// <summary>
    /// Adresa zákazníka.
    /// </summary>
    public string CustomerAddress { get; set; } = string.Empty;

    /// <summary>
    /// IČO zákazníka.
    /// </summary>
    public string? CustomerTaxId { get; set; }

    /// <summary>
    /// DIČ zákazníka.
    /// </summary>
    public string? CustomerVatId { get; set; }

    /// <summary>
    /// Typ faktury.
    /// </summary>
    public InvoiceType Type { get; set; }

    /// <summary>
    /// Stav faktury.
    /// </summary>
    public InvoiceStatus Status { get; set; }

    /// <summary>
    /// Celková částka bez DPH.
    /// </summary>
    public decimal SubTotal { get; set; }

    /// <summary>
    /// Výše DPH.
    /// </summary>
    public decimal TaxAmount { get; set; }

    /// <summary>
    /// Celková částka včetně DPH.
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// Zaplacená částka.
    /// </summary>
    public decimal PaidAmount { get; set; }

    /// <summary>
    /// Zbývající částka k doplacení.
    /// </summary>
    public decimal RemainingAmount { get; set; }

    /// <summary>
    /// Měna faktury.
    /// </summary>
    public string Currency { get; set; } = "CZK";

    /// <summary>
    /// Poznámky k faktuře.
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Datum zaplacení.
    /// </summary>
    public DateTime? PaymentDate { get; set; }

    /// <summary>
    /// Způsob platby.
    /// </summary>
    public PaymentMethod PaymentMethod { get; set; }

    /// <summary>
    /// Variabilní symbol.
    /// </summary>
    public string? VariableSymbol { get; set; }

    /// <summary>
    /// Konstantní symbol.
    /// </summary>
    public string? ConstantSymbol { get; set; }

    /// <summary>
    /// Specifický symbol.
    /// </summary>
    public string? SpecificSymbol { get; set; }
}
