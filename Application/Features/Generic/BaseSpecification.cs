using System.Linq.Expressions;

namespace Application.Features.Generic;

/// <summary>
/// Základní implementace specifikace pro filtrování, řazení a eager loading entit
/// </summary>
/// <typeparam name="T">Typ entity</typeparam>
public abstract class BaseSpecification<T> : ISpecification<T>
{
    /// <summary>
    /// Filtrační kritérium (WHERE klauzule)
    /// </summary>
    public Expression<Func<T, bool>>? Criteria { get; private set; }

    /// <summary>
    /// Seznam pro Eager Loading (JOIN a INCLUDE)
    /// </summary>
    public List<Expression<Func<T, object>>> Includes { get; } = new();

    /// <summary>
    /// Řazení vzestupně
    /// </summary>
    public Expression<Func<T, object>>? OrderBy { get; private set; }

    /// <summary>
    /// Řazení sestupně
    /// </summary>
    public Expression<Func<T, object>>? OrderByDescending { get; private set; }

    /// <summary>
    /// Pro stránkování - počet záznamů k přeskočení
    /// </summary>
    public int Skip { get; private set; }

    /// <summary>
    /// Pro stránkování - počet záznamů k načtení
    /// </summary>
    public int Take { get; private set; }

    /// <summary>
    /// Určuje, zda je povoleno stránkování
    /// </summary>
    public bool IsPagingEnabled { get; private set; }

    /// <summary>
    /// Konstruktor pro specifikaci bez filtrování
    /// </summary>
    protected BaseSpecification()
    {
    }

    /// <summary>
    /// Konstruktor pro specifikaci s filtrováním
    /// </summary>
    /// <param name="criteria">Filtrační kritérium</param>
    protected BaseSpecification(Expression<Func<T, bool>> criteria)
    {
        Criteria = criteria;
    }

    /// <summary>
    /// Nastaví filtrační kritérium
    /// </summary>
    /// <param name="criteria">Filtrační kritérium</param>
    protected void SetCriteria(Expression<Func<T, bool>> criteria)
    {
        Criteria = criteria;
    }

    /// <summary>
    /// Přidá include pro eager loading
    /// </summary>
    /// <param name="includeExpression">Výraz pro include</param>
    protected void AddInclude(Expression<Func<T, object>> includeExpression)
    {
        Includes.Add(includeExpression);
    }

    /// <summary>
    /// Nastaví řazení vzestupně
    /// </summary>
    /// <param name="orderByExpression">Výraz pro řazení</param>
    protected void AddOrderBy(Expression<Func<T, object>> orderByExpression)
    {
        OrderBy = orderByExpression;
    }

    /// <summary>
    /// Nastaví řazení sestupně
    /// </summary>
    /// <param name="orderByDescExpression">Výraz pro řazení sestupně</param>
    protected void AddOrderByDescending(Expression<Func<T, object>> orderByDescExpression)
    {
        OrderByDescending = orderByDescExpression;
    }

    /// <summary>
    /// Nastaví stránkování
    /// </summary>
    /// <param name="skip">Počet záznamů k přeskočení</param>
    /// <param name="take">Počet záznamů k načtení</param>
    protected void ApplyPaging(int skip, int take)
    {
        Skip = skip;
        Take = take;
        IsPagingEnabled = true;
    }

    /// <summary>
    /// Nastaví stránkování podle čísla stránky a velikosti stránky
    /// </summary>
    /// <param name="pageNumber">Číslo stránky (začíná od 1)</param>
    /// <param name="pageSize">Velikost stránky</param>
    protected void ApplyPagedPaging(int pageNumber, int pageSize)
    {
        var skip = (pageNumber - 1) * pageSize;
        ApplyPaging(skip, pageSize);
    }
}
