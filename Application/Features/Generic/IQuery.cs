
using SharedKernel.Abstractions.Mediator;
using System.Linq.Expressions;

namespace Application.Features.Generic;

public interface IQuery<TResult> : IRequest<TResult>;

public interface ICachableQuery<TResult>
{
    /// <summary>
    /// Jedinečn<PERSON> klíč pro cache (např. "GetUserById:1234").
    /// </summary>
    string CacheKey { get; }

    /// <summary>
    /// Tagy pro invalidaci cache
    /// </summary>
    IEnumerable<string>? Tags { get; }
}

public interface IInvalidateCache
{
    /// <summary>
    /// Jeden nebo více klíčů, které se mají po provedení příkazu vymazat.
    /// </summary>
    IEnumerable<string> CacheKeys { get; }

    /// <summary>
    /// Jeden nebo více tagů, podle kterých se mají po provedení příkazu vymazat cache záznamy.
    /// </summary>
    IEnumerable<string>? CacheTags { get; }
}

/// <summary>
/// Specifikace pro filtrování, řazení a eager loading entit
/// </summary>
/// <typeparam name="T">Typ entity</typeparam>
public interface ISpecification<T>
{
    /// <summary>
    /// Filtrační kritérium (WHERE klauzule)
    /// </summary>
    Expression<Func<T, bool>>? Criteria { get; }

    /// <summary>
    /// Seznam pro Eager Loading (JOIN a INCLUDE)
    /// </summary>
    List<Expression<Func<T, object>>> Includes { get; }

    /// <summary>
    /// Řazení vzestupně
    /// </summary>
    Expression<Func<T, object>>? OrderBy { get; }

    /// <summary>
    /// Řazení sestupně
    /// </summary>
    Expression<Func<T, object>>? OrderByDescending { get; }

    /// <summary>
    /// Pro stránkování - počet záznamů k přeskočení
    /// </summary>
    int Skip { get; }

    /// <summary>
    /// Pro stránkování - počet záznamů k načtení
    /// </summary>
    int Take { get; }

    /// <summary>
    /// Určuje, zda je povoleno stránkování
    /// </summary>
    bool IsPagingEnabled { get; }
}