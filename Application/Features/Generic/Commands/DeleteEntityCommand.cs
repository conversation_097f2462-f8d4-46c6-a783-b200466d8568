using Application.Abstraction;
using SharedKernel.Abstractions.Mediator;
using SharedKernel.Domain;
using SharedKernel.Models;

namespace Application.Features.Generic.Commands;

/// <summary>
/// Generický příkaz pro smazání entity podle ID.
/// </summary>
/// <typeparam name="TEntity">Typ entity, kter<PERSON> má být smazána</typeparam>
/// <typeparam name="TKey">Typ primárního klíče entity</typeparam>
public class DeleteEntityCommand<TEntity, TKey> : IRequest<Result<bool>>, IInvalidateCache
{
    /// <summary>
    /// ID entity, která má být smazána.
    /// </summary>
    public TKey Id { get; init; } = default!;

    /// <summary>
    /// Cache klíče pro invalidaci cache
    /// </summary>
    public IEnumerable<string> CacheKeys { get; } = Array.Empty<string>();

    /// <summary>
    /// Cache tagy pro invalidaci cache - vymaže všechno pro tuhle entitu
    /// </summary>
    public IEnumerable<string>? CacheTags => new[] { typeof(TEntity).Name };
}

/// <summary>
/// Handler pro zpracování příkazu DeleteEntityCommand.
/// </summary>
/// <typeparam name="TEntity">Typ entity, která má být smazána</typeparam>
/// <typeparam name="TKey">Typ primárního klíče entity</typeparam>
public class DeleteEntityCommandHandler<TEntity, TKey>
    : IRequestHandler<DeleteEntityCommand<TEntity, TKey>, Result<bool>>
    where TEntity : BaseEntity<TKey>
{
    private readonly IApplicationDbContext _ctx;

    /// <summary>
    /// Inicializuje novou instanci handleru.
    /// </summary>
    /// <param name="ctx">Databázový kontext</param>
    public DeleteEntityCommandHandler(IApplicationDbContext ctx)
    {
        _ctx = ctx;
    }

    /// <summary>
    /// Zpracuje příkaz pro smazání entity.
    /// </summary>
    /// <param name="request">Příkaz obsahující ID entity k smazání</param>
    /// <param name="ct">Cancellation token</param>
    /// <returns>Result s informací o úspěchu operace</returns>
    public async Task<Result<bool>> Handle(
        DeleteEntityCommand<TEntity, TKey> request,
        CancellationToken ct)
    {
        try
        {
            // Najít existující entitu
            var entity = await _ctx.Set<TEntity>()
                .FindAsync(new object[] { request.Id! }, ct);

            if (entity is null)
                return Result<bool>.Error("NotFound", $"{typeof(TEntity).Name} s ID {request.Id} nebyl nalezen.");

            // Smazat entitu
            _ctx.Set<TEntity>().Remove(entity);

            // Uložit změny
            int deletedEntriesCount = await _ctx.SaveChangesAsync(ct);

            if (deletedEntriesCount > 0)
            {
                return await Result<bool>.OkAsync(true);
            }
            else
            {
                return await Result<bool>.OkAsync(false);
            }
        }
        catch(Exception ex)
        {
            return await Result<bool>.ErrorAsync($"Chyba při mazání entity: {ex.Message}");
        }
    }
}
