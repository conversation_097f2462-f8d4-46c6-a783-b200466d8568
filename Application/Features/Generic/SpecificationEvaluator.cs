using Microsoft.EntityFrameworkCore;

namespace Application.Features.Generic;

/// <summary>
/// Evaluátor specifikací pro aplikaci filtrování, řazení a eager loading na IQueryable
/// </summary>
/// <typeparam name="TEntity">Typ entity</typeparam>
public static class SpecificationEvaluator<TEntity> where TEntity : class
{
    /// <summary>
    /// Aplikuje specifikaci na IQueryable dotaz
    /// </summary>
    /// <param name="inputQuery">Vstupní dotaz</param>
    /// <param name="specification">Specifikace k aplikaci</param>
    /// <returns>Upravený dotaz se specifikací</returns>
    public static IQueryable<TEntity> GetQuery(IQueryable<TEntity> inputQuery, ISpecification<TEntity> specification)
    {
        var query = inputQuery;

        // Aplikace filtrování (WHERE klauzule)
        if (specification.Criteria is not null)
        {
            query = query.Where(specification.Criteria);
        }

        // Aplikace eager loading (INCLUDE)
        query = specification.Includes.Aggregate(query,
            (current, include) => current.Include(include));

        // Aplikace řazení
        if (specification.OrderBy is not null)
        {
            query = query.OrderBy(specification.OrderBy);
        }
        else if (specification.OrderByDescending is not null)
        {
            query = query.OrderByDescending(specification.OrderByDescending);
        }

        // Aplikace stránkování
        if (specification.IsPagingEnabled)
        {
            query = query.Skip(specification.Skip).Take(specification.Take);
        }

        return query;
    }

    /// <summary>
    /// Aplikuje specifikaci na IQueryable dotaz bez stránkování (pro počítání celkového počtu záznamů)
    /// </summary>
    /// <param name="inputQuery">Vstupní dotaz</param>
    /// <param name="specification">Specifikace k aplikaci</param>
    /// <returns>Upravený dotaz se specifikací bez stránkování</returns>
    public static IQueryable<TEntity> GetQueryForCount(IQueryable<TEntity> inputQuery, ISpecification<TEntity> specification)
    {
        var query = inputQuery;

        // Aplikace filtrování (WHERE klauzule)
        if (specification.Criteria is not null)
        {
            query = query.Where(specification.Criteria);
        }

        // Pro počítání nepotřebujeme Include, OrderBy ani stránkování
        return query;
    }
}
