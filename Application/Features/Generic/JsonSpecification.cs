using System.Linq.Expressions;
using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Application.Features.Generic;

/// <summary>
/// JSON-serializovatelná implementace specifikace pro filtrování, řazení a eager loading entit
/// Umožňuje deserializaci z JSON payloadu bez problémů s Expression objekty
///
/// POZNÁMKA PRO PRODUKCI:
/// - Obsahuje SQLite-specifick<PERSON> (decimal řazení), které nebudou potřeba v MS SQL Server
/// - Include operace jsou dočasně vypnuté kvůli složitosti EF Core expressions (nezávisle na databázi)
/// </summary>
/// <typeparam name="T">Typ entity</typeparam>
public class JsonSpecification<T> : ISpecification<T> where T : class
{
    // Privátní pole pro Expression objekty
    private Expression<Func<T, bool>>? _criteria;
    private List<Expression<Func<T, object>>> _includes = new();
    private Expression<Func<T, object>>? _orderBy;
    private Expression<Func<T, object>>? _orderByDescending;

    /// <summary>
    /// Filtrační kritérium (WHERE klauzule) - interně sestavené z JSON vlastností
    /// </summary>
    [JsonIgnore]
    public Expression<Func<T, bool>>? Criteria
    {
        get
        {
            if (!_expressionsBuilt) BuildExpressions();
            return _criteria;
        }
        private set => _criteria = value;
    }

    /// <summary>
    /// Seznam pro Eager Loading (JOIN a INCLUDE) - interně sestavený z JSON vlastností
    /// </summary>
    [JsonIgnore]
    public List<Expression<Func<T, object>>> Includes
    {
        get
        {
            if (!_expressionsBuilt) BuildExpressions();
            return _includes;
        }
        private set => _includes = value;
    }

    /// <summary>
    /// Řazení vzestupně - interně sestavené z JSON vlastností
    /// </summary>
    [JsonIgnore]
    public Expression<Func<T, object>>? OrderBy
    {
        get
        {
            if (!_expressionsBuilt) BuildExpressions();
            return _orderBy;
        }
        private set => _orderBy = value;
    }

    /// <summary>
    /// Řazení sestupně - interně sestavené z JSON vlastností
    /// </summary>
    [JsonIgnore]
    public Expression<Func<T, object>>? OrderByDescending
    {
        get
        {
            if (!_expressionsBuilt) BuildExpressions();
            return _orderByDescending;
        }
        private set => _orderByDescending = value;
    }

    /// <summary>
    /// Pro stránkování - počet záznamů k přeskočení
    /// </summary>
    public int Skip { get; set; }

    /// <summary>
    /// Pro stránkování - počet záznamů k načtení
    /// </summary>
    public int Take { get; set; }

    /// <summary>
    /// Určuje, zda je povoleno stránkování
    /// </summary>
    public bool IsPagingEnabled { get; set; }

    // === JSON serializovatelné vlastnosti pro filtrování ===

    // === JSON serializovatelné vlastnosti pro filtrování ===
    private List<PropertyFilter>? _propertyFilters;
    private List<RangeFilter>? _rangeFilters;
    private List<TextSearchFilter>? _textSearchFilters;
    private List<DateRangeFilter>? _dateRangeFilters;
    private OrderByFilter? _orderByFilter;
    private List<string>? _includeProperties;

    /// <summary>
    /// Filtr podle názvu vlastnosti a hodnoty (rovnost)
    /// Formát: { "propertyName": "Name", "value": "test" }
    /// </summary>
    public List<PropertyFilter>? PropertyFilters
    {
        get => _propertyFilters;
        set
        {
            _propertyFilters = value;
            _expressionsBuilt = false;
        }
    }

    /// <summary>
    /// Filtr podle rozsahu hodnot
    /// Formát: { "propertyName": "Age", "minValue": 18, "maxValue": 65 }
    /// </summary>
    public List<RangeFilter>? RangeFilters
    {
        get => _rangeFilters;
        set
        {
            _rangeFilters = value;
            _expressionsBuilt = false;
        }
    }

    /// <summary>
    /// Filtr pro textové vyhledávání (Contains)
    /// Formát: { "propertyName": "Name", "searchText": "test" }
    /// </summary>
    public List<TextSearchFilter>? TextSearchFilters
    {
        get => _textSearchFilters;
        set
        {
            _textSearchFilters = value;
            _expressionsBuilt = false;
        }
    }

    /// <summary>
    /// Filtr podle data
    /// Formát: { "propertyName": "CreatedAt", "fromDate": "2024-01-01", "toDate": "2024-12-31" }
    /// </summary>
    public List<DateRangeFilter>? DateRangeFilters
    {
        get => _dateRangeFilters;
        set
        {
            _dateRangeFilters = value;
            _expressionsBuilt = false;
        }
    }

    /// <summary>
    /// Řazení podle vlastnosti
    /// Formát: { "propertyName": "Name", "descending": false }
    /// </summary>
    public OrderByFilter? OrderByFilter
    {
        get => _orderByFilter;
        set
        {
            _orderByFilter = value;
            _expressionsBuilt = false;
        }
    }

    /// <summary>
    /// Seznam vlastností pro eager loading
    /// Formát: ["RelatedEntity", "AnotherRelatedEntity"]
    /// </summary>
    public List<string>? IncludeProperties
    {
        get => _includeProperties;
        set
        {
            _includeProperties = value;
            _expressionsBuilt = false;
        }
    }

    // Pomocná proměnná pro sledování, zda byly Expression objekty sestavené
    [JsonIgnore]
    private bool _expressionsBuilt = false;

    /// <summary>
    /// Konstruktor pro deserializaci z JSON
    /// </summary>
    public JsonSpecification()
    {
    }

    /// <summary>
    /// Sestaví Expression objekty z JSON vlastností
    /// </summary>
    public void BuildExpressions()
    {
        // Pokud už byly Expression objekty sestavené, neděláme nic
        if (_expressionsBuilt) return;

        var parameter = Expression.Parameter(typeof(T), "x");
        var criteriaExpressions = new List<Expression>();

        // Sestavení filtrů podle vlastností (rovnost)
        if (PropertyFilters?.Any() == true)
        {
            foreach (var filter in PropertyFilters)
            {
                var expr = BuildPropertyFilter(parameter, filter);
                if (expr != null) criteriaExpressions.Add(expr);
            }
        }

        // Sestavení rozsahových filtrů
        if (RangeFilters?.Any() == true)
        {
            foreach (var filter in RangeFilters)
            {
                var expr = BuildRangeFilter(parameter, filter);
                if (expr != null) criteriaExpressions.Add(expr);
            }
        }

        // Sestavení textových filtrů
        if (TextSearchFilters?.Any() == true)
        {
            foreach (var filter in TextSearchFilters)
            {
                var expr = BuildTextSearchFilter(parameter, filter);
                if (expr != null) criteriaExpressions.Add(expr);
            }
        }

        // Sestavení datových filtrů
        if (DateRangeFilters?.Any() == true)
        {
            foreach (var filter in DateRangeFilters)
            {
                var expr = BuildDateRangeFilter(parameter, filter);
                if (expr != null) criteriaExpressions.Add(expr);
            }
        }

        // Kombinace všech kritérií pomocí AND
        if (criteriaExpressions.Any())
        {
            var combinedExpression = criteriaExpressions.Aggregate((left, right) => Expression.AndAlso(left, right));
            _criteria = Expression.Lambda<Func<T, bool>>(combinedExpression, parameter);
        }

        // Sestavení řazení
        if (OrderByFilter != null)
        {
            var orderExpression = BuildOrderByExpression(OrderByFilter);
            if (orderExpression != null)
            {
                if (OrderByFilter.Descending)
                    _orderByDescending = orderExpression;
                else
                    _orderBy = orderExpression;
            }
        }

        // TODO: IMPLEMENTACE - Dokončit Include operace pro eager loading
        // POZNÁMKA: Include operace vyžadují specifické Expression typy pro EF Core
        // Toto není omezení SQLite, ale obecný problém s EF Core Include expressions
        // Sestavení includes - pro EF Core Include operace potřebujeme speciální přístup
        if (IncludeProperties?.Any() == true)
        {
            // Pro nyní includes ignorujeme kvůli složitosti EF Core Include expressions
            // Možná řešení:
            // 1. String-based includes: context.Set<T>().Include("PropertyName")
            // 2. Specifické Expression typy pro navigační vlastnosti
            // 3. Použití reflection pro dynamické sestavení Include expressions
        }

        // Označíme, že Expression objekty byly sestavené
        _expressionsBuilt = true;
    }

    private Expression? BuildPropertyFilter(ParameterExpression parameter, PropertyFilter filter)
    {
        var property = typeof(T).GetProperty(filter.PropertyName, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
        if (property == null) return null;

        try
        {
            var propertyAccess = Expression.MakeMemberAccess(parameter, property);

            // Ošetření nullable typů
            var targetType = property.PropertyType;
            var underlyingType = Nullable.GetUnderlyingType(targetType) ?? targetType;

            object? convertedValue;
            if (filter.Value == null)
            {
                convertedValue = null;
            }
            else
            {
                convertedValue = ConvertJsonValue(filter.Value, underlyingType);
            }

            var constantValue = Expression.Constant(convertedValue, targetType);
            return Expression.Equal(propertyAccess, constantValue);
        }
        catch (Exception)
        {
            // Pokud konverze selže, ignorujeme tento filtr
            return null;
        }
    }

    private Expression? BuildRangeFilter(ParameterExpression parameter, RangeFilter filter)
    {
        var property = typeof(T).GetProperty(filter.PropertyName, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
        if (property == null) return null;

        try
        {
            var propertyAccess = Expression.MakeMemberAccess(parameter, property);
            var expressions = new List<Expression>();

            // Ošetření nullable typů
            var targetType = property.PropertyType;
            var underlyingType = Nullable.GetUnderlyingType(targetType) ?? targetType;

            if (filter.MinValue != null)
            {
                var convertedMinValue = ConvertJsonValue(filter.MinValue, underlyingType);
                if (convertedMinValue != null)
                {
                    var minConstant = Expression.Constant(convertedMinValue, targetType);
                    expressions.Add(Expression.GreaterThanOrEqual(propertyAccess, minConstant));
                }
            }

            if (filter.MaxValue != null)
            {
                var convertedMaxValue = ConvertJsonValue(filter.MaxValue, underlyingType);
                if (convertedMaxValue != null)
                {
                    var maxConstant = Expression.Constant(convertedMaxValue, targetType);
                    expressions.Add(Expression.LessThanOrEqual(propertyAccess, maxConstant));
                }
            }

            return expressions.Count switch
            {
                1 => expressions[0],
                2 => Expression.AndAlso(expressions[0], expressions[1]),
                _ => null
            };
        }
        catch (Exception)
        {
            // Pokud konverze selže, ignorujeme tento filtr
            return null;
        }
    }

    private Expression? BuildTextSearchFilter(ParameterExpression parameter, TextSearchFilter filter)
    {
        var property = typeof(T).GetProperty(filter.PropertyName, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
        if (property == null || property.PropertyType != typeof(string)) return null;

        var propertyAccess = Expression.MakeMemberAccess(parameter, property);
        var searchValue = Expression.Constant(filter.SearchText);
        var containsMethod = typeof(string).GetMethod("Contains", new[] { typeof(string) });

        return Expression.Call(propertyAccess, containsMethod!, searchValue);
    }

    private Expression? BuildDateRangeFilter(ParameterExpression parameter, DateRangeFilter filter)
    {
        var property = typeof(T).GetProperty(filter.PropertyName, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
        if (property == null || (property.PropertyType != typeof(DateTime) && property.PropertyType != typeof(DateTime?)))
            return null;

        try
        {
            var propertyAccess = Expression.MakeMemberAccess(parameter, property);
            var expressions = new List<Expression>();

            if (filter.FromDate.HasValue)
            {
                var fromConstant = Expression.Constant(filter.FromDate.Value, property.PropertyType);
                expressions.Add(Expression.GreaterThanOrEqual(propertyAccess, fromConstant));
            }

            if (filter.ToDate.HasValue)
            {
                var toConstant = Expression.Constant(filter.ToDate.Value, property.PropertyType);
                expressions.Add(Expression.LessThanOrEqual(propertyAccess, toConstant));
            }

            return expressions.Count switch
            {
                1 => expressions[0],
                2 => Expression.AndAlso(expressions[0], expressions[1]),
                _ => null
            };
        }
        catch (Exception)
        {
            // Pokud konverze selže, ignorujeme tento filtr
            return null;
        }
    }

    private Expression<Func<T, object>>? BuildOrderByExpression(OrderByFilter filter)
    {
        var property = typeof(T).GetProperty(filter.PropertyName, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
        if (property == null) return null;

        try
        {
            var parameter = Expression.Parameter(typeof(T), "x");
            var propertyAccess = Expression.MakeMemberAccess(parameter, property);

            // TODO: PRODUKCE - Odstranit po přechodu na MS SQL Server
            // POZNÁMKA: SQLite nepodporuje řazení podle decimal typů v ORDER BY klauzulích
            // MS SQL Server toto omezení nemá, takže v produkci nebude tato konverze potřeba
            // Ošetření decimal typů pro SQLite - konverze na double
            if (property.PropertyType == typeof(decimal) || property.PropertyType == typeof(decimal?))
            {
                // Pro SQLite konvertujeme decimal na double
                // V MS SQL Server lze řadit přímo podle decimal bez konverze
                var convertToDouble = Expression.Convert(propertyAccess, typeof(double));
                var convertToObject = Expression.Convert(convertToDouble, typeof(object));
                return Expression.Lambda<Func<T, object>>(convertToObject, parameter);
            }

            var converted = Expression.Convert(propertyAccess, typeof(object));
            return Expression.Lambda<Func<T, object>>(converted, parameter);
        }
        catch (Exception)
        {
            // Pokud sestavení expression selže, ignorujeme řazení
            return null;
        }
    }

    private Expression<Func<T, object>>? BuildIncludeExpression(string propertyName)
    {
        var property = typeof(T).GetProperty(propertyName, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
        if (property == null) return null;

        try
        {
            var parameter = Expression.Parameter(typeof(T), "x");
            var propertyAccess = Expression.MakeMemberAccess(parameter, property);

            // Pro Include operace nesmíme konvertovat na object - EF Core potřebuje přímý přístup k vlastnosti
            // Pokud je vlastnost už object typu, nekonvertujeme
            if (property.PropertyType == typeof(object))
            {
                return Expression.Lambda<Func<T, object>>(propertyAccess, parameter);
            }
            else
            {
                // Pro navigační vlastnosti vytvoříme expression bez konverze na object
                // EF Core bude schopen zpracovat pouze navigační vlastnosti
                var converted = Expression.Convert(propertyAccess, typeof(object));
                return Expression.Lambda<Func<T, object>>(converted, parameter);
            }
        }
        catch (Exception)
        {
            // Pokud sestavení expression selže, ignorujeme tento include
            return null;
        }
    }

    /// <summary>
    /// Pomocná metoda pro konverzi JSON hodnot na správný typ
    /// </summary>
    private static object? ConvertJsonValue(object? value, Type targetType)
    {
        if (value == null) return null;

        // Pokud je hodnota už správného typu, vrátíme ji
        if (targetType.IsAssignableFrom(value.GetType()))
            return value;

        // Ošetření JsonElement objektů z System.Text.Json
        if (value is System.Text.Json.JsonElement jsonElement)
        {
            return ConvertJsonElement(jsonElement, targetType);
        }

        // Ošetření enum typů
        if (targetType.IsEnum)
        {
            return Enum.Parse(targetType, value.ToString()!);
        }

        // Standardní konverze
        return Convert.ChangeType(value, targetType);
    }

    /// <summary>
    /// Konverze JsonElement na správný typ
    /// </summary>
    private static object? ConvertJsonElement(System.Text.Json.JsonElement element, Type targetType)
    {
        return targetType.Name switch
        {
            nameof(String) => element.GetString(),
            nameof(Int32) => element.GetInt32(),
            nameof(Int64) => element.GetInt64(),
            nameof(Double) => element.GetDouble(),
            nameof(Decimal) => element.GetDecimal(),
            nameof(Boolean) => element.GetBoolean(),
            nameof(DateTime) => element.GetDateTime(),
            nameof(Guid) => element.GetGuid(),
            _ when targetType.IsEnum => Enum.Parse(targetType, element.GetString()!),
            _ => element.GetString() // Fallback na string
        };
    }
}

/// <summary>
/// Filtr podle vlastnosti a hodnoty (rovnost)
/// </summary>
public class PropertyFilter
{
    public string PropertyName { get; set; } = string.Empty;
    public object? Value { get; set; }
}

/// <summary>
/// Filtr podle rozsahu hodnot
/// </summary>
public class RangeFilter
{
    public string PropertyName { get; set; } = string.Empty;
    public object? MinValue { get; set; }
    public object? MaxValue { get; set; }
}

/// <summary>
/// Filtr pro textové vyhledávání
/// </summary>
public class TextSearchFilter
{
    public string PropertyName { get; set; } = string.Empty;
    public string SearchText { get; set; } = string.Empty;
}

/// <summary>
/// Filtr podle rozsahu dat
/// </summary>
public class DateRangeFilter
{
    public string PropertyName { get; set; } = string.Empty;
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

/// <summary>
/// Filtr pro řazení
/// </summary>
public class OrderByFilter
{
    public string PropertyName { get; set; } = string.Empty;
    public bool Descending { get; set; } = false;
}
