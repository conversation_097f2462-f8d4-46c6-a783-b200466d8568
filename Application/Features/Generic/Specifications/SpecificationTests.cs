using Domain.Entities;
using System.Linq.Expressions;

namespace Application.Features.Generic.Specifications;

/// <summary>
/// Jednoduché testy pro ov<PERSON><PERSON><PERSON><PERSON> specifikací
/// Tyto testy lze spustit jako unit testy nebo použít pro manuální testování
/// </summary>
public class SpecificationTests
{
    /// <summary>
    /// Test základní specifikace s filtrováním
    /// </summary>
    public void TestActiveSpecification()
    {
        // Arrange
        var spec = new SampleSpecifications.ActiveSamplesSpecification();
        
        // Assert
        Assert.NotNull(spec.Criteria);
        Assert.NotNull(spec.OrderBy);
        Assert.Null(spec.OrderByDescending);
        Assert.False(spec.IsPagingEnabled);
        
        Console.WriteLine("✓ ActiveSamplesSpecification test passed");
    }

    /// <summary>
    /// Test specifikace s parametrem
    /// </summary>
    public void TestNameFilterSpecification()
    {
        // Arrange
        var nameFilter = "test";
        var spec = new SampleSpecifications.SamplesByNameSpecification(nameFilter);
        
        // Assert
        Assert.NotNull(spec.Criteria);
        Assert.NotNull(spec.OrderBy);
        Assert.False(spec.IsPagingEnabled);
        
        Console.WriteLine("✓ SamplesByNameSpecification test passed");
    }

    /// <summary>
    /// Test specifikace se stránkováním
    /// </summary>
    public void TestPagedSpecification()
    {
        // Arrange
        var pageNumber = 2;
        var pageSize = 5;
        var spec = new SampleSpecifications.PagedActiveSamplesSpecification(pageNumber, pageSize);
        
        // Assert
        Assert.NotNull(spec.Criteria);
        Assert.NotNull(spec.OrderBy);
        Assert.True(spec.IsPagingEnabled);
        Assert.Equal(5, spec.Skip); // (pageNumber - 1) * pageSize = (2-1) * 5 = 5
        Assert.Equal(5, spec.Take);
        
        Console.WriteLine("✓ PagedActiveSamplesSpecification test passed");
    }

    /// <summary>
    /// Test komplexní specifikace
    /// </summary>
    public void TestComplexSpecification()
    {
        // Arrange
        var nameFilter = "sample";
        var isActive = true;
        var createdAfter = DateTime.Now.AddDays(-30);
        
        var spec = new SampleSpecifications.ComplexSampleSpecification(
            nameFilter, isActive, createdAfter);
        
        // Assert
        Assert.NotNull(spec.Criteria);
        Assert.NotNull(spec.OrderBy);
        Assert.False(spec.IsPagingEnabled);
        
        Console.WriteLine("✓ ComplexSampleSpecification test passed");
    }

    /// <summary>
    /// Test obecné specifikace
    /// </summary>
    public void TestCommonSpecifications()
    {
        // Arrange
        var createdAfter = DateTime.Now.AddDays(-7);
        var spec = new CommonSpecifications.CreatedAfterSpecification<SampleEntity>(createdAfter);
        
        // Assert
        Assert.NotNull(spec.Criteria);
        Assert.NotNull(spec.OrderByDescending);
        Assert.False(spec.IsPagingEnabled);
        
        Console.WriteLine("✓ CreatedAfterSpecification test passed");
    }

    /// <summary>
    /// Test SpecificationEvaluator s mock daty
    /// </summary>
    public void TestSpecificationEvaluator()
    {
        // Arrange
        var sampleData = new List<SampleEntity>
        {
            new() { Id = 1, Name = "Active Sample 1", IsActive = true, CreatedAt = DateTime.Now.AddDays(-5) },
            new() { Id = 2, Name = "Inactive Sample", IsActive = false, CreatedAt = DateTime.Now.AddDays(-10) },
            new() { Id = 3, Name = "Active Sample 2", IsActive = true, CreatedAt = DateTime.Now.AddDays(-2) },
        }.AsQueryable();

        var spec = new SampleSpecifications.ActiveSamplesSpecification();
        
        // Act
        var result = SpecificationEvaluator<SampleEntity>.GetQuery(sampleData, spec);
        var items = result.ToList();
        
        // Assert
        Assert.Equal(2, items.Count);
        Assert.All(items, item => Assert.True(item.IsActive));
        
        Console.WriteLine("✓ SpecificationEvaluator test passed");
    }

    /// <summary>
    /// Spustí všechny testy
    /// </summary>
    public void RunAllTests()
    {
        Console.WriteLine("Spouštím testy specifikací...\n");
        
        try
        {
            TestActiveSpecification();
            TestNameFilterSpecification();
            TestPagedSpecification();
            TestComplexSpecification();
            TestCommonSpecifications();
            TestSpecificationEvaluator();
            
            Console.WriteLine("\n✅ Všechny testy prošly úspěšně!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ Test selhal: {ex.Message}");
            throw;
        }
    }
}

/// <summary>
/// Jednoduchá Assert třída pro testování
/// </summary>
public static class Assert
{
    public static void NotNull(object? obj)
    {
        if (obj == null)
            throw new Exception("Expected non-null value");
    }
    
    public static void Null(object? obj)
    {
        if (obj != null)
            throw new Exception("Expected null value");
    }
    
    public static void True(bool condition)
    {
        if (!condition)
            throw new Exception("Expected true");
    }
    
    public static void False(bool condition)
    {
        if (condition)
            throw new Exception("Expected false");
    }
    
    public static void Equal<T>(T expected, T actual)
    {
        if (!EqualityComparer<T>.Default.Equals(expected, actual))
            throw new Exception($"Expected {expected}, but got {actual}");
    }
    
    public static void All<T>(IEnumerable<T> collection, Action<T> action)
    {
        foreach (var item in collection)
        {
            action(item);
        }
    }
}
