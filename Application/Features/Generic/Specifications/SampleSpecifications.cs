using Domain.Entities;
using SharedKernel.Domain;

namespace Application.Features.Generic.Specifications;

/// <summary>
/// Ukázkové specifikace pro SampleEntity
/// </summary>
public static class SampleSpecifications
{
    /// <summary>
    /// Specifikace pro aktivní sample entity
    /// </summary>
    public class ActiveSamplesSpecification : BaseSpecification<SampleEntity>
    {
        public ActiveSamplesSpecification() : base(x => x.IsActive)
        {
            AddOrderBy(x => x.Name);
        }
    }

    /// <summary>
    /// Specifikace pro sample entity podle názvu (obsahuje text)
    /// </summary>
    public class SamplesByNameSpecification : BaseSpecification<SampleEntity>
    {
        public SamplesByNameSpecification(string nameFilter) 
            : base(x => x.Name.Contains(nameFilter))
        {
            AddOrderBy(x => x.Name);
        }
    }

    /// <summary>
    /// Specifikace pro sample entity vytvořené po určitém datu
    /// </summary>
    public class SamplesCreatedAfterSpecification : BaseSpecification<SampleEntity>
    {
        public SamplesCreatedAfterSpecification(DateTime createdAfter) 
            : base(x => x.CreatedAt > createdAfter)
        {
            AddOrderByDescending(x => x.CreatedAt);
        }
    }

    /// <summary>
    /// Specifikace pro stránkované aktivní sample entity
    /// </summary>
    public class PagedActiveSamplesSpecification : BaseSpecification<SampleEntity>
    {
        public PagedActiveSamplesSpecification(int pageNumber, int pageSize)
            : base(x => x.IsActive)
        {
            AddOrderBy(x => x.Name);
            ApplyPagedPaging(pageNumber, pageSize);
        }
    }

    /// <summary>
    /// Komplexní specifikace kombinující více kritérií
    /// </summary>
    public class ComplexSampleSpecification : BaseSpecification<SampleEntity>
    {
        public ComplexSampleSpecification(string? nameFilter = null, bool? isActive = null,
            DateTime? createdAfter = null, int? pageNumber = null, int? pageSize = null)
        {
            // Dynamické sestavení kritérií
            if (!string.IsNullOrEmpty(nameFilter) && isActive.HasValue && createdAfter.HasValue)
            {
                SetCriteria(x => x.Name.Contains(nameFilter) &&
                               x.IsActive == isActive.Value &&
                               x.CreatedAt > createdAfter.Value);
            }
            else if (!string.IsNullOrEmpty(nameFilter) && isActive.HasValue)
            {
                SetCriteria(x => x.Name.Contains(nameFilter) && x.IsActive == isActive.Value);
            }
            else if (!string.IsNullOrEmpty(nameFilter))
            {
                SetCriteria(x => x.Name.Contains(nameFilter));
            }
            else if (isActive.HasValue)
            {
                SetCriteria(x => x.IsActive == isActive.Value);
            }
            else if (createdAfter.HasValue)
            {
                SetCriteria(x => x.CreatedAt > createdAfter.Value);
            }

            // Řazení
            AddOrderBy(x => x.Name);

            // Stránkování pokud je zadáno
            if (pageNumber.HasValue && pageSize.HasValue)
            {
                ApplyPagedPaging(pageNumber.Value, pageSize.Value);
            }
        }
    }
}

/// <summary>
/// Obecné specifikace použitelné pro různé entity
/// </summary>
public static class CommonSpecifications
{
    /// <summary>
    /// Specifikace pro entity implementující ITrackableEntity vytvořené po určitém datu
    /// </summary>
    public class CreatedAfterSpecification<T> : BaseSpecification<T> where T : class, ITrackableEntity<int>
    {
        public CreatedAfterSpecification(DateTime createdAfter)
            : base(x => x.CreatedAt > createdAfter)
        {
            AddOrderByDescending(x => x.CreatedAt);
        }
    }

    /// <summary>
    /// Specifikace pro stránkování bez dalších kritérií
    /// </summary>
    public class PagedSpecification<T> : BaseSpecification<T> where T : class
    {
        public PagedSpecification(int pageNumber, int pageSize)
        {
            ApplyPagedPaging(pageNumber, pageSize);
        }
    }

    /// <summary>
    /// Specifikace pro řazení podle zadané vlastnosti
    /// </summary>
    public class OrderedSpecification<T> : BaseSpecification<T> where T : class
    {
        public OrderedSpecification(System.Linq.Expressions.Expression<Func<T, object>> orderBy, bool descending = false)
        {
            if (descending)
                AddOrderByDescending(orderBy);
            else
                AddOrderBy(orderBy);
        }
    }
}
