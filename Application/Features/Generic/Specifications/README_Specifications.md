# Specifikace pro filtrování a dotazování

Tento dokument popisuje implementaci Specification Pattern pro filtrování, <PERSON>azení a eager loading entit v projektu DataCapture.

## Přehled

Specification Pattern umožňuje enkapsulaci business logiky pro dotazování do znovupoužitelných objektů. Naše implementace podporuje:

- **Filtrování** - WHERE klauzule pomocí Expression<Func<T, bool>>
- **Eager Loading** - INCLUDE pro načítání souvisejících entit
- **Řazení** - OrderBy a OrderByDescending
- **Stránkování** - Skip a Take pro stránkované výsledky

## Základní komponenty

### ISpecification<T>

Základní rozhraní pro všechny specifikace:

```csharp
public interface ISpecification<T>
{
    Expression<Func<T, bool>>? Criteria { get; }
    List<Expression<Func<T, object>>> Includes { get; }
    Expression<Func<T, object>>? OrderBy { get; }
    Expression<Func<T, object>>? OrderByDescending { get; }
    int Skip { get; }
    int Take { get; }
    bool IsPagingEnabled { get; }
}
```

### BaseSpecification<T>

Abstraktní základní třída poskytující implementaci ISpecification<T>:

```csharp
public abstract class BaseSpecification<T> : ISpecification<T>
{
    protected BaseSpecification() { }
    protected BaseSpecification(Expression<Func<T, bool>> criteria) { }
    
    protected void AddInclude(Expression<Func<T, object>> includeExpression) { }
    protected void AddOrderBy(Expression<Func<T, object>> orderByExpression) { }
    protected void AddOrderByDescending(Expression<Func<T, object>> orderByDescExpression) { }
    protected void ApplyPaging(int skip, int take) { }
    protected void ApplyPaging(int pageNumber, int pageSize) { }
    protected void SetCriteria(Expression<Func<T, bool>> criteria) { }
}
```

### SpecificationEvaluator<T>

Statická třída pro aplikaci specifikací na IQueryable:

```csharp
public static class SpecificationEvaluator<TEntity>
{
    public static IQueryable<TEntity> GetQuery(IQueryable<TEntity> inputQuery, ISpecification<TEntity> specification)
    public static IQueryable<TEntity> GetQueryForCount(IQueryable<TEntity> inputQuery, ISpecification<TEntity> specification)
}
```

## Použití specifikací

### 1. Vytvoření jednoduché specifikace

```csharp
public class ActiveSamplesSpecification : BaseSpecification<SampleEntity>
{
    public ActiveSamplesSpecification() : base(x => x.IsActive)
    {
        AddOrderBy(x => x.Name);
    }
}
```

### 2. Specifikace s parametry

```csharp
public class SamplesByNameSpecification : BaseSpecification<SampleEntity>
{
    public SamplesByNameSpecification(string nameFilter) 
        : base(x => x.Name.Contains(nameFilter))
    {
        AddOrderBy(x => x.Name);
    }
}
```

### 3. Specifikace se stránkováním

```csharp
public class PagedActiveSamplesSpecification : BaseSpecification<SampleEntity>
{
    public PagedActiveSamplesSpecification(int pageNumber, int pageSize) 
        : base(x => x.IsActive)
    {
        AddOrderBy(x => x.Name);
        ApplyPaging(pageNumber, pageSize);
    }
}
```

### 4. Komplexní specifikace

```csharp
public class ComplexSampleSpecification : BaseSpecification<SampleEntity>
{
    public ComplexSampleSpecification(string? nameFilter = null, bool? isActive = null, 
        DateTime? createdAfter = null)
    {
        if (!string.IsNullOrEmpty(nameFilter) && isActive.HasValue)
        {
            SetCriteria(x => x.Name.Contains(nameFilter) && x.IsActive == isActive.Value);
        }
        // další kombinace...
        
        AddOrderBy(x => x.Name);
    }
}
```

## Použití v dotazech

### GetAllEntitiesQuery se specifikací

```csharp
var query = new GetAllEntitiesQuery<SampleDto, SampleEntity>
{
    Specification = new ActiveSamplesSpecification(),
    UseCache = true
};

var result = await mediator.Send(query);
```

### GetPagedEntitiesQuery se specifikací

```csharp
var query = new GetPagedEntitiesQuery<SampleDto, SampleEntity>
{
    Specification = new PagedActiveSamplesSpecification(1, 10),
    UseCache = true
};

var result = await mediator.Send(query);
```

## Použití v API

### ICrudApiService rozšíření

```csharp
public interface ICrudApiService<TEntity, TDto, TAddEditDto, TKey>
{
    Task<IResult> GetAllWithSpecificationAsync(ISpecification<TEntity>? specification, bool useCache = false);
    Task<IResult> GetPagedWithSpecificationAsync(ISpecification<TEntity>? specification, int pageNumber = 1, int pageSize = 10, bool useCache = false);
}
```

### Použití v kontrolerech/endpointech

```csharp
// Získání aktivních entit
var activeSpec = new ActiveSamplesSpecification();
var result = await crudService.GetAllWithSpecificationAsync(activeSpec);

// Vyhledávání podle názvu
var searchSpec = new SamplesByNameSpecification("test");
var searchResult = await crudService.GetAllWithSpecificationAsync(searchSpec);
```

## Obecné specifikace

Pro často používané operace jsou k dispozici obecné specifikace:

```csharp
// Pro entity implementující ITrackableEntity
var recentSpec = new CommonSpecifications.CreatedAfterSpecification<SampleEntity>(DateTime.Now.AddDays(-7));

// Pouze stránkování
var pagedSpec = new CommonSpecifications.PagedSpecification<SampleEntity>(1, 10);

// Řazení
var orderedSpec = new CommonSpecifications.OrderedSpecification<SampleEntity>(x => x.Name, descending: false);
```

## Výhody

1. **Znovupoužitelnost** - Specifikace lze použít v různých částech aplikace
2. **Testovatelnost** - Každá specifikace je samostatně testovatelná
3. **Čitelnost** - Business logika je jasně oddělená a pojmenovaná
4. **Kombinovatelnost** - Specifikace lze snadno kombinovat
5. **Type Safety** - Kompilační kontrola správnosti dotazů
6. **Performance** - Dotazy se vykonávají na databázové úrovni

## Zpětná kompatibilita

Stávající kód zůstává funkční díky zachování původních dotazů:
- `GetAllEntitiesQuery<TDto>` - označen jako Obsolete
- `GetPagedEntitiesQuery<TDto>` - označen jako Obsolete

Nové dotazy:
- `GetAllEntitiesQuery<TDto, TEntity>` - s podporou specifikací
- `GetPagedEntitiesQuery<TDto, TEntity>` - s podporou specifikací
