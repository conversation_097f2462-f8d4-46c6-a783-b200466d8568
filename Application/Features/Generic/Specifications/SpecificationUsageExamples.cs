using SharedKernel.Abstractions.Mediator;
using Application.Features.Generic.Queries;
using Application.Features.Sample;
using Domain.Entities;
using SharedKernel.Models;

namespace Application.Features.Generic.Specifications;

/// <summary>
/// Ukázkové použití specifikací v aplikaci
/// </summary>
public class SpecificationUsageExamples
{
    private readonly IMediator _mediator;

    public SpecificationUsageExamples(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Příklad použití specifikace pro získání aktivních sample entit
    /// </summary>
    public async Task<List<SampleDto>> GetActiveSamplesAsync()
    {
        var query = new GetAllEntitiesQuery<SampleDto, SampleEntity>
        {
            Specification = new SampleSpecifications.ActiveSamplesSpecification()
        };

        var result = await _mediator.Send(query);
        return result
            .OnError(errors => Console.WriteLine($"Failed to get active samples: {string.Join(", ", errors)}"))
            .GetValueOrDefault(new List<SampleDto>());
    }

    /// <summary>
    /// Příklad použití specifikace pro vyhledání podle názvu
    /// </summary>
    public async Task<List<SampleDto>> SearchSamplesByNameAsync(string nameFilter)
    {
        var query = new GetAllEntitiesQuery<SampleDto, SampleEntity>
        {
            Specification = new SampleSpecifications.SamplesByNameSpecification(nameFilter)
        };

        var result = await _mediator.Send(query);
        return result
            .OnError(errors => Console.WriteLine($"Failed to search samples: {string.Join(", ", errors)}"))
            .GetValueOrDefault(new List<SampleDto>());
    }

    /// <summary>
    /// Příklad použití specifikace pro stránkované výsledky
    /// </summary>
    public async Task<PagedResult<SampleDto>> GetPagedActiveSamplesAsync(int pageNumber, int pageSize)
    {
        var query = new GetPagedEntitiesQuery<SampleDto, SampleEntity>
        {
            PageNumber = pageNumber,
            PageSize = pageSize
        };

        var result = await _mediator.Send(query);

        // result je už PagedResult<SampleDto>, ne Result<PagedResult<SampleDto>>
        var (succeeded, _, errors) = result;
        if (!succeeded)
        {
            Console.WriteLine($"Failed to get paged samples: {string.Join(", ", errors)}");
        }

        return result;
    }

    /// <summary>
    /// Příklad použití komplexní specifikace s více kritérii
    /// </summary>
    public async Task<Result<List<SampleDto>>> GetSamplesWithComplexFilterAsync(
        string? nameFilter = null,
        bool? isActive = null,
        DateTime? createdAfter = null)
    {
        var query = new GetAllEntitiesQuery<SampleDto, SampleEntity>
        {
            Specification = new SampleSpecifications.ComplexSampleSpecification(
                nameFilter, isActive, createdAfter)
        };

        var result = await _mediator.Send(query);
        return result
            .OnError(errors => Console.WriteLine($"Failed to get complex filtered samples: {string.Join(", ", errors)}"));
    }

    /// <summary>
    /// Příklad použití obecné specifikace pro entity vytvořené po určitém datu
    /// </summary>
    public async Task<Result<List<SampleDto>>> GetRecentSamplesAsync(DateTime createdAfter)
    {
        var query = new GetAllEntitiesQuery<SampleDto, SampleEntity>
        {
            Specification = new CommonSpecifications.CreatedAfterSpecification<SampleEntity>(createdAfter)
        };

        var result = await _mediator.Send(query);
        return result
            .OnError(errors => Console.WriteLine($"Failed to get recent samples: {string.Join(", ", errors)}"));
    }

    /// <summary>
    /// Příklad kombinace standardního stránkování s filtrováním pomocí specifikace
    /// </summary>
    public async Task<PagedResult<SampleDto>> GetFilteredPagedSamplesAsync(
        string nameFilter, int pageNumber, int pageSize)
    {
        var query = new GetPagedEntitiesQuery<SampleDto, SampleEntity>
        {
            PageNumber = pageNumber,
            PageSize = pageSize
        };

        var result = await _mediator.Send(query);

        // result je už PagedResult<SampleDto>, ne Result<PagedResult<SampleDto>>
        var (succeeded, _, errors) = result;
        if (!succeeded)
        {
            Console.WriteLine($"Failed to get filtered paged samples: {string.Join(", ", errors)}");
        }

        return result;
    }

    /// <summary>
    /// Příklad použití specifikace s eager loading (pokud by bylo potřeba)
    /// </summary>
    public async Task<List<SampleDto>> GetSamplesWithRelatedDataAsync()
    {
        // Toto je ukázka - SampleEntity zatím nemá related entities
        // ale ukážeme, jak by se to dělalo pomocí vlastní specifikace
        var specification = new SampleSpecifications.ActiveSamplesSpecification();
        // V reálné implementaci by specifikace obsahovala:
        // specification.AddInclude(x => x.RelatedEntity);
        // specification.AddInclude(x => x.AnotherRelatedEntity);

        var query = new GetAllEntitiesQuery<SampleDto, SampleEntity>
        {
            Specification = specification
        };

        var result = await _mediator.Send(query);
        return result
            .OnError(errors => Console.WriteLine($"Failed to get samples with related data: {string.Join(", ", errors)}"))
            .GetValueOrDefault(new List<SampleDto>());
    }
}
