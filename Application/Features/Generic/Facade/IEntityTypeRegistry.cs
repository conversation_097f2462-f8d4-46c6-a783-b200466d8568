using System.Collections.Concurrent;

namespace Application.Features.Generic.Facade;

/// <summary>
/// Registry pro mapování typů entit na související DTO a klíčové typy
/// </summary>
public interface IEntityTypeRegistry
{
    /// <summary>
    /// Registruje typy pro entitu
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <typeparam name="TDto">Typ DTO pro čtení</typeparam>
    /// <typeparam name="TEditDto">Typ DTO pro editaci</typeparam>
    /// <typeparam name="TKey">Typ klíče</typeparam>
    void RegisterEntity<TEntity, TDto, TEditDto, TKey>()
        where TEntity : class
        where TDto : class
        where TEditDto : class;

    /// <summary>
    /// Získá informace o typech pro entitu
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <returns>Informace o typech nebo null pokud není registrována</returns>
    EntityTypeInfo? GetEntityTypeInfo<TEntity>() where TEntity : class;

    /// <summary>
    /// Získá informace o typech pro entitu podle typu
    /// </summary>
    /// <param name="entityType">Typ entity</param>
    /// <returns>Informace o typech nebo null pokud není registrována</returns>
    EntityTypeInfo? GetEntityTypeInfo(Type entityType);

    /// <summary>
    /// Zkontroluje, zda je entita registrována
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <returns>True pokud je registrována</returns>
    bool IsEntityRegistered<TEntity>() where TEntity : class;

    /// <summary>
    /// Získá všechny registrované entity
    /// </summary>
    /// <returns>Kolekce informací o všech registrovaných entitách</returns>
    IEnumerable<EntityTypeInfo> GetAllEntityTypes();
}

/// <summary>
/// Informace o typech pro entitu
/// </summary>
public class EntityTypeInfo
{
    public required Type EntityType { get; init; }
    public required Type DtoType { get; init; }
    public required Type EditDtoType { get; init; }
    public required Type KeyType { get; init; }
}

/// <summary>
/// Implementace registry pro mapování typů entit
/// </summary>
public class EntityTypeRegistry : IEntityTypeRegistry
{
    private readonly ConcurrentDictionary<Type, EntityTypeInfo> _entityTypes = new();

    public void RegisterEntity<TEntity, TDto, TEditDto, TKey>()
        where TEntity : class
        where TDto : class
        where TEditDto : class
    {
        var entityType = typeof(TEntity);
        var typeInfo = new EntityTypeInfo
        {
            EntityType = entityType,
            DtoType = typeof(TDto),
            EditDtoType = typeof(TEditDto),
            KeyType = typeof(TKey)
        };

        _entityTypes.AddOrUpdate(entityType, typeInfo, (_, _) => typeInfo);
    }

    public EntityTypeInfo? GetEntityTypeInfo<TEntity>() where TEntity : class
    {
        return GetEntityTypeInfo(typeof(TEntity));
    }

    public EntityTypeInfo? GetEntityTypeInfo(Type entityType)
    {
        return _entityTypes.TryGetValue(entityType, out var typeInfo) ? typeInfo : null;
    }

    public bool IsEntityRegistered<TEntity>() where TEntity : class
    {
        return _entityTypes.ContainsKey(typeof(TEntity));
    }

    public IEnumerable<EntityTypeInfo> GetAllEntityTypes()
    {
        return _entityTypes.Values;
    }
}
