using SharedKernel.Models;

namespace Application.Features.Generic.Facade;

/// <summary>
/// Hlavní fasáda pro jednoduché použití generických operací s entitami
/// Kombinuje příkazy a dotazy do jednoho rozhraní s minimálními generickými parametry
/// </summary>
public interface IEntityFacade
{
    // === PŘÍKAZY ===
    
    /// <summary>
    /// Vytvoří novou entitu
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <param name="editDto">DTO pro vytvoření entity</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>ID vytvořené entity</returns>
    Task<Result<object>> CreateAsync<TEntity>(object editDto, CancellationToken cancellationToken = default)
        where TEntity : class;

    /// <summary>
    /// Aktualizuje existující entitu
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <param name="id">ID entity</param>
    /// <param name="editDto">DTO pro aktualizaci entity</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True pokud byla aktualizace úspěšná</returns>
    Task<Result<bool>> UpdateAsync<TEntity>(object id, object editDto, CancellationToken cancellationToken = default)
        where TEntity : class;

    /// <summary>
    /// Smaže entitu podle ID
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <param name="id">ID entity</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True pokud bylo smazání úspěšné</returns>
    Task<Result<bool>> DeleteAsync<TEntity>(object id, CancellationToken cancellationToken = default)
        where TEntity : class;

    // === DOTAZY ===
    
    /// <summary>
    /// Získá entitu podle ID
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <param name="id">ID entity</param>
    /// <param name="useCache">Zda použít cache</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>DTO entity nebo null pokud neexistuje</returns>
    Task<Result<object?>> GetByIdAsync<TEntity>(object id, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class;

    /// <summary>
    /// Získá všechny entity
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <param name="specification">Volitelná specifikace pro filtrování</param>
    /// <param name="useCache">Zda použít cache</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Seznam DTO entit</returns>
    Task<Result<IEnumerable<object>>> GetAllAsync<TEntity>(ISpecification<TEntity>? specification = null, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class;

    /// <summary>
    /// Získá stránkovaný seznam entit
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <param name="pageNumber">Číslo stránky</param>
    /// <param name="pageSize">Velikost stránky</param>
    /// <param name="specification">Volitelná specifikace pro filtrování</param>
    /// <param name="useCache">Zda použít cache</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Stránkovaný seznam DTO entit</returns>
    Task<PagedResult<object>> GetPagedAsync<TEntity>(int pageNumber = 1, int pageSize = 10, ISpecification<TEntity>? specification = null, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class;

    // === SILNĚ TYPOVANÉ DOTAZY (pro pokročilé použití) ===
    
    /// <summary>
    /// Získá všechny entity se silně typovaným výsledkem
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <typeparam name="TDto">Typ DTO</typeparam>
    /// <param name="specification">Volitelná specifikace pro filtrování</param>
    /// <param name="useCache">Zda použít cache</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Seznam DTO entit</returns>
    Task<Result<List<TDto>>> GetAllAsync<TEntity, TDto>(ISpecification<TEntity>? specification = null, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class
        where TDto : class;

    /// <summary>
    /// Získá stránkovaný seznam entit se silně typovaným výsledkem
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <typeparam name="TDto">Typ DTO</typeparam>
    /// <param name="pageNumber">Číslo stránky</param>
    /// <param name="pageSize">Velikost stránky</param>
    /// <param name="specification">Volitelná specifikace pro filtrování</param>
    /// <param name="useCache">Zda použít cache</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Stránkovaný seznam DTO entit</returns>
    Task<PagedResult<TDto>> GetPagedAsync<TEntity, TDto>(int pageNumber = 1, int pageSize = 10, ISpecification<TEntity>? specification = null, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class
        where TDto : class;

    // === UTILITY METODY ===
    
    /// <summary>
    /// Zkontroluje, zda je entita registrována v registry
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <returns>True pokud je registrována</returns>
    bool IsEntityRegistered<TEntity>() where TEntity : class;

    /// <summary>
    /// Získá informace o typech pro entitu
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <returns>Informace o typech nebo null pokud není registrována</returns>
    EntityTypeInfo? GetEntityTypeInfo<TEntity>() where TEntity : class;
}

/// <summary>
/// Implementace hlavní fasády pro generické operace s entitami
/// </summary>
public class EntityFacade : IEntityFacade
{
    private readonly IEntityCommandFacade _commandFacade;
    private readonly IEntityQueryFacade _queryFacade;
    private readonly IEntityTypeRegistry _typeRegistry;

    public EntityFacade(
        IEntityCommandFacade commandFacade,
        IEntityQueryFacade queryFacade,
        IEntityTypeRegistry typeRegistry)
    {
        _commandFacade = commandFacade;
        _queryFacade = queryFacade;
        _typeRegistry = typeRegistry;
    }

    // === PŘÍKAZY ===
    
    public Task<Result<object>> CreateAsync<TEntity>(object editDto, CancellationToken cancellationToken = default)
        where TEntity : class
    {
        return _commandFacade.CreateAsync<TEntity>(editDto, cancellationToken);
    }

    public Task<Result<bool>> UpdateAsync<TEntity>(object id, object editDto, CancellationToken cancellationToken = default)
        where TEntity : class
    {
        return _commandFacade.UpdateAsync<TEntity>(id, editDto, cancellationToken);
    }

    public Task<Result<bool>> DeleteAsync<TEntity>(object id, CancellationToken cancellationToken = default)
        where TEntity : class
    {
        return _commandFacade.DeleteAsync<TEntity>(id, cancellationToken);
    }

    // === DOTAZY ===
    
    public Task<Result<object?>> GetByIdAsync<TEntity>(object id, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class
    {
        return _queryFacade.GetByIdAsync<TEntity>(id, useCache, cancellationToken);
    }

    public Task<Result<IEnumerable<object>>> GetAllAsync<TEntity>(ISpecification<TEntity>? specification = null, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class
    {
        return _queryFacade.GetAllAsync<TEntity>(specification, useCache, cancellationToken);
    }

    public Task<PagedResult<object>> GetPagedAsync<TEntity>(int pageNumber = 1, int pageSize = 10, ISpecification<TEntity>? specification = null, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class
    {
        return _queryFacade.GetPagedAsync<TEntity>(pageNumber, pageSize, specification, useCache, cancellationToken);
    }

    // === SILNĚ TYPOVANÉ DOTAZY ===
    
    public Task<Result<List<TDto>>> GetAllAsync<TEntity, TDto>(ISpecification<TEntity>? specification = null, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class
        where TDto : class
    {
        return _queryFacade.GetAllAsync<TEntity, TDto>(specification, useCache, cancellationToken);
    }

    public Task<PagedResult<TDto>> GetPagedAsync<TEntity, TDto>(int pageNumber = 1, int pageSize = 10, ISpecification<TEntity>? specification = null, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class
        where TDto : class
    {
        return _queryFacade.GetPagedAsync<TEntity, TDto>(pageNumber, pageSize, specification, useCache, cancellationToken);
    }

    // === UTILITY METODY ===
    
    public bool IsEntityRegistered<TEntity>() where TEntity : class
    {
        return _typeRegistry.IsEntityRegistered<TEntity>();
    }

    public EntityTypeInfo? GetEntityTypeInfo<TEntity>() where TEntity : class
    {
        return _typeRegistry.GetEntityTypeInfo<TEntity>();
    }
}
