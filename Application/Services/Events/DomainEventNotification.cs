using Domain;
using SharedKernel.Domain;
using SharedKernel.Abstractions.Mediator;

namespace Application.Services.Events;

public class DomainEventNotification<TDomainEvent> : INotification
    where TDomainEvent : DomainEvent  // <PERSON><PERSON><PERSON> událost z Domain Layer
{
    public TDomainEvent DomainEvent { get; }

    public DomainEventNotification(TDomainEvent domainEvent)
    {
        DomainEvent = domainEvent;
    }
}