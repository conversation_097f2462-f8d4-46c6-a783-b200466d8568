using Application.Abstraction;
using SharedKernel.Abstractions.Mediator;
using Application.Features.Generic.Commands;
using Application.Features.Generic.Facade;
using Application.Features.Generic.Queries;
using Application.Features.Sample;
using Application.Features.Orders;
using Application.Features.Invoices;
using Application.Models;
using Application.Pipeline;
using Application.Services;
using Domain;
using Domain.Entities;
using Microsoft.Extensions.DependencyInjection;
using SharedKernel.Models;
using System.Reflection;

namespace Application;

public static class DependencyInjection
{
    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(CacheBehavior<,>));
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(CacheInvalidationBehavior<,>));
        services.AddTransient(typeof(IPipelineBehavior<,>), typeof(PerformanceBehavior<,>));

        // Automatická registrace generických handlerů
        RegisterGenericHandlers(services);

        // Registrace fasády pro jednoduché použití
        RegisterEntityFacade(services);

        return services;
    }



    /// <summary>
    /// Automaticky registruje generické handlery pro všechny entity
    /// </summary>
    private static void RegisterGenericHandlers(IServiceCollection services)
    {
        // Získání všech entit, které dědí z BaseEntity
        var entityTypes = GetEntityTypes();

        foreach (var entityInfo in entityTypes)
        {
            RegisterHandlersForEntity(services, entityInfo);
        }
    }

    /// <summary>
    /// Získá informace o všech entitách v projektu
    /// </summary>
    private static List<EntityInfo> GetEntityTypes()
    {
        return GetEntityTypesInternal();
    }

    /// <summary>
    /// Veřejná metoda pro získání informací o entitách pro Infrastructure vrstvu
    /// </summary>
    /// <returns>Seznam informací o entitách</returns>
    public static List<object> GetEntityTypesForInfrastructure()
    {
        return GetEntityTypesInternal().Cast<object>().ToList();
    }

    /// <summary>
    /// Diagnostická metoda pro ověření automatického zjišťování entit
    /// </summary>
    /// <returns>Informace o zjištěných entitách</returns>
    public static (bool IsAutoDiscoveryWorking, List<string> DiscoveredEntities, List<string> MissingDtoTypes)
        DiagnoseEntityDiscovery()
    {
        var autoDiscovered = DiscoverEntitiesAutomatically();
        var isWorking = autoDiscovered.Any();

        var discoveredEntities = autoDiscovered.Select(e => e.EntityType.Name).ToList();

        // Najdeme entity, pro které chybí DTO typy
        var applicationAssembly = Assembly.GetExecutingAssembly();

        // Najdeme Domain assembly
        Assembly? domainAssembly = null;
        try
        {
            domainAssembly = typeof(Domain.Entities.SampleEntity).Assembly;
        }
        catch
        {
            domainAssembly = AppDomain.CurrentDomain.GetAssemblies()
                .FirstOrDefault(a => a.GetName().Name == "Domain");
        }

        if (domainAssembly == null)
            return (isWorking, discoveredEntities, new List<string>());

        var allEntityTypes = domainAssembly.GetTypes()
            .Where(t => t.IsClass && !t.IsAbstract &&
                       typeof(SharedKernel.Domain.IEntity).IsAssignableFrom(t))
            .ToList();

        var missingDtoTypes = new List<string>();
        foreach (var entityType in allEntityTypes)
        {
            var entityName = entityType.Name;

            // Zkusíme obě konvence pojmenování
            string dtoTypeName = $"{entityName}Dto";
            string addEditTypeName = $"{entityName}AddEdit";

            var dtoType = applicationAssembly.GetTypes()
                .FirstOrDefault(t => t.Name == dtoTypeName);
            var addEditType = applicationAssembly.GetTypes()
                .FirstOrDefault(t => t.Name == addEditTypeName);

            // Pokud nenajdeme, zkusíme druhou konvenci
            if (dtoType == null || addEditType == null)
            {
                var shortEntityName = entityName.EndsWith("Entity")
                    ? entityName.Substring(0, entityName.Length - 6)
                    : entityName;

                dtoTypeName = $"{shortEntityName}Dto";
                addEditTypeName = $"{shortEntityName}AddEdit";

                dtoType = applicationAssembly.GetTypes()
                    .FirstOrDefault(t => t.Name == dtoTypeName);
                addEditType = applicationAssembly.GetTypes()
                    .FirstOrDefault(t => t.Name == addEditTypeName);
            }

            if (dtoType == null || addEditType == null)
            {
                missingDtoTypes.Add($"{entityName} (chybí: {(dtoType == null ? dtoTypeName : "")} {(addEditType == null ? addEditTypeName : "")})");
            }
        }

        return (isWorking, discoveredEntities, missingDtoTypes);
    }

    /// <summary>
    /// Interní metoda pro získání informací o všech entitách v projektu
    /// </summary>
    private static List<EntityInfo> GetEntityTypesInternal()
    {
        // Automatické zjišťování entit
        return DiscoverEntitiesAutomatically();
    }

    /// <summary>
    /// Automaticky zjistí všechny entity pomocí reflection
    /// </summary>
    private static List<EntityInfo> DiscoverEntitiesAutomatically()
    {
        var entityInfos = new List<EntityInfo>();

        // Najdeme všechny typy entit, které dědí z IEntity
        // Musíme hledat v Domain assembly, ne v SharedKernel assembly
        var applicationAssembly = Assembly.GetExecutingAssembly();

        // Najdeme Domain assembly pomocí známé entity
        Assembly? domainAssembly = null;
        try
        {
            // Pokusíme se najít Domain assembly pomocí SampleEntity
            domainAssembly = typeof(Domain.Entities.SampleEntity).Assembly;
        }
        catch
        {
            // Pokud se nepodaří najít SampleEntity, zkusíme najít assembly podle názvu
            domainAssembly = AppDomain.CurrentDomain.GetAssemblies()
                .FirstOrDefault(a => a.GetName().Name == "Domain");
        }

        if (domainAssembly == null)
            return entityInfos;

        var entityTypes = domainAssembly.GetTypes()
            .Where(t => t.IsClass && !t.IsAbstract &&
                       typeof(SharedKernel.Domain.IEntity).IsAssignableFrom(t))
            .ToList();

        foreach (var entityType in entityTypes)
        {
            var entityInfo = CreateEntityInfoForType(entityType, applicationAssembly);
            if (entityInfo != null)
            {
                entityInfos.Add(entityInfo);
            }
        }

        return entityInfos;
    }

    /// <summary>
    /// Vytvoří EntityInfo pro daný typ entity
    /// </summary>
    private static EntityInfo? CreateEntityInfoForType(Type entityType, Assembly applicationAssembly)
    {
        try
        {
            // Zjistíme typ klíče z IEntity<T>
            var entityInterface = entityType.GetInterfaces()
                .FirstOrDefault(i => i.IsGenericType &&
                               i.GetGenericTypeDefinition() == typeof(SharedKernel.Domain.IEntity<>));

            if (entityInterface == null)
                return null;

            var keyType = entityInterface.GetGenericArguments()[0];

            // Pokusíme se najít odpovídající DTO typy podle konvence
            var entityName = entityType.Name;

            // Pokusíme se najít DTO typy podle různých konvencí
            string dtoTypeName;
            string addEditTypeName;

            // Konvence 1: {EntityName}Dto (např. SampleEntityDto)
            dtoTypeName = $"{entityName}Dto";
            addEditTypeName = $"{entityName}AddEdit";

            var dtoType = applicationAssembly.GetTypes()
                .FirstOrDefault(t => t.Name == dtoTypeName);
            var addEditType = applicationAssembly.GetTypes()
                .FirstOrDefault(t => t.Name == addEditTypeName);

            // Konvence 2: {EntityNameWithoutEntity}Dto (např. SampleDto pro SampleEntity)
            if (dtoType == null || addEditType == null)
            {
                var shortEntityName = entityName.EndsWith("Entity")
                    ? entityName.Substring(0, entityName.Length - 6)
                    : entityName;

                dtoTypeName = $"{shortEntityName}Dto";
                addEditTypeName = $"{shortEntityName}AddEdit";

                dtoType = applicationAssembly.GetTypes()
                    .FirstOrDefault(t => t.Name == dtoTypeName);
                addEditType = applicationAssembly.GetTypes()
                    .FirstOrDefault(t => t.Name == addEditTypeName);
            }

            // Pokud nenajdeme DTO typy, přeskočíme tuto entitu
            if (dtoType == null || addEditType == null)
                return null;

            return new EntityInfo
            {
                EntityType = entityType,
                KeyType = keyType,
                DtoType = dtoType,
                AddEditType = addEditType
            };
        }
        catch
        {
            return null;
        }
    }



    /// <summary>
    /// Registruje handlery pro konkrétní entitu
    /// </summary>
    private static void RegisterHandlersForEntity(IServiceCollection services, EntityInfo entityInfo)
    {
        // Registrace Query handlerů
        RegisterQueryHandlers(services, entityInfo);

        // Registrace Command handlerů
        RegisterCommandHandlers(services, entityInfo);
    }

    /// <summary>
    /// Registruje query handlery pro entitu
    /// </summary>
    private static void RegisterQueryHandlers(IServiceCollection services, EntityInfo entityInfo)
    {
        // GetAllEntitiesQuery handler (nová verze se specifikací)
        var getAllQueryWithSpecType = typeof(GetAllEntitiesQuery<,>).MakeGenericType(entityInfo.DtoType, entityInfo.EntityType);
        var getAllResponseType = typeof(Result<>).MakeGenericType(typeof(List<>).MakeGenericType(entityInfo.DtoType));
        var getAllHandlerType = typeof(GetAllEntitiesQueryHandler<,>).MakeGenericType(entityInfo.EntityType, entityInfo.DtoType);
        var getAllRequestHandlerType = typeof(IRequestHandler<,>).MakeGenericType(getAllQueryWithSpecType, getAllResponseType);

        services.AddScoped(getAllRequestHandlerType, getAllHandlerType);



        // GetEntityByIdQuery handler
        var getByIdQueryType = typeof(GetEntityByIdQuery<>).MakeGenericType(entityInfo.DtoType);
        var getByIdResponseType = typeof(Result<>).MakeGenericType(entityInfo.DtoType);
        var getByIdHandlerType = typeof(GetEntityByIdQueryHandler<,>).MakeGenericType(entityInfo.EntityType, entityInfo.DtoType);
        var getByIdRequestHandlerType = typeof(IRequestHandler<,>).MakeGenericType(getByIdQueryType, getByIdResponseType);

        services.AddScoped(getByIdRequestHandlerType, getByIdHandlerType);



        // GetPagedEntitiesQuery handler (nová verze se specifikací)
        var getPagedQueryWithSpecType = typeof(GetPagedEntitiesQuery<,>).MakeGenericType(entityInfo.DtoType, entityInfo.EntityType);
        var getPagedResponseType = typeof(PagedResult<>).MakeGenericType(entityInfo.DtoType);
        var getPagedHandlerType = typeof(GetPagedEntitiesQueryHandler<,>).MakeGenericType(entityInfo.EntityType, entityInfo.DtoType);
        var getPagedRequestHandlerType = typeof(IRequestHandler<,>).MakeGenericType(getPagedQueryWithSpecType, getPagedResponseType);

        services.AddScoped(getPagedRequestHandlerType, getPagedHandlerType);


    }

    /// <summary>
    /// Registruje command handlery pro entitu
    /// </summary>
    private static void RegisterCommandHandlers(IServiceCollection services, EntityInfo entityInfo)
    {
        // CreateEntityCommand handler
        var createCommandType = typeof(CreateEntityCommand<,,>).MakeGenericType(entityInfo.EntityType, entityInfo.AddEditType, entityInfo.KeyType);
        var createResponseType = typeof(Result<>).MakeGenericType(entityInfo.KeyType);
        var createHandlerType = typeof(CreateEntityCommandHandler<,,>).MakeGenericType(entityInfo.EntityType, entityInfo.AddEditType, entityInfo.KeyType);
        var createRequestHandlerType = typeof(IRequestHandler<,>).MakeGenericType(createCommandType, createResponseType);

        services.AddScoped(createRequestHandlerType, createHandlerType);

        // UpdateEntityCommand handler
        var updateCommandType = typeof(UpdateEntityCommand<,,>).MakeGenericType(entityInfo.EntityType, entityInfo.AddEditType, entityInfo.KeyType);
        var updateResponseType = typeof(Result<bool>);
        var updateHandlerType = typeof(UpdateEntityCommandHandler<,,>).MakeGenericType(entityInfo.EntityType, entityInfo.AddEditType, entityInfo.KeyType);
        var updateRequestHandlerType = typeof(IRequestHandler<,>).MakeGenericType(updateCommandType, updateResponseType);

        services.AddScoped(updateRequestHandlerType, updateHandlerType);

        // DeleteEntityCommand handler
        var deleteCommandType = typeof(DeleteEntityCommand<,>).MakeGenericType(entityInfo.EntityType, entityInfo.KeyType);
        var deleteResponseType = typeof(Result<bool>);
        var deleteHandlerType = typeof(DeleteEntityCommandHandler<,>).MakeGenericType(entityInfo.EntityType, entityInfo.KeyType);
        var deleteRequestHandlerType = typeof(IRequestHandler<,>).MakeGenericType(deleteCommandType, deleteResponseType);

        services.AddScoped(deleteRequestHandlerType, deleteHandlerType);
    }

    /// <summary>
    /// Registruje fasádu pro jednoduché použití generických operací
    /// </summary>
    private static void RegisterEntityFacade(IServiceCollection services)
    {
        // Registrace EntityTypeRegistry jako singleton
        services.AddSingleton<IEntityTypeRegistry>(provider =>
        {
            var registry = new EntityTypeRegistry();

            // Registrace všech známých entit
            var entityTypes = GetEntityTypes();
            foreach (var entityInfo in entityTypes)
            {
                // Použijeme reflection pro volání generické metody RegisterEntity
                var registerMethod = typeof(IEntityTypeRegistry)
                    .GetMethod(nameof(IEntityTypeRegistry.RegisterEntity))!
                    .MakeGenericMethod(entityInfo.EntityType, entityInfo.DtoType, entityInfo.AddEditType, entityInfo.KeyType);

                registerMethod.Invoke(registry, null);
            }

            return registry;
        });

        // Registrace fasád
        services.AddScoped<IEntityCommandFacade, EntityCommandFacade>();
        services.AddScoped<IEntityQueryFacade, EntityQueryFacade>();
        services.AddScoped<IEntityFacade, EntityFacade>();
    }

    /// <summary>
    /// Pomocná třída pro informace o entitě
    /// </summary>
    private class EntityInfo
    {
        public Type EntityType { get; set; } = null!;
        public Type KeyType { get; set; } = null!;
        public Type DtoType { get; set; } = null!;
        public Type AddEditType { get; set; } = null!;
    }
}