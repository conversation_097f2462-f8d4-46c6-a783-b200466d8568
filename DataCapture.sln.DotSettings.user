<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AILogger_00601_002Ecs_002Fl_003A_002E_002E_003F_002E_002E_003FLibrary_003FApplication_0020Support_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F5a8b463a40e54a1895569ca5e0e8e57010710_003F6d_003Fd3f5a811_003FILogger_00601_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AServiceCollection_002Ecs_002Fl_003A_002E_002E_003F_002E_002E_003FLibrary_003FApplication_0020Support_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FSourcesCache_003F595e4f2361fd31cb66c7e9303f46cf8f2fe77d96eea9d0e42c17cae783aa74_003FServiceCollection_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AValueTask_002Ecs_002Fl_003A_002E_002E_003F_002E_002E_003FLibrary_003FApplication_0020Support_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FSourcesCache_003F88249243c7f3fdbe7ee28384a24562de33bd287ea74c2a1ede87e44d08816_003FValueTask_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=01451421_002Dfa76_002D4ff3_002D8d60_002D99db4a8a850c/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" IsActive="True" Name="All tests from MediatorTests.cs" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;
  &lt;Or&gt;
    &lt;ProjectFile&gt;F1FBA8D7-348E-46D5-9B37-F1A1463A5629/d:Mediator/f:MediatorTests.cs&lt;/ProjectFile&gt;
    &lt;Project Location="/Users/<USER>/RiderProjects/DataCapture/Architecture.Tests" Presentation="&amp;lt;Architecture.Tests&amp;gt;" /&gt;
    &lt;ProjectFile&gt;F1FBA8D7-348E-46D5-9B37-F1A1463A5629/d:Mediator/f:MediatorIntegrationTests.cs&lt;/ProjectFile&gt;
    &lt;Project Location="/Users/<USER>/RiderProjects/DataCapture/Infrastructure.Tests" Presentation="&amp;lt;Infrastructure.Tests&amp;gt;" /&gt;
  &lt;/Or&gt;
&lt;/SessionState&gt;</s:String>
	
	
	
	</wpf:ResourceDictionary>