using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using API.ApiService;
using Application.Features.Generic.Facade;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.DependencyInjection;
using SharedKernel.Models;

namespace API.Endpoints;

/// <summary>
/// Rozšiřující metody pro mapování CRUD endpointů s minimálními generickými parametry
/// </summary>
public static class CrudEndpoints
{
    /// <summary>
    /// Mapuje kompletní sadu CRUD endpointů pro danou entitu s automatickým odvozením typů
    /// </summary>
    /// <typeparam name="TEntity">Typ entity - ostatní typy se odvodí automaticky</typeparam>
    /// <param name="app">Endpoint route builder</param>
    /// <param name="routePrefix">Prefix pro všechny endpointy (např. "/v1/entities")</param>
    /// <param name="tag">Tag pro OpenAPI dokumentaci</param>
    /// <returns>Endpoint route builder pro fluent API</returns>
    public static IEndpointRouteBuilder MapCrudEndpoints<TEntity>(
        this IEndpointRouteBuilder app,
        string routePrefix,
        string? tag = null)
        where TEntity : class
    {
        // Získáme informace o typech z registry
        var serviceProvider = app.ServiceProvider;
        var typeRegistry = serviceProvider.GetRequiredService<IEntityTypeRegistry>();
        var typeInfo = typeRegistry.GetEntityTypeInfo<TEntity>();

        if (typeInfo == null)
            throw new InvalidOperationException($"Entita {typeof(TEntity).Name} není registrována v EntityTypeRegistry");

        // Použijeme pomocnou metodu pro mapování s konkrétními typy
        return MapCrudEndpointsInternal(app, routePrefix, tag, typeInfo);
    }

    /// <summary>
    /// Interní metoda pro mapování CRUD endpointů s konkrétními typy
    /// </summary>
    private static IEndpointRouteBuilder MapCrudEndpointsInternal(
        IEndpointRouteBuilder app,
        string routePrefix,
        string? tag,
        EntityTypeInfo typeInfo)
    {
        // Použijeme reflection pro volání generické metody s konkrétními typy
        var mapMethod = typeof(CrudEndpoints)
            .GetMethod(nameof(MapCrudEndpointsWithTypes), BindingFlags.NonPublic | BindingFlags.Static)!
            .MakeGenericMethod(typeInfo.EntityType, typeInfo.DtoType, typeInfo.EditDtoType, typeInfo.KeyType);

        return (IEndpointRouteBuilder)mapMethod.Invoke(null, new object[] { app, routePrefix, tag })!;
    }

    /// <summary>
    /// Mapuje CRUD endpointy s konkrétními typy pro správnou OpenAPI dokumentaci
    /// </summary>
    private static IEndpointRouteBuilder MapCrudEndpointsWithTypes<TEntity, TDto, TEditDto, TKey>(
        IEndpointRouteBuilder app,
        string routePrefix,
        string? tag)
        where TEntity : class
        where TDto : class
        where TEditDto : class
    {
        var group = app.MapGroup(routePrefix);
        if (!string.IsNullOrEmpty(tag))
            group.WithTags(tag);

        var entityName = typeof(TEntity).Name;

        // GET /entities - získá všechny entity
        group.MapGet("/", async (
            [FromServices] ICrudApiService<TEntity> service,
            [FromQuery] bool useCache = true) =>
            await service.GetAllAsync(useCache))
            .WithName($"GetAll{entityName}")
            .WithSummary($"Získá všechny {entityName}")
            .WithDescription($"Vrací seznam všech {entityName} entit")
            .Produces<TDto[]>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status500InternalServerError);

        // GET /entities/{id} - získá entitu podle ID
        group.MapGet("/{id}", async (
            string id, // Používáme string pro maximální flexibilitu
            [FromServices] ICrudApiService<TEntity> service,
            [FromQuery] bool useCache = true) =>
        {
            // Pokusíme se převést ID na správný typ
            object convertedId = ConvertId(id);
            return await service.GetByIdAsync(convertedId, useCache);
        })
            .WithName($"Get{entityName}ById")
            .WithSummary($"Získá {entityName} podle ID")
            .WithDescription($"Vrací konkrétní {entityName} entitu podle jejího ID")
            .Produces<TDto>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status404NotFound)
            .Produces(StatusCodes.Status500InternalServerError);

        // POST /entities - vytvoří novou entitu
        group.MapPost("/", async (
            HttpContext context,
            [FromServices] ICrudApiService<TEntity> service) =>
        {
            // Deserializujeme na správný AddEdit typ
            var input = await context.Request.ReadFromJsonAsync<TEditDto>();
            if (input == null)
                return Results.BadRequest("Neplatná data");

            return await service.CreateAsync(input);
        })
            .WithName($"Create{entityName}")
            .WithSummary($"Vytvoří nový {entityName}")
            .WithDescription($"Vytvoří novou {entityName} entitu na základě poskytnutých dat")
            .Accepts<TEditDto>("application/json")
            .Produces<TDto>(StatusCodes.Status201Created)
            .Produces(StatusCodes.Status400BadRequest)
            .ProducesValidationProblem();

        // PUT /entities/{id} - aktualizuje entitu
        group.MapPut("/{id}", async (
            string id,
            HttpContext context,
            [FromServices] ICrudApiService<TEntity> service) =>
        {
            object convertedId = ConvertId(id);

            // Deserializujeme na správný AddEdit typ
            var input = await context.Request.ReadFromJsonAsync<TEditDto>();
            if (input == null)
                return Results.BadRequest("Neplatná data");

            return await service.UpdateAsync(convertedId, input);
        })
            .WithName($"Update{entityName}")
            .WithSummary($"Aktualizuje {entityName}")
            .WithDescription($"Aktualizuje existující {entityName} entitu")
            .Accepts<TEditDto>("application/json")
            .Produces(StatusCodes.Status204NoContent)
            .Produces(StatusCodes.Status400BadRequest)
            .ProducesValidationProblem();

        // DELETE /entities/{id} - smaže entitu
        group.MapDelete("/{id}", async (
            string id,
            [FromServices] ICrudApiService<TEntity> service) =>
        {
            object convertedId = ConvertId(id);
            return await service.DeleteAsync(convertedId);
        })
            .WithName($"Delete{entityName}")
            .WithSummary($"Smaže {entityName}")
            .WithDescription($"Smaže existující {entityName} entitu podle jejího ID")
            .Produces(StatusCodes.Status204NoContent)
            .Produces(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status404NotFound)
            .Produces(StatusCodes.Status500InternalServerError);

        return app;
    }

    /// <summary>
    /// Mapuje rozšířené CRUD endpointy se specifikacemi
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <param name="app">Endpoint route builder</param>
    /// <param name="routePrefix">Prefix pro všechny endpointy</param>
    /// <param name="tag">Tag pro OpenAPI dokumentaci</param>
    /// <returns>Endpoint route builder pro fluent API</returns>
    public static IEndpointRouteBuilder MapCrudEndpointsWithSpecifications<TEntity>(
        this IEndpointRouteBuilder app,
        string routePrefix,
        string? tag = null)
        where TEntity : class
    {
        // Nejdříve mapujeme základní endpointy
        app.MapCrudEndpoints<TEntity>(routePrefix, tag);

        // Získáme informace o typech z registry
        var serviceProvider = app.ServiceProvider;
        var typeRegistry = serviceProvider.GetRequiredService<IEntityTypeRegistry>();
        var typeInfo = typeRegistry.GetEntityTypeInfo<TEntity>();

        if (typeInfo == null)
            throw new InvalidOperationException($"Entita {typeof(TEntity).Name} není registrována v EntityTypeRegistry");

        // Použijeme pomocnou metodu pro mapování specifikačních endpointů s konkrétními typy
        return MapSpecificationEndpointsInternal(app, routePrefix, tag, typeInfo);
    }

    /// <summary>
    /// Interní metoda pro mapování specifikačních endpointů s konkrétními typy
    /// </summary>
    private static IEndpointRouteBuilder MapSpecificationEndpointsInternal(
        IEndpointRouteBuilder app,
        string routePrefix,
        string? tag,
        EntityTypeInfo typeInfo)
    {
        // Použijeme reflection pro volání generické metody s konkrétními typy
        var mapMethod = typeof(CrudEndpoints)
            .GetMethod(nameof(MapSpecificationEndpointsWithTypes), BindingFlags.NonPublic | BindingFlags.Static)!
            .MakeGenericMethod(typeInfo.EntityType, typeInfo.DtoType, typeInfo.EditDtoType, typeInfo.KeyType);

        return (IEndpointRouteBuilder)mapMethod.Invoke(null, new object[] { app, routePrefix, tag })!;
    }

    /// <summary>
    /// Mapuje specifikační endpointy s konkrétními typy pro správnou OpenAPI dokumentaci
    /// </summary>
    private static IEndpointRouteBuilder MapSpecificationEndpointsWithTypes<TEntity, TDto, TEditDto, TKey>(
        IEndpointRouteBuilder app,
        string routePrefix,
        string? tag)
        where TEntity : class
        where TDto : class
        where TEditDto : class
    {
        var group = app.MapGroup(routePrefix);

        if (!string.IsNullOrEmpty(tag))
            group.WithTags(tag);

        var entityName = typeof(TEntity).Name;

        // POST /entities/query - univerzální dotazovací endpoint (filtrování + stránkování)
        group.MapPost("/query", async (
            [FromBody] Application.Features.Generic.JsonSpecification<TEntity>? specification,
            [FromServices] ICrudApiService<TEntity> service,
            [FromQuery] int? pageNumber = null,
            [FromQuery] int? pageSize = null,
            [FromQuery] bool useCache = false) =>
        {
            // Pokud je specifikace null, vytvoříme prázdnou
            var spec = specification ?? new Application.Features.Generic.JsonSpecification<TEntity>();

            // Ujistíme se, že jsou Expression objekty sestavené
            spec.BuildExpressions();

            return await service.QueryAsync(spec, pageNumber, pageSize, useCache);
        })
            .WithName($"Query{entityName}")
            .WithSummary($"Univerzální dotaz na {entityName}")
            .WithDescription($"Vrací seznam nebo stránkovaný seznam {entityName} entit podle JsonSpecification a parametrů stránkování. " +
                           "Pokud nejsou zadány parametry stránkování, vrátí všechny výsledky. " +
                           "Pokud není zadána specifikace, vrátí všechny entity (případně stránkované). " +
                           "Podporuje PropertyFilters, RangeFilters, TextSearchFilters, DateRangeFilters, OrderByFilter a IncludeProperties.")
            .Accepts<Application.Features.Generic.JsonSpecification<TEntity>>("application/json")
            .Produces<TDto[]>(StatusCodes.Status200OK)
            .Produces<PagedResult<TDto>>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status500InternalServerError);
        
        return app;
    }

    /// <summary>
    /// Pomocná metoda pro konverzi ID z string na správný typ
    /// </summary>
    /// <param name="id">ID jako string</param>
    /// <returns>Konvertované ID</returns>
    private static object ConvertId(string id)
    {
        // Pokusíme se převést na int
        if (int.TryParse(id, out var intId))
            return intId;

        // Pokusíme se převést na Guid
        if (Guid.TryParse(id, out var guidId))
            return guidId;

        // Pokusíme se převést na long
        if (long.TryParse(id, out var longId))
            return longId;

        // Jinak vrátíme jako string
        return id;
    }
}

/// <summary>
/// Rozšíření pro registraci CRUD endpointů
/// </summary>
public static class CrudEndpointExtensions
{
    /// <summary>
    /// Přidá CRUD endpointy pro entitu
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <param name="app">WebApplication</param>
    /// <param name="routePrefix">Prefix pro endpointy</param>
    /// <param name="tag">Tag pro OpenAPI</param>
    /// <param name="includeSpecifications">Zda zahrnout endpointy pro specifikace</param>
    /// <returns>WebApplication pro fluent API</returns>
    public static WebApplication MapEntityEndpoints<TEntity>(
        this WebApplication app,
        string routePrefix,
        string? tag = null,
        bool includeSpecifications = true)
        where TEntity : class
    {
        if (includeSpecifications)
        {
            app.MapCrudEndpointsWithSpecifications<TEntity>(routePrefix, tag);
        }
        else
        {
            app.MapCrudEndpoints<TEntity>(routePrefix, tag);
        }

        return app;
    }

    /// <summary>
    /// Automaticky mapuje CRUD endpointy pro všechny registrované entity
    /// </summary>
    /// <param name="app">WebApplication</param>
    /// <param name="includeSpecifications">Zda zahrnout endpointy pro specifikace</param>
    /// <returns>WebApplication pro fluent API</returns>
    public static WebApplication MapAllEntityEndpoints(
        this WebApplication app,
        bool includeSpecifications = true)
    {
        var serviceProvider = app.Services;
        var typeRegistry = serviceProvider.GetRequiredService<IEntityTypeRegistry>();
        var entityTypes = typeRegistry.GetAllEntityTypes();

        foreach (var entityTypeInfo in entityTypes)
        {
            // Vytvoříme route prefix z názvu entity
            var entityName = entityTypeInfo.EntityType.Name;
            var routePrefix = $"/v1/{ConvertToKebabCase(entityName.Replace("Entity", ""))}s";
            var tag = entityName.Replace("Entity", "") + "s";

            // Použijeme reflexi pro volání generické metody
            var mapMethod = typeof(CrudEndpointExtensions)
                .GetMethod(nameof(MapEntityEndpoints))!
                .MakeGenericMethod(entityTypeInfo.EntityType);

            mapMethod.Invoke(null, new object[] { app, routePrefix, tag, includeSpecifications });
        }

        return app;
    }

    /// <summary>
    /// Převede PascalCase na kebab-case
    /// </summary>
    private static string ConvertToKebabCase(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        return string.Concat(
            input.Select((x, i) => i > 0 && char.IsUpper(x) ? "-" + x : x.ToString()))
            .ToLower();
    }
}
