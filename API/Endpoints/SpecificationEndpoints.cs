using System;
using System.Collections.Generic;
using API.ApiService;
using Application.Features.Generic.Specifications;
using Application.Features.Sample;
using Domain.Entities;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using SharedKernel.Models;

namespace API.Endpoints;

/// <summary>
/// Ukázkové endpointy demonstrující použití specifikací
/// </summary>
public static class SpecificationEndpoints
{
    /// <summary>
    /// Registruje ukázkové endpointy pro specifikace
    /// </summary>
    public static IEndpointRouteBuilder MapSpecificationEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/v1/samples/specifications")
            .WithTags("Sample Specifications")
            .WithOpenApi();

        // GET /v1/samples/specifications/active - získá aktivní sample entity
        group.MapGet("/active", async (
            [FromServices] ICrudApiService<SampleEntity> service,
            [FromQuery] bool useCache = true) =>
        {
            var specification = new SampleSpecifications.ActiveSamplesSpecification();
            return await service.GetAllWithSpecificationAsync(specification, useCache);
        })
        .WithName("GetActiveSamples")
        .WithSummary("Získá aktivní sample entity")
        .WithDescription("Vrací seznam všech aktivních sample entit seřazených podle názvu")
        .Produces<List<SampleDto>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest);

        // GET /v1/samples/specifications/search - vyhledá podle názvu
        group.MapGet("/search", async (
            [FromServices] ICrudApiService<SampleEntity> service,
            [FromQuery] string nameFilter,
            [FromQuery] bool useCache = false) =>
        {
            if (string.IsNullOrWhiteSpace(nameFilter))
                return Results.BadRequest("Parametr nameFilter je povinný");

            var specification = new SampleSpecifications.SamplesByNameSpecification(nameFilter);
            return await service.GetAllWithSpecificationAsync(specification, useCache);
        })
        .WithName("SearchSamplesByName")
        .WithSummary("Vyhledá sample entity podle názvu")
        .WithDescription("Vrací seznam sample entit, jejichž název obsahuje zadaný text")
        .Produces<List<SampleDto>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest);

        // GET /v1/samples/specifications/recent - získá nedávno vytvořené entity
        group.MapGet("/recent", async (
            [FromServices] ICrudApiService<SampleEntity> service,
            [FromQuery] int days = 7,
            [FromQuery] bool useCache = true) =>
        {
            var createdAfter = DateTime.Now.AddDays(-Math.Abs(days));
            var specification = new CommonSpecifications.CreatedAfterSpecification<SampleEntity>(createdAfter);
            return await service.GetAllWithSpecificationAsync(specification, useCache);
        })
        .WithName("GetRecentSamples")
        .WithSummary("Získá nedávno vytvořené sample entity")
        .WithDescription("Vrací seznam sample entit vytvořených v posledních N dnech")
        .Produces<List<SampleDto>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest);

        // GET /v1/samples/specifications/paged-active - stránkované aktivní entity
        group.MapGet("/paged-active", async (
            [FromServices] ICrudApiService<SampleEntity> service,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] bool useCache = true) =>
        {
            var specification = new SampleSpecifications.PagedActiveSamplesSpecification(pageNumber, pageSize);
            return await service.GetPagedWithSpecificationAsync(specification, pageNumber, pageSize, useCache);
        })
        .WithName("GetPagedActiveSamples")
        .WithSummary("Získá stránkované aktivní sample entity")
        .WithDescription("Vrací stránkovaný seznam aktivních sample entit")
        .Produces<PagedResult<SampleDto>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest);

        // GET /v1/samples/specifications/complex - komplexní filtrování
        group.MapGet("/complex", async (
            [FromServices] ICrudApiService<SampleEntity> service,
            [FromQuery] string? nameFilter = null,
            [FromQuery] bool? isActive = null,
            [FromQuery] DateTime? createdAfter = null,
            [FromQuery] bool useCache = false) =>
        {
            var specification = new SampleSpecifications.ComplexSampleSpecification(
                nameFilter, isActive, createdAfter);
            return await service.GetAllWithSpecificationAsync(specification, useCache);
        })
        .WithName("GetSamplesWithComplexFilter")
        .WithSummary("Získá sample entity s komplexním filtrováním")
        .WithDescription("Vrací seznam sample entit na základě kombinace různých filtrů")
        .Produces<List<SampleDto>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest);

        // GET /v1/samples/specifications/filtered-paged - kombinace filtrování a stránkování
        group.MapGet("/filtered-paged", async (
            [FromServices] ICrudApiService<SampleEntity> service,
            [FromQuery] string nameFilter,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] bool useCache = false) =>
        {
            if (string.IsNullOrWhiteSpace(nameFilter))
                return Results.BadRequest("Parametr nameFilter je povinný");

            var specification = new SampleSpecifications.SamplesByNameSpecification(nameFilter);
            return await service.GetPagedWithSpecificationAsync(specification, pageNumber, pageSize, useCache);
        })
        .WithName("GetFilteredPagedSamples")
        .WithSummary("Získá filtrované a stránkované sample entity")
        .WithDescription("Vrací stránkovaný seznam sample entit filtrovaných podle názvu")
        .Produces<PagedResult<SampleDto>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest);

        return app;
    }
}

/// <summary>
/// Rozšíření pro registraci ukázkových endpointů
/// </summary>
public static class SpecificationEndpointExtensions
{
    /// <summary>
    /// Přidá ukázkové endpointy pro specifikace do aplikace
    /// </summary>
    public static WebApplication MapSpecificationExamples(this WebApplication app)
    {
        //app.MapSpecificationEndpoints();
        return app;
    }
}
