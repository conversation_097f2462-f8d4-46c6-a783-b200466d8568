using System;
using System.Collections.Generic;
using Domain.Entities;
using Infrastructure.Persistence;
using Infrastructure.Persistence.Seeders;
using Infrastructure.RuleEngine;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;

namespace API.Endpoints;

/// <summary>
/// Minimal API endpointy pro naplnění databáze testovacími daty.
/// </summary>
public static class SeedEndpoints
{
    /// <summary>
    /// Registruje všechny seed endpointy.
    /// </summary>
    public static IEndpointRouteBuilder MapSeedEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/seed")
            .WithTags("Seed")
            .WithOpenApi();

        // GET /api/seed/stats - Získá statistiky databáze
        group.MapGet("/stats", async (
            [FromServices] ApplicationDbContext context) =>
        {
            try
            {
                var stats = new
                {
                    Orders = await context.Set<Order>().CountAsync(),
                    OrderItems = await context.Set<OrderItem>().CountAsync(),
                    Invoices = await context.Set<Invoice>().CountAsync(),
                    InvoiceItems = await context.Set<InvoiceItem>().CountAsync(),
                    BusinessRules = await context.BusinessRules.CountAsync()
                };

                return Results.Ok(stats);
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při načítání statistik: {ex.Message}");
            }
        })
        .WithName("GetDatabaseStats")
        .WithSummary("Získá statistiky databáze")
        .WithDescription("Vrací počet záznamů v jednotlivých tabulkách")
        .Produces<object>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status500InternalServerError);

        // POST /api/seed/orders-and-invoices - Naplní databázi objednávkami a fakturami
        group.MapPost("/orders-and-invoices", async (
            [FromServices] ApplicationDbContext context) =>
        {
            try
            {
                await OrderAndInvoiceSeeder.SeedAsync(context);
                return Results.Ok(new { Message = "Testovací objednávky a faktury byly úspěšně vytvořeny." });
            }
            catch (Exception ex)
            {
                return Results.BadRequest(new { Message = "Chyba při vytváření testovacích dat.", Error = ex.Message });
            }
        })
        .WithName("SeedOrdersAndInvoices")
        .WithSummary("Naplní databázi testovacími objednávkami a fakturami")
        .WithDescription("Vytvoří ukázkové objednávky s položkami a související faktury")
        .Produces<object>(StatusCodes.Status200OK)
        .Produces<object>(StatusCodes.Status400BadRequest);

        // POST /api/seed/business-rules - Vytvoří ukázková obchodní pravidla
        group.MapPost("/business-rules", async (
            [FromServices] ApplicationDbContext context) =>
        {
            try
            {
                // Zkontrolujeme, zda už existují pravidla
                if (await context.BusinessRules.AnyAsync())
                {
                    return Results.Ok(new { Message = "Obchodní pravidla již existují." });
                }

                var rules = CreateSampleBusinessRules();
                await context.BusinessRules.AddRangeAsync(rules);
                await context.SaveChangesAsync();

                return Results.Ok(new { Message = $"Bylo vytvořeno {rules.Count} ukázkových obchodních pravidel." });
            }
            catch (Exception ex)
            {
                return Results.BadRequest(new { Message = "Chyba při vytváření obchodních pravidel.", Error = ex.Message });
            }
        })
        .WithName("SeedBusinessRules")
        .WithSummary("Vytvoří ukázková obchodní pravidla")
        .WithDescription("Vytvoří sadu ukázkových obchodních pravidel pro demonstraci funkcionality")
        .Produces<object>(StatusCodes.Status200OK)
        .Produces<object>(StatusCodes.Status400BadRequest);

        // POST /api/seed/all - Naplní databázi všemi testovacími daty
        group.MapPost("/all", async (
            [FromServices] ApplicationDbContext context) =>
        {
            try
            {
                var results = new List<string>();

                // Objednávky a faktury
                try
                {
                    await OrderAndInvoiceSeeder.SeedAsync(context);
                    results.Add("✅ Objednávky a faktury vytvořeny");
                }
                catch (Exception ex)
                {
                    results.Add($"❌ Chyba při vytváření objednávek: {ex.Message}");
                }

                // Obchodní pravidla
                try
                {
                    if (!await context.BusinessRules.AnyAsync())
                    {
                        var rules = CreateSampleBusinessRules();
                        await context.BusinessRules.AddRangeAsync(rules);
                        await context.SaveChangesAsync();
                        results.Add($"✅ Vytvořeno {rules.Count} obchodních pravidel");
                    }
                    else
                    {
                        results.Add("ℹ️ Obchodní pravidla již existují");
                    }
                }
                catch (Exception ex)
                {
                    results.Add($"❌ Chyba při vytváření pravidel: {ex.Message}");
                }

                return Results.Ok(new { Message = "Seed operace dokončena", Results = results });
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při seed operaci: {ex.Message}");
            }
        })
        .WithName("SeedAll")
        .WithSummary("Naplní databázi všemi testovacími daty")
        .WithDescription("Vytvoří všechna testovací data najednou - objednávky, faktury i obchodní pravidla")
        .Produces<object>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status500InternalServerError);

        // DELETE /api/seed/clear - Vymaže všechna testovací data
        group.MapDelete("/clear", async (
            [FromServices] ApplicationDbContext context) =>
        {
            try
            {
                var results = new List<string>();

                // Smazání v správném pořadí kvůli foreign key constraints
                var invoiceItemsCount = await context.Set<InvoiceItem>().CountAsync();
                if (invoiceItemsCount > 0)
                {
                    context.Set<InvoiceItem>().RemoveRange(context.Set<InvoiceItem>());
                    results.Add($"Smazáno {invoiceItemsCount} položek faktur");
                }

                var invoicesCount = await context.Set<Invoice>().CountAsync();
                if (invoicesCount > 0)
                {
                    context.Set<Invoice>().RemoveRange(context.Set<Invoice>());
                    results.Add($"Smazáno {invoicesCount} faktur");
                }

                var orderItemsCount = await context.Set<OrderItem>().CountAsync();
                if (orderItemsCount > 0)
                {
                    context.Set<OrderItem>().RemoveRange(context.Set<OrderItem>());
                    results.Add($"Smazáno {orderItemsCount} položek objednávek");
                }

                var ordersCount = await context.Set<Order>().CountAsync();
                if (ordersCount > 0)
                {
                    context.Set<Order>().RemoveRange(context.Set<Order>());
                    results.Add($"Smazáno {ordersCount} objednávek");
                }

                var rulesCount = await context.BusinessRules.CountAsync();
                if (rulesCount > 0)
                {
                    context.BusinessRules.RemoveRange(context.BusinessRules);
                    results.Add($"Smazáno {rulesCount} obchodních pravidel");
                }

                await context.SaveChangesAsync();

                return Results.Ok(new { Message = "Všechna testovací data byla smazána", Results = results });
            }
            catch (Exception ex)
            {
                return Results.Problem($"Chyba při mazání dat: {ex.Message}");
            }
        })
        .WithName("ClearAllData")
        .WithSummary("Vymaže všechna testovací data")
        .WithDescription("Smaže všechny objednávky, faktury a obchodní pravidla z databáze")
        .Produces<object>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status500InternalServerError);

        // POST /api/seed/complex-rule - Vytvoří komplexní obchodní pravidlo
        group.MapPost("/complex-rule", async (
            [FromServices] ApplicationDbContext context) =>
        {
            try
            {
                var complexRule = CreateComplexCustomerLoyaltyRule();
                await context.BusinessRules.AddAsync(complexRule);
                await context.SaveChangesAsync();

                return Results.Ok(new {
                    Message = "Komplexní pravidlo pro věrnostní slevy bylo vytvořeno.",
                    RuleId = complexRule.Id,
                    RuleName = complexRule.Name
                });
            }
            catch (Exception ex)
            {
                return Results.BadRequest(new { Message = "Chyba při vytváření komplexního pravidla.", Error = ex.Message });
            }
        })
        .WithName("CreateComplexRule")
        .WithSummary("Vytvoří komplexní pravidlo pro věrnostní slevy")
        .WithDescription("Vytvoří pravidlo, které počítá slevu podle celkové hodnoty faktur zákazníka za poslední rok")
        .Produces<object>(StatusCodes.Status200OK)
        .Produces<object>(StatusCodes.Status400BadRequest);

        return app;
    }

    /// <summary>
    /// Vytvoří ukázková obchodní pravidla.
    /// </summary>
    private static List<BusinessRule> CreateSampleBusinessRules()
    {
        var rules = new List<BusinessRule>();

        // Pravidlo 1: Sleva 10% pro objednávky nad 20 000 Kč
        rules.Add(new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Sleva 10% nad 20 000 Kč",
            Description = "Automatická sleva 10% pro objednávky s celkovou částkou nad 20 000 Kč",
            TargetEntityName = "Order",
            TargetProperty = "DiscountAmount",
            SchemaVersion = "1.0",
            RootNode = new OperationNode
            {
                Operator = OperatorType.If,
                Operands = new List<RuleNode>
                {
                    // Podmínka: TotalAmount > 20000
                    new OperationNode
                    {
                        Operator = OperatorType.GreaterThan,
                        Operands = new List<RuleNode>
                        {
                            new SourceValueNode { SourcePath = "TotalAmount" },
                            new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "20000" }
                        }
                    },
                    // Pak: TotalAmount * 0.1
                    new OperationNode
                    {
                        Operator = OperatorType.Multiply,
                        Operands = new List<RuleNode>
                        {
                            new SourceValueNode { SourcePath = "TotalAmount" },
                            new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "0.1" }
                        }
                    },
                    // Jinak: 0
                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "0" }
                }
            },
            IsActive = true
        });

        // Pravidlo 2: Doprava zdarma pro objednávky nad 15 000 Kč
        rules.Add(new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Doprava zdarma nad 15 000 Kč",
            Description = "Doprava zdarma pro objednávky s celkovou částkou nad 15 000 Kč",
            TargetEntityName = "Order",
            TargetProperty = "ShippingCost",
            SchemaVersion = "1.0",
            RootNode = new OperationNode
            {
                Operator = OperatorType.If,
                Operands = new List<RuleNode>
                {
                    // Podmínka: TotalAmount > 15000
                    new OperationNode
                    {
                        Operator = OperatorType.GreaterThan,
                        Operands = new List<RuleNode>
                        {
                            new SourceValueNode { SourcePath = "TotalAmount" },
                            new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "15000" }
                        }
                    },
                    // Pak: 0 (doprava zdarma)
                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "0" },
                    // Jinak: 200 (standardní poštovné)
                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "200" }
                }
            },
            IsActive = true,
        });

        // Pravidlo 3: Upomínka pro faktury po splatnosti
        rules.Add(new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Upomínka po splatnosti",
            Description = "Určuje, zda má být odeslána upomínka pro fakturu po splatnosti",
            TargetEntityName = "Invoice",
            TargetProperty = "RequiresReminder",
            SchemaVersion = "1.0",
            RootNode = new OperationNode
            {
                Operator = OperatorType.And,
                Operands = new List<RuleNode>
                {
                    // Faktura je po splatnosti
                    new SourceValueNode { SourcePath = "IsOverdue" },
                    // A více než 7 dní po splatnosti
                    new OperationNode
                    {
                        Operator = OperatorType.GreaterThan,
                        Operands = new List<RuleNode>
                        {
                            new SourceValueNode { SourcePath = "DaysOverdue" },
                            new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Integer, Value = "7" }
                        }
                    }
                }
            },
            IsActive = true
        });

        return rules;
    }

    /// <summary>
    /// Vytvoří komplexní pravidlo pro věrnostní slevy podle celkové hodnoty faktur zákazníka.
    /// </summary>
    private static BusinessRule CreateComplexCustomerLoyaltyRule()
    {
        return new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Věrnostní sleva podle celkové hodnoty faktur",
            Description = "Sleva podle celkové hodnoty faktur zákazníka za poslední rok: nad 100 000 Kč = 20%, nad 50 000 Kč = 10%, jinak 0%",
            TargetEntityName = "Order",
            TargetProperty = "DiscountPercentage",
            SchemaVersion = "1.0",
            RootNode = new OperationNode
            {
                Operator = OperatorType.If,
                Operands = new List<RuleNode>
                {
                    // Podmínka 1: Celková hodnota faktur > 100 000
                    new OperationNode
                    {
                        Operator = OperatorType.GreaterThan,
                        Operands = new List<RuleNode>
                        {
                            // Agregace: Součet všech faktur zákazníka za poslední rok
                            new RelatedAggregationNode
                            {
                                SourceEntityName = "Order",
                                TargetEntityName = "Invoice",
                                RelationshipProperty = "CustomerId",
                                AggregationType = AggregationType.Sum,
                                AggregationField = "TotalAmount",
                                FilterCondition = new OperationNode
                                {
                                    Operator = OperatorType.GreaterThan,
                                    Operands = new List<RuleNode>
                                    {
                                        new SourceValueNode { SourcePath = "IssueDate" },
                                        new ConstantNode
                                        {
                                            DataType = Infrastructure.RuleEngine.ValueType.DateTime,
                                            Value = DateTime.Now.AddYears(-1).ToString("yyyy-MM-dd")
                                        }
                                    }
                                }
                            },
                            new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "100000" }
                        }
                    },
                    // Pak: 20% sleva
                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "20" },
                    // Jinak: Další podmínka
                    new OperationNode
                    {
                        Operator = OperatorType.If,
                        Operands = new List<RuleNode>
                        {
                            // Podmínka 2: Celková hodnota faktur > 50 000
                            new OperationNode
                            {
                                Operator = OperatorType.GreaterThan,
                                Operands = new List<RuleNode>
                                {
                                    // Stejná agregace jako výše
                                    new RelatedAggregationNode
                                    {
                                        SourceEntityName = "Order",
                                        TargetEntityName = "Invoice",
                                        RelationshipProperty = "CustomerId",
                                        AggregationType = AggregationType.Sum,
                                        AggregationField = "TotalAmount",
                                        FilterCondition = new OperationNode
                                        {
                                            Operator = OperatorType.GreaterThan,
                                            Operands = new List<RuleNode>
                                            {
                                                new SourceValueNode { SourcePath = "IssueDate" },
                                                new ConstantNode
                                                {
                                                    DataType = Infrastructure.RuleEngine.ValueType.DateTime,
                                                    Value = DateTime.Now.AddYears(-1).ToString("yyyy-MM-dd")
                                                }
                                            }
                                        }
                                    },
                                    new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "50000" }
                                }
                            },
                            // Pak: 10% sleva
                            new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "10" },
                            // Jinak: 0% sleva
                            new ConstantNode { DataType = Infrastructure.RuleEngine.ValueType.Decimal, Value = "0" }
                        }
                    }
                }
            },
            IsActive = true
        };
    }
}
