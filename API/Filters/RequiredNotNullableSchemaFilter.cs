using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace API.Filters;

/// <summary>
/// Schema filter pro označení všech non-nullable vlastností jako povinn<PERSON> v OpenAPI dokumentaci.
/// </summary>
public class RequiredNotNullableSchemaFilter : ISchemaFilter
{
    /// <summary>
    /// Aplikuje filtr na OpenAPI schéma.
    /// </summary>
    /// <param name="schema">OpenAPI schéma</param>
    /// <param name="context">Kontext generování schématu</param>
    public void Apply(OpenApiSchema schema, SchemaFilterContext context)
    {
        if (schema.Properties == null || context.Type == null)
            return;

        // Získáme všechny vlastnosti typu
        var properties = context.Type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
        
        foreach (var property in properties)
        {
            // Převedeme název vlastnosti na camelCase (jak ho používá JSON serializer)
            var propertyName = char.ToLowerInvariant(property.Name[0]) + property.Name[1..];
            
            if (!schema.Properties.ContainsKey(propertyName))
                continue;

            var propertySchema = schema.Properties[propertyName];
            
            // Zkontrolujeme, zda má vlastnost [Required] atribut
            var hasRequiredAttribute = property.GetCustomAttribute<RequiredAttribute>() != null;
            
            // Zkontrolujeme, zda je vlastnost nullable
            var isNullable = IsNullableType(property);
            
            // Pokud má [Required] atribut nebo není nullable, označíme jako povinnou
            if (hasRequiredAttribute || !isNullable)
            {
                // Přidáme vlastnost do seznamu povinných
                schema.Required ??= new HashSet<string>();
                schema.Required.Add(propertyName);
                
                // Nastavíme nullable na false pro non-nullable typy
                if (!isNullable)
                {
                    propertySchema.Nullable = false;
                }
            }
        }
    }

    /// <summary>
    /// Zkontroluje, zda je vlastnost nullable.
    /// </summary>
    /// <param name="property">Vlastnost k ověření</param>
    /// <returns>True pokud je vlastnost nullable, jinak false</returns>
    private static bool IsNullableType(PropertyInfo property)
    {
        var type = property.PropertyType;

        // Nullable value types (int?, DateTime?, atd.)
        if (Nullable.GetUnderlyingType(type) != null)
            return true;

        // Pro reference types použijeme NullabilityInfoContext
        if (!type.IsValueType)
        {
            try
            {
                var nullabilityContext = new NullabilityInfoContext();
                var nullabilityInfo = nullabilityContext.Create(property);
                return nullabilityInfo.WriteState == NullabilityState.Nullable;
            }
            catch
            {
                // Fallback: pokud se nepodaří určit nullability, předpokládáme nullable
                return true;
            }
        }

        return false;
    }
}
