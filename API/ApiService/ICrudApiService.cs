using Application.Features.Generic;
using Application.Features.Generic.Facade;
using System.Text.Json;

namespace API.ApiService;

/// <summary>
/// Rozhraní pro CRUD API službu s minimálními generickými parametry
/// Používá Entity Facade pro automatické odvození typů
/// </summary>
/// <typeparam name="TEntity">Typ entity - ostatní typy se odvodí automaticky</typeparam>
public interface ICrudApiService<TEntity>
    where TEntity : class
{
    /// <summary>
    /// Získá všechny entity
    /// </summary>
    /// <param name="useCache">Zda použít cache</param>
    /// <returns>HTTP odpověď se seznamem entit</returns>
    Task<IResult> GetAllAsync(bool useCache = true);

    /// <summary>
    /// Získá stránkovaný seznam entit
    /// </summary>
    /// <param name="pageNumber"><PERSON><PERSON><PERSON> str<PERSON></param>
    /// <param name="pageSize">V<PERSON><PERSON><PERSON> stránky</param>
    /// <param name="sortBy">Pole pro řazení</param>
    /// <param name="sortDescending">Zda řadit sestupně</param>
    /// <param name="useCache">Zda použít cache</param>
    /// <returns>HTTP odpověď se stránkovaným seznamem entit</returns>
    Task<IResult> GetPagedAsync(int pageNumber = 1, int pageSize = 10, string? sortBy = null, bool sortDescending = false, bool useCache = true);

    /// <summary>
    /// Získá entitu podle ID
    /// </summary>
    /// <param name="id">ID entity</param>
    /// <param name="useCache">Zda použít cache</param>
    /// <returns>HTTP odpověď s entitou nebo NotFound</returns>
    Task<IResult> GetByIdAsync(object id, bool useCache = true);

    /// <summary>
    /// Vytvoří novou entitu
    /// </summary>
    /// <param name="dto">DTO pro vytvoření entity</param>
    /// <returns>HTTP odpověď s vytvořenou entitou</returns>
    Task<IResult> CreateAsync(object dto);

    /// <summary>
    /// Aktualizuje existující entitu
    /// </summary>
    /// <param name="id">ID entity</param>
    /// <param name="dto">DTO pro aktualizaci entity</param>
    /// <returns>HTTP odpověď s výsledkem aktualizace</returns>
    Task<IResult> UpdateAsync(object id, object dto);

    /// <summary>
    /// Smaže entitu podle ID
    /// </summary>
    /// <param name="id">ID entity</param>
    /// <returns>HTTP odpověď s výsledkem smazání</returns>
    Task<IResult> DeleteAsync(object id);

    /// <summary>
    /// Získá všechny entity podle specifikace s volitelnou podporou cache
    /// </summary>
    /// <param name="specification">Specifikace pro filtrování a řazení</param>
    /// <param name="useCache">Zda použít cache</param>
    /// <returns>HTTP odpověď se seznamem entit</returns>
    Task<IResult> GetAllWithSpecificationAsync(ISpecification<TEntity>? specification, bool useCache = false);

    /// <summary>
    /// Získá stránkovaný seznam entit podle specifikace
    /// </summary>
    /// <param name="specification">Specifikace pro filtrování a řazení</param>
    /// <param name="pageNumber">Číslo stránky (pokud není specifikováno ve specifikaci)</param>
    /// <param name="pageSize">Velikost stránky (pokud není specifikováno ve specifikaci)</param>
    /// <param name="useCache">Zda použít cache</param>
    /// <returns>HTTP odpověď se stránkovaným seznamem entit</returns>
    Task<IResult> GetPagedWithSpecificationAsync(ISpecification<TEntity>? specification, int pageNumber = 1, int pageSize = 10, bool useCache = false);

    /// <summary>
    /// Univerzální dotazovací metoda - podporuje filtrování i stránkování v jednom endpointu
    /// </summary>
    /// <param name="specification">Specifikace pro filtrování a řazení (volitelná)</param>
    /// <param name="pageNumber">Číslo stránky (volitelné - pokud není specifikováno, vrátí všechny výsledky)</param>
    /// <param name="pageSize">Velikost stránky (volitelné)</param>
    /// <param name="useCache">Zda použít cache</param>
    /// <returns>HTTP odpověď se seznamem nebo stránkovaným seznamem entit podle parametrů</returns>
    Task<IResult> QueryAsync(ISpecification<TEntity>? specification = null, int? pageNumber = null, int? pageSize = null, bool useCache = false);
}

/// <summary>
/// Implementace CRUD API služby používající Entity Facade
/// </summary>
/// <typeparam name="TEntity">Typ entity</typeparam>
public class CrudApiService<TEntity> : ICrudApiService<TEntity>
    where TEntity : class
{
    private readonly IEntityFacade _entityFacade;
    private readonly IEntityTypeRegistry _typeRegistry;

    public CrudApiService(
        IEntityFacade entityFacade,
        IEntityTypeRegistry typeRegistry)
    {
        _entityFacade = entityFacade;
        _typeRegistry = typeRegistry;
    }

    public async Task<IResult> GetAllAsync(bool useCache = true)
    {
        // Použijeme obecnou verzi Entity Facade, která vrací object
        var result = await _entityFacade.GetAllAsync<TEntity>(useCache: useCache);

        return result
            .OnError(errors => Console.WriteLine($"GetAll failed: {string.Join(", ", errors)}"))
            .Map(data => Results.Ok(data))
            .GetValueOrDefault(Results.BadRequest(result.Errors));
    }

    public async Task<IResult> GetPagedAsync(int pageNumber = 1, int pageSize = 10, string? sortBy = null, bool sortDescending = false, bool useCache = true)
    {
        // Pro sortBy zatím použijeme fallback podobně jako v původním CrudApiService
        if (!string.IsNullOrEmpty(sortBy))
        {
            return Results.BadRequest(new
            {
                Error = "Řazení pomocí sortBy parametru není v zjednodušené verzi podporováno.",
                Solution = "Použijte specifikace pro definování řazení nebo volání bez sortBy parametru.",
                Example = "Vytvořte specifikaci s AddOrderBy() nebo AddOrderByDescending() metodami."
            });
        }

        var result = await _entityFacade.GetPagedAsync<TEntity>(pageNumber, pageSize, useCache: useCache);

        return result
            .OnError(errors => Console.WriteLine($"GetPaged failed: {string.Join(", ", errors)}"))
            .Map(data => Results.Ok(data))
            .GetValueOrDefault(Results.BadRequest(result.Errors));
    }

    public async Task<IResult> GetByIdAsync(object id, bool useCache = true)
    {
        // Základní validace ID
        if (id == null)
            return Results.BadRequest("ID nesmí být null");

        // Speciální validace pro int ID
        if (id is int intId && intId <= 0)
            return Results.BadRequest("ID musí být kladné číslo");

        var (succeeded, data, errors) = await _entityFacade.GetByIdAsync<TEntity>(id, useCache);

        if (!succeeded)
        {
            Console.WriteLine($"GetById failed for ID {id}: {string.Join(", ", errors)}");
            return Results.BadRequest(errors);
        }

        return data != null ? Results.Ok(data) : Results.NotFound();
    }

    public async Task<IResult> CreateAsync(object dto)
    {
        if (dto == null)
            return Results.BadRequest("DTO nesmí být null");

        // Konverze JSON objektu na správný typ
        var convertedDto = ConvertToEditDto(dto);
        if (convertedDto == null)
            return Results.BadRequest("Nepodařilo se převést DTO na správný typ");

        var result = await _entityFacade.CreateAsync<TEntity>(convertedDto);

        return result
            .OnSuccess(id => Console.WriteLine($"Created entity with ID: {id}"))
            .OnError(errors => Console.WriteLine($"Create failed: {string.Join(", ", errors)}"))
            .Map(id =>
            {
                var entityName = typeof(TEntity).Name.ToLower().Replace("entity", "");
                var location = $"/v1/{entityName}s/{id}";
                return Results.Created(location, new { Id = id });
            })
            .GetValueOrDefault(Results.BadRequest(result.Errors));
    }

    public async Task<IResult> UpdateAsync(object id, object dto)
    {
        // Validace
        if (id == null)
            return Results.BadRequest("ID nesmí být null");

        if (dto == null)
            return Results.BadRequest("DTO nesmí být null");

        // Speciální validace pro int ID
        if (id is int intId && intId <= 0)
            return Results.BadRequest("ID musí být kladné číslo");

        // Konverze JSON objektu na správný typ
        var convertedDto = ConvertToEditDto(dto);
        if (convertedDto == null)
            return Results.BadRequest("Nepodařilo se převést DTO na správný typ");

        var result = await _entityFacade.UpdateAsync<TEntity>(id, convertedDto);

        return result
            .OnSuccess(success => Console.WriteLine($"Updated entity with ID: {id}, success: {success}"))
            .OnError(errors => Console.WriteLine($"Update failed for ID {id}: {string.Join(", ", errors)}"))
            .Map(success => success ? Results.NoContent() : Results.NotFound())
            .GetValueOrDefault(Results.BadRequest(result.Errors));
    }

    public async Task<IResult> DeleteAsync(object id)
    {
        // Validace ID
        if (id == null)
            return Results.BadRequest("ID nesmí být null");

        // Speciální validace pro int ID
        if (id is int intId && intId <= 0)
            return Results.BadRequest("ID musí být kladné číslo");

        var result = await _entityFacade.DeleteAsync<TEntity>(id);

        return result
            .OnSuccess(success => Console.WriteLine($"Deleted entity with ID: {id}, success: {success}"))
            .OnError(errors => Console.WriteLine($"Delete failed for ID {id}: {string.Join(", ", errors)}"))
            .Map(success => success ? Results.NoContent() : Results.NotFound())
            .GetValueOrDefault(Results.BadRequest(result.Errors));
    }

    public async Task<IResult> GetAllWithSpecificationAsync(ISpecification<TEntity>? specification, bool useCache = false)
    {
        // Použijeme obecnou verzi s object return type
        var result = await _entityFacade.GetAllAsync<TEntity>(specification, useCache);

        return result
            .OnError(errors => Console.WriteLine($"GetAllWithSpecification failed: {string.Join(", ", errors)}"))
            .Map(data => Results.Ok(data))
            .GetValueOrDefault(Results.BadRequest(result.Errors));
    }

    public async Task<IResult> GetPagedWithSpecificationAsync(ISpecification<TEntity>? specification, int pageNumber = 1, int pageSize = 10, bool useCache = false)
    {
        var result = await _entityFacade.GetPagedAsync<TEntity>(pageNumber, pageSize, specification, useCache);

        return result
            .OnError(errors => Console.WriteLine($"GetPagedWithSpecification failed: {string.Join(", ", errors)}"))
            .Map(data => Results.Ok(data))
            .GetValueOrDefault(Results.BadRequest(result.Errors));
    }

    public async Task<IResult> QueryAsync(ISpecification<TEntity>? specification = null, int? pageNumber = null, int? pageSize = null, bool useCache = false)
    {
        // Pokud jsou zadány parametry stránkování, použijeme stránkovaný dotaz
        if (pageNumber.HasValue || pageSize.HasValue)
        {
            var actualPageNumber = pageNumber ?? 1;
            var actualPageSize = pageSize ?? 10;

            var pagedResult = await _entityFacade.GetPagedAsync<TEntity>(actualPageNumber, actualPageSize, specification, useCache);

            return pagedResult
                .OnError(errors => Console.WriteLine($"Paged query failed: {string.Join(", ", errors)}"))
                .Map(data => Results.Ok(data))
                .GetValueOrDefault(Results.BadRequest(pagedResult.Errors));
        }

        // Jinak použijeme běžný dotaz pro všechny entity
        var result = await _entityFacade.GetAllAsync<TEntity>(specification, useCache);

        return result
            .OnError(errors => Console.WriteLine($"Query failed: {string.Join(", ", errors)}"))
            .Map(data => Results.Ok(data))
            .GetValueOrDefault(Results.BadRequest(result.Errors));
    }

    /// <summary>
    /// Konvertuje object (JsonElement) na správný EditDto typ
    /// </summary>
    /// <param name="dto">DTO objekt k převodu</param>
    /// <returns>Převedený DTO nebo null při chybě</returns>
    private object? ConvertToEditDto(object dto)
    {
        try
        {
            var typeInfo = _typeRegistry.GetEntityTypeInfo<TEntity>();
            if (typeInfo == null)
                return null;

            // Pokud je to už správný typ, vrátíme ho
            if (dto.GetType() == typeInfo.EditDtoType)
                return dto;

            // Pokud je to JsonElement, deserializujeme ho
            if (dto is JsonElement jsonElement)
            {
                var json = jsonElement.GetRawText();
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                return JsonSerializer.Deserialize(json, typeInfo.EditDtoType, options);
            }

            // Pokud je to jiný objekt, pokusíme se ho serializovat a deserializovat
            var options2 = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            var jsonString = JsonSerializer.Serialize(dto, options2);
            return JsonSerializer.Deserialize(jsonString, typeInfo.EditDtoType, options2);
        }
        catch (Exception ex)
        {
            // Pro debug účely - v produkci by se mělo logovat
            Console.WriteLine($"Chyba při konverzi DTO: {ex.Message}");
            return null;
        }
    }
}
