# Dokumentace třídy Mediator

## P<PERSON>ehled

T<PERSON><PERSON><PERSON> `Mediator` je implementací n<PERSON>vrhového vzoru **Mediator** v aplikaci DataCapture. Slouží jako centrální bod pro zpracování požadavků (requests) a publikování notifikací (notifications) mezi různými částmi aplikace. Implementuje rozhraní `IMediator` a poskytuje mechanismus pro snížení propojení mezi komponentami aplikace.

## Umístění

- **Namespace**: `Infrastructure.Mediator`
- **Soubor**: `Infrastructure/Mediator/Mediator.cs`
- **Rozhraní**: `Application.Abstraction.Mediator.IMediator`

## Architektura

### Závislosti

```csharp
public class Mediator : IMediator
{
    private readonly IServiceProvider _serviceProvider;
    private readonly INotificationPublisher _notificationPublisher;
}
```

- **IServiceProvider**: Poskytuje přístup k dependency injection kontejneru pro získání handlerů a behaviors
- **INotificationPublisher**: Zodpovědný za distribuci notifikací všem registrovaným handlerům

### Klíčové komponenty

1. **Request/Response Pattern**: Zpracování požadavků s návratovou hodnotou
2. **Notification Pattern**: Publikování událostí více handlerům současně
3. **Pipeline Behaviors**: Middleware pro cross-cutting concerns (cache, validace, logování)

## Veřejné metody

### Send<TResponse>

Odešle požadavek příslušnému handleru a vrátí výsledek.

```csharp
public async Task<TResponse> Send<TResponse>(
    IRequest<TResponse> request, 
    CancellationToken cancellationToken = default)
```

#### Parametry
- **request**: Požadavek implementující `IRequest<TResponse>`
- **cancellationToken**: Token pro zrušení operace (volitelný)

#### Návratová hodnota
- **Task<TResponse>**: Výsledek zpracování požadavku

#### Proces zpracování
1. **Nalezení handleru**: Dynamicky najde odpovídající `IRequestHandler<TRequest, TResponse>`
2. **Získání behaviors**: Načte všechny registrované `IPipelineBehavior<TRequest, TResponse>`
3. **Vytvoření pipeline**: Sestaví řetězec behaviors v opačném pořadí (LIFO)
4. **Spuštění**: Provede celou pipeline včetně handleru

#### Příklad použití
```csharp
var query = new GetUserByIdQuery { Id = 123 };
var user = await mediator.Send(query);
```

### Publish<TNotification>

Publikuje notifikaci všem registrovaným handlerům.

```csharp
public async Task Publish<TNotification>(
    TNotification notification, 
    CancellationToken cancellationToken = default)
    where TNotification : INotification
```

#### Parametry
- **notification**: Notifikace implementující `INotification`
- **cancellationToken**: Token pro zrušení operace (volitelný)

#### Návratová hodnota
- **Task**: Asynchronní operace bez návratové hodnoty

#### Proces zpracování
1. **Nalezení handlerů**: Najde všechny `INotificationHandler<TNotification>`
2. **Kontrola existence**: Pokud nejsou žádné handlery, operace se ukončí
3. **Publikování**: Použije `INotificationPublisher` pro distribuci

#### Příklad použití
```csharp
var notification = new UserCreatedNotification { UserId = 123 };
await mediator.Publish(notification);
```

## Pipeline Behaviors

### Princip fungování

Pipeline behaviors implementují návrhový vzor **Decorator** a umožňují přidat dodatečnou logiku před a po zpracování požadavku:

```csharp
// Vytvoření delegátu pro volání handleru
RequestHandlerDelegate<TResponse> handlerDelegate = () => handler.Handle((dynamic)request, cancellationToken);

// Vytvoření pipeline behaviors v opačném pořadí (LIFO)
foreach (var behavior in behaviors.Reverse())
{
    var next = handlerDelegate;
    handlerDelegate = () => behavior.Handle((dynamic)request, next, cancellationToken);
}

// Spuštění pipeline
return await handlerDelegate();
```

### Pořadí vykonávání

Behaviors jsou registrovány v pořadí, ale vykonávány v **opačném pořadí** (LIFO - Last In, First Out):

```
Registrace: [Behavior1, Behavior2, Behavior3]
Vykonávání: Behavior3 → Behavior2 → Behavior1 → Handler → Behavior1 → Behavior2 → Behavior3
```

### Příklady behaviors v aplikaci

1. **CacheBehavior**: Cachování výsledků queries
2. **CacheInvalidationBehavior**: Invalidace cache po commands
3. **ValidationBehavior**: Validace vstupních dat
4. **LoggingBehavior**: Logování požadavků a odpovědí

## Notification Publisher

### Strategie publikování

Mediator deleguje publikování notifikací na `INotificationPublisher`, který může implementovat různé strategie:

#### ParallelNotificationPublisher
```csharp
public Task Publish<TNotification>(
    IEnumerable<INotificationHandler<TNotification>> handlers,
    TNotification notification,
    CancellationToken cancellationToken)
{
    var tasks = handlers.Select(h => h.Handle(notification, cancellationToken));
    return Task.WhenAll(tasks); // Paralelní zpracování
}
```

#### SerialNotificationPublisher (možná implementace)
```csharp
public async Task Publish<TNotification>(
    IEnumerable<INotificationHandler<TNotification>> handlers,
    TNotification notification,
    CancellationToken cancellationToken)
{
    foreach (var handler in handlers)
    {
        await handler.Handle(notification, cancellationToken); // Sériové zpracování
    }
}
```

## Registrace v DI

### Infrastructure/DependencyInjection.cs
```csharp
// Registrace Mediator služeb
services.AddScoped<IMediator, Mediator>();
services.AddScoped<INotificationPublisher, ParallelNotificationPublisher>();

// Automatická registrace handlerů
services.AddTransient<IRequestHandler<GetUserByIdQuery, UserDto>, GetUserByIdHandler>();
services.AddTransient<INotificationHandler<UserCreatedNotification>, EmailNotificationHandler>();
services.AddTransient<INotificationHandler<UserCreatedNotification>, AuditLogHandler>();

// Registrace pipeline behaviors
services.AddTransient(typeof(IPipelineBehavior<,>), typeof(CacheBehavior<,>));
services.AddTransient(typeof(IPipelineBehavior<,>), typeof(CacheInvalidationBehavior<,>));
```

## Příklady použití

### 1. Query s cache behavior

```csharp
public class GetUserByIdQuery : IRequest<UserDto>, ICachableQuery<UserDto>
{
    public int Id { get; set; }
    public string CacheKey => $"User:{Id}";
    public IEnumerable<string>? Tags => new[] { "Users" };
}

public class GetUserByIdHandler : IRequestHandler<GetUserByIdQuery, UserDto>
{
    public async Task<UserDto> Handle(GetUserByIdQuery request, CancellationToken cancellationToken)
    {
        // Implementace načtení uživatele
        return new UserDto { Id = request.Id, Name = "John Doe" };
    }
}

// Použití
var query = new GetUserByIdQuery { Id = 123 };
var user = await mediator.Send(query); // Projde přes CacheBehavior
```

### 2. Command s cache invalidation

```csharp
public class CreateUserCommand : IRequest<int>, IInvalidateCache
{
    public string Name { get; set; }
    public IEnumerable<string> CacheKeys => Array.Empty<string>();
    public IEnumerable<string>? CacheTags => new[] { "Users" };
}

public class CreateUserHandler : IRequestHandler<CreateUserCommand, int>
{
    public async Task<int> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        // Implementace vytvoření uživatele
        return 456; // ID nového uživatele
    }
}

// Použití
var command = new CreateUserCommand { Name = "Jane Doe" };
var userId = await mediator.Send(command); // Projde přes CacheInvalidationBehavior
```

### 3. Notification s více handlery

```csharp
public class UserCreatedNotification : INotification
{
    public int UserId { get; set; }
    public string UserName { get; set; }
}

public class EmailNotificationHandler : INotificationHandler<UserCreatedNotification>
{
    public async Task Handle(UserCreatedNotification notification, CancellationToken cancellationToken)
    {
        // Odeslání uvítacího emailu
        await SendWelcomeEmail(notification.UserName);
    }
}

public class AuditLogHandler : INotificationHandler<UserCreatedNotification>
{
    public async Task Handle(UserCreatedNotification notification, CancellationToken cancellationToken)
    {
        // Záznam do audit logu
        await LogUserCreation(notification.UserId);
    }
}

// Použití
var notification = new UserCreatedNotification { UserId = 456, UserName = "Jane Doe" };
await mediator.Publish(notification); // Spustí oba handlery paralelně
```

## Výhody implementace

### 1. Oddělení zodpovědností
- Každý handler má jednu konkrétní zodpovědnost
- Mediator koordinuje komunikaci mezi komponentami
- Snížení přímých závislostí mezi třídami

### 2. Rozšiřitelnost
- Snadné přidání nových handlerů bez změny existujícího kódu
- Pipeline behaviors pro cross-cutting concerns
- Různé strategie publikování notifikací

### 3. Testovatelnost
- Každý handler lze testovat izolovaně
- Mock objekty pro dependencies
- Jasně definované vstupy a výstupy

### 4. Konzistence
- Jednotný způsob zpracování požadavků v celé aplikaci
- Centralizované logování a error handling
- Standardizované rozhraní pro všechny operace

## Výkonnostní aspekty

### Dynamic dispatch
Mediator používá `dynamic` klíčové slovo pro volání handlerů, což může mít mírný výkonnostní dopad. Toto řešení bylo zvoleno pro jednoduchost implementace.

### Pipeline overhead
Každý behavior přidává další vrstvu do pipeline, což může ovlivnit výkon při velkém počtu behaviors.

### Notification publishing
Paralelní publikování notifikací může zlepšit výkon, ale může také způsobit problémy s konzistencí dat.

## Doporučení pro použití

### 1. Naming conventions
- **Queries**: `Get{Entity}ByIdQuery`, `GetAll{Entities}Query`
- **Commands**: `Create{Entity}Command`, `Update{Entity}Command`, `Delete{Entity}Command`
- **Notifications**: `{Entity}CreatedNotification`, `{Entity}UpdatedNotification`

### 2. Error handling
- Používejte `Result<T>` pattern pro error handling
- Implementujte globální exception handling behavior
- Logujte chyby na úrovni behaviors, ne handlerů

### 3. Performance
- Používejte cache behaviors pro často čtené data
- Implementujte timeout behaviors pro dlouho běžící operace
- Monitorujte výkon pipeline behaviors

### 4. Testing
- Testujte handlery izolovaně bez mediátoru
- Používejte integration testy pro celou pipeline
- Mockujte dependencies v unit testech

## Závěr

Třída `Mediator` poskytuje robustní a flexibilní implementaci návrhového vzoru Mediator, která umožňuje čistou architekturu s oddělením zodpovědností. Díky podpoře pipeline behaviors a různých strategií publikování notifikací je vhodná pro komplexní aplikace s požadavky na rozšiřitelnost a testovatelnost.
