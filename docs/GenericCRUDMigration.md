# Migrace na Generické CRUD Operace

Tento dokument popisuje migraci z entity-specifických dotazů na generické CRUD operace v DataCapture aplikaci.

## Přehled Změn

Aplikace byla refaktorována tak, aby použ<PERSON><PERSON>a pouze generické dotazy a příkazy pro všechny obvyklé CRUD operace. Specifické dotazy pro Sample entity byly odstraněny a nahrazeny generickými ekvivalenty.

## Odstraněné Soubory

### Specifické Sample Dotazy (ODSTRANĚNO)
- `Application/Features/Sample/Queries/GetSampleByIdQuery.cs`
- `Application/Features/Sample/Queries/GetAllSamplesQuery.cs`
- `Application/Features/Sample/Queries/GetPagedSamplesQuery.cs`
- `Application/Features/Sample/Queries/` (celý adresář)

### Obsolete Dotazy (OZNAČENO JAKO OBSOLETE)
- `GetEntityByIdCachedQuery<TDto>` - nahrazeno parametrickou cache v `GetEntityByIdQuery<TDto>`

## Migrace Dotazů

### 1. GetAllSamplesQuery → GetAllEntitiesQuery<SampleDto>

**Starý způsob:**
```csharp
var query = new GetAllSamplesQuery();
var result = await mediator.Send(query);
```

**Nový způsob:**
```csharp
var query = new GetAllEntitiesQuery<SampleDto> { UseCache = true };
var result = await mediator.Send(query);
```

### 2. GetSampleByIdQuery → GetEntityByIdQuery<SampleDto>

**Starý způsob:**
```csharp
var query = new GetSampleByIdQuery(id: 123, useCache: true);
var result = await mediator.Send(query);
```

**Nový způsob:**
```csharp
var query = new GetEntityByIdQuery<SampleDto> { Id = 123, UseCache = true };
var result = await mediator.Send(query);
```

### 3. GetPagedSamplesQuery → GetPagedEntitiesQuery<SampleDto>

**Starý způsob:**
```csharp
var query = new GetPagedSamplesQuery
{
    PageNumber = 1,
    PageSize = 20,
    SortBy = "Name",
    SortDescending = false,
    NameFilter = "test"  // Specifické filtrování
};
```

**Nový způsob:**
```csharp
var query = new GetPagedEntitiesQuery<SampleDto>
{
    PageNumber = 1,
    PageSize = 20,
    SortBy = "Name",
    SortDescending = false,
    UseCache = true
    // Poznámka: Specifické filtrování (NameFilter) není podporováno v generickém dotazu
};
```

### 4. GetEntityByIdCachedQuery → GetEntityByIdQuery s UseCache

**Starý způsob (OBSOLETE):**
```csharp
var query = new GetEntityByIdCachedQuery<SampleDto> { Id = 123 };
var result = await mediator.Send(query);
```

**Nový způsob:**
```csharp
var query = new GetEntityByIdQuery<SampleDto> { Id = 123, UseCache = true };
var result = await mediator.Send(query);
```

## Aktualizované API Endpoints

### SampleEntitiesEndpoints.cs

#### GET /v1/sample-entities
```csharp
// Nový způsob s explicitní cache
var items = await mediator.Send(new GetAllEntitiesQuery<SampleDto> { UseCache = true });
```

#### GET /v1/sample-entities/{id}
```csharp
// Nový způsob s parametrickou cache
var result = await mediator.Send(new GetEntityByIdQuery<SampleDto> { Id = id, UseCache = true });
```

#### GET /v1/sample-entities/paged (NOVÝ ENDPOINT)
```csharp
var query = new GetPagedEntitiesQuery<SampleDto>
{
    PageNumber = pageNumber,
    PageSize = pageSize,
    SortBy = sortBy,
    SortDescending = sortDescending,
    UseCache = true
};
var result = await mediator.Send(query);
```

## Aktualizované Controllery

### SampleController.cs
```csharp
// GetAll endpoint
var query = new GetAllEntitiesQuery<SampleDto> { UseCache = true };

// GetPaged endpoint  
var query = new GetPagedEntitiesQuery<SampleDto>
{
    PageNumber = pageNumber,
    PageSize = pageSize,
    SortBy = sortBy,
    SortDescending = sortDescending,
    UseCache = true
};
```

## Aktualizované Razor Components

### Weather.razor
```csharp
@using Application.Features.Generic.Queries

private GetAllEntitiesQuery<SampleDto> _samplesQuery { get; set; } = new();

// V OnInitializedAsync
var query = new GetAllEntitiesQuery<SampleDto> { UseCache = true };
```

## Výhody Generického Přístupu

### 1. **Méně Kódu**
- Odstraněno ~150 řádků specifického kódu
- Žádné duplicitní handlery pro každou entitu
- Jednotná implementace napříč všemi entitami

### 2. **Konzistentní API**
- Všechny entity používají stejné vzory dotazů
- Jednotná cache strategie
- Standardizované parametry (UseCache, PageSize, SortBy, atd.)

### 3. **Snadná Rozšiřitelnost**
- Přidání nové entity vyžaduje pouze:
  - Definici entity
  - DTO třídy
  - Registraci v `DependencyInjection.cs`
- Žádné specifické dotazy nebo handlery

### 4. **Lepší Testovatelnost**
- Jeden set testů pokrývá všechny entity
- Méně mock objektů
- Konzistentní testovací vzory

## Omezení a Kompromisy

### 1. **Ztráta Specifického Filtrování**
- Generické dotazy nepodporují entity-specifické filtry
- Pro složité filtrování je potřeba vytvořit specifické dotazy
- Alternativa: Použít obecné filtrování nebo post-processing

### 2. **Méně Flexibilní Cache Klíče**
- Cache klíče jsou generované automaticky
- Nelze snadno customizovat cache strategii pro specifické entity

### 3. **Potenciální Over-fetching**
- Generické dotazy mohou načítat více dat než je potřeba
- Pro optimalizaci může být potřeba specifické dotazy

## Doporučení pro Budoucí Vývoj

### 1. **Kdy Použít Generické Dotazy**
- ✅ Základní CRUD operace
- ✅ Standardní stránkování a řazení
- ✅ Jednoduché filtrování podle ID
- ✅ Cache pro často používaná data

### 2. **Kdy Vytvořit Specifické Dotazy**
- ❌ Složité business logika
- ❌ Entity-specifické filtry
- ❌ Komplexní joins a agregace
- ❌ Výkonově kritické operace

### 3. **Vzor pro Nové Entity**
```csharp
// 1. Definice entity
public class NewEntity : BaseAuditableEntity<int> { ... }

// 2. DTO třídy
public class NewEntityDto { ... }
public class NewEntityAddEdit { ... }

// 3. Registrace v DI
entityInfos.Add(new EntityInfo
{
    EntityType = typeof(NewEntity),
    KeyType = typeof(int),
    DtoType = typeof(NewEntityDto),
    AddEditType = typeof(NewEntityAddEdit)
});

// 4. Použití generických dotazů
var query = new GetAllEntitiesQuery<NewEntityDto> { UseCache = true };
```

## Závěr

Migrace na generické CRUD operace významně zjednodušila kódovou základnu a zlepšila konzistenci API. Aplikace nyní podporuje standardní CRUD operace pro všechny entity pomocí jednotného setu generických dotazů a příkazů s plnou podporou cache invalidation.
