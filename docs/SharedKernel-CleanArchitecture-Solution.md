# Řešení problémů s čistou architekturou v SharedKernel

## Problém

Projekt SharedKernel obsahoval externí závislosti na Microsoft.EntityFrameworkCore, které porušovaly principy čisté architektury. Konkrétně:

1. **Závislosti v SharedKernel.csproj:**
   - `Microsoft.EntityFrameworkCore` (Version="9.0.7")
   - `Microsoft.EntityFrameworkCore.Abstractions` (Version="9.0.7")

2. **Použití EF-specifických atributů:**
   - `[NotMapped]` atribut v `BaseEntity<T>.cs`
   - `[Timestamp]` atribut v `IEntity<T>.cs`

## Řešení

### 1. Odstranění problematických závislostí

Odstraněny závislosti na Entity Framework ze SharedKernel.csproj:
```xml
<!-- ODSTRANĚNO -->
<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.7" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Abstractions" Version="9.0.7" />
```

### 2. Obecná konfigurace entit na úrovni Infrastructure

Vytvořeno řešení na úrovni Infrastructure vrstvy, které automaticky nakonfiguruje všechny entity ze SharedKernel:

#### ApplicationDbContext.cs
```csharp
protected override void OnModelCreating(ModelBuilder modelBuilder)
{
    base.OnModelCreating(modelBuilder);
    
    // Aplikujeme obecnou konfiguraci pro všechny entity ze SharedKernel
    ConfigureSharedKernelEntities(modelBuilder);
    
    // Aplikujeme specifické konfigurace entit
    modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);
}

private void ConfigureSharedKernelEntities(ModelBuilder modelBuilder)
{
    foreach (var entityType in modelBuilder.Model.GetEntityTypes())
    {
        var clrType = entityType.ClrType;

        // Konfigurace optimistického zamykání pro entity implementující IEntity<T>
        var entityInterface = clrType.GetInterfaces()
            .FirstOrDefault(i => i.IsGenericType && 
                           i.GetGenericTypeDefinition() == typeof(SharedKernel.Domain.IEntity<>));

        if (entityInterface != null)
        {
            var rowVersionProperty = entityType.FindProperty("RowVersion");
            if (rowVersionProperty != null)
            {
                modelBuilder.Entity(clrType)
                    .Property("RowVersion")
                    .IsRowVersion()
                    .HasDefaultValue(new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 })
                    .HasComment("Verze řádku pro optimistické zamykání");
            }
        }

        // Vyloučení DomainEvents z mapování pro entity dědící z BaseEntity<T>
        if (clrType.IsAssignableFromGeneric(typeof(SharedKernel.Domain.BaseEntity<>)))
        {
            modelBuilder.Entity(clrType)
                .Ignore("DomainEvents");
        }
    }
}
```

#### TypeExtensions.cs
Vytvořena pomocná třída pro práci s generickými typy:
```csharp
public static class TypeExtensions
{
    public static bool IsAssignableFromGeneric(this Type type, Type genericType)
    {
        if (type == null || genericType == null)
            return false;

        if (!genericType.IsGenericTypeDefinition)
            return genericType.IsAssignableFrom(type);

        // Procházíme hierarchii typů
        var currentType = type;
        while (currentType != null)
        {
            if (currentType.IsGenericType && 
                currentType.GetGenericTypeDefinition() == genericType)
            {
                return true;
            }
            currentType = currentType.BaseType;
        }

        // Zkontrolujeme implementovaná rozhraní
        foreach (var interfaceType in type.GetInterfaces())
        {
            if (interfaceType.IsGenericType && 
                interfaceType.GetGenericTypeDefinition() == genericType)
            {
                return true;
            }
        }

        return false;
    }
}
```

### 3. Automatické funkce

Řešení poskytuje následující automatické funkce:

1. **Optimistické zamykání:**
   - Automaticky nakonfiguruje `RowVersion` vlastnost jako `IsRowVersion()`
   - Nastaví výchozí hodnotu pro SQLite
   - Přidá komentář pro dokumentaci

2. **Vyloučení DomainEvents:**
   - Automaticky vyloučí `DomainEvents` vlastnost z mapování do databáze
   - Platí pro všechny entity dědící z `BaseEntity<T>`

3. **Obecnost:**
   - Funguje pro všechny entity implementující `IEntity<T>` nebo dědící z `BaseEntity<T>`
   - Není potřeba specifická konfigurace pro každou entitu
   - Automaticky se aplikuje na nové entity

### 4. Testování

Vytvořeny testy pro ověření správné funkčnosti:

```csharp
[Fact]
public void RowVersion_Should_Be_Configured_For_SharedKernel_Entities()
{
    var sampleEntityType = _context.Model.FindEntityType(typeof(SampleEntity));
    var rowVersionProperty = sampleEntityType.FindProperty("RowVersion");
    
    Assert.NotNull(rowVersionProperty);
    Assert.True(rowVersionProperty.IsConcurrencyToken);
}

[Fact]
public void DomainEvents_Should_Be_Ignored_For_SharedKernel_Entities()
{
    var sampleEntityType = _context.Model.FindEntityType(typeof(SampleEntity));
    var domainEventsProperty = sampleEntityType.FindProperty("DomainEvents");
    
    Assert.Null(domainEventsProperty); // Není mapováno do databáze
}
```

## Výsledek

### ✅ Vyřešené problémy:
1. **SharedKernel je nezávislý** na infrastrukturních technologiích
2. **Architektonické testy prochází** - SharedKernel závisí pouze na System.* a Microsoft.*
3. **Optimistické zamykání funguje** automaticky pro všechny entity
4. **DomainEvents nejsou mapovány** do databáze
5. **Obecné řešení** - funguje pro všechny současné i budoucí entity

### 🔧 Klíčové výhody:
- **Čistá architektura** - SharedKernel neobsahuje infrastrukturní závislosti
- **Automatizace** - není potřeba manuální konfigurace pro každou entitu
- **Konzistence** - všechny entity mají stejné chování
- **Udržitelnost** - nové entity automaticky získají správnou konfiguraci
- **Testovatelnost** - řešení je pokryto testy

## Závěr

Řešení úspěšně odstraňuje porušení principů čisté architektury v SharedKernel projektu a poskytuje robustní, automatizovanou konfiguraci entit na úrovni Infrastructure vrstvy. Všechny architektonické testy nyní prochází a systém je připraven pro budoucí rozšíření.
