# Parametrická Cache v Generických Dotazech

Tento dokument popisuje implementaci parametrické cache funkcionality v generických dotazech aplikace.

## Přehled

Aplikace nyní podporuje parametrickou cache pro všechny tři základní generické dotazy:
- `GetEntityByIdQuery<TDto>`
- `GetAllEntitiesQuery<TDto>`
- `GetPagedEntitiesQuery<TDto>`

Cache je implementována pomocí parametru `UseCache`, který umožňuje zapnout nebo vypnout cache pro konkrétní dotaz.

## Použití

### GetEntityByIdQuery s Cache

```csharp
// S cache (doporučeno pro často používané entity)
var query = new GetEntityByIdQuery<SampleDto>
{
    Id = 123,
    UseCache = true
};

// Bez cache (pro aktuální data)
var query = new GetEntityByIdQuery<SampleDto>
{
    Id = 123,
    UseCache = false // výchozí hodnota
};
```

### GetAllEntitiesQuery s Cache

```csharp
// S cache (doporučeno pro malé, stabilní datasety)
var query = new GetAllEntitiesQuery<SampleDto>
{
    UseCache = true
};

// Bez cache (pro aktuální data)
var query = new GetAllEntitiesQuery<SampleDto>
{
    UseCache = false // výchozí hodnota
};
```

### GetPagedEntitiesQuery s Cache

```csharp
// S cache (doporučeno pro často zobrazované stránky)
var query = new GetPagedEntitiesQuery<SampleDto>
{
    PageNumber = 1,
    PageSize = 20,
    SortBy = "Name",
    UseCache = true
};

// Bez cache (pro aktuální data)
var query = new GetPagedEntitiesQuery<SampleDto>
{
    PageNumber = 1,
    PageSize = 20,
    UseCache = false // výchozí hodnota
};
```

## Sample Dotazy s Cache

Aplikace obsahuje ukázkové dotazy, které demonstrují použití cache:

### GetAllSamplesQuery
```csharp
// Automaticky zapnutá cache
var query = new GetAllSamplesQuery(); // UseCache = true

// Explicitně vypnutá cache
var query = new GetAllSamplesQuery(useCache: false);
```

### GetPagedSamplesQuery
```csharp
// Automaticky zapnutá cache
var query = new GetPagedSamplesQuery(); // UseCache = true

// Explicitně vypnutá cache
var query = new GetPagedSamplesQuery(useCache: false);
```

### GetSampleByIdQuery
```csharp
// S cache
var query = new GetSampleByIdQuery(id: 123, useCache: true);

// Bez cache
var query = new GetSampleByIdQuery(id: 123, useCache: false);
```

## Cache Klíče

Cache klíče jsou automaticky generovány na základě typu dotazu a parametrů:

- **GetEntityByIdQuery**: `"GetById_{TDto}_{Id}"`
- **GetAllEntitiesQuery**: `"GetAll_{TDto}"`
- **GetPagedEntitiesQuery**: `"GetPaged_{TDto}_Page{PageNumber}_Size{PageSize}[_Sort{SortBy}[Desc]]"`

### Příklady Cache Klíčů

```csharp
// GetEntityByIdQuery<SampleDto> s Id = 123
"GetById_SampleDto_123"

// GetAllEntitiesQuery<SampleDto>
"GetAll_SampleDto"

// GetPagedEntitiesQuery<SampleDto> s PageNumber = 2, PageSize = 20
"GetPaged_SampleDto_Page2_Size20"

// GetPagedEntitiesQuery<SampleDto> s řazením podle Name sestupně
"GetPaged_SampleDto_Page1_Size10_SortNameDesc"
```

## Cache Tagy

Všechny dotazy používají název DTO typu jako cache tag pro invalidaci:
```csharp
Tags = new[] { typeof(TDto).Name }
```

## Výchozí Chování

- **Výchozí hodnota `UseCache`**: `false` pro všechny dotazy
- **Cache expiration**: 10 minut (nastaveno v `CacheBehavior`)
- **Cache provider**: `MemoryCacheService` (in-memory cache)

## Migrace z Obsolete Dotazů

### GetEntityByIdCachedQuery (OBSOLETE)

**Starý způsob:**
```csharp
var query = new GetEntityByIdCachedQuery<SampleDto> { Id = 123 };
```

**Nový způsob:**
```csharp
var query = new GetEntityByIdQuery<SampleDto> { Id = 123, UseCache = true };
```

## Výhody Parametrické Cache

1. **Jednotné API**: Všechny dotazy používají stejný vzor
2. **Flexibilita**: Cache lze zapnout/vypnout per-request
3. **Méně kódu**: Není potřeba udržovat separátní cached verze dotazů
4. **Zpětná kompatibilita**: Výchozí chování zůstává nezměněno
5. **Lepší testovatelnost**: Jednodušší mockování a testování

## Doporučení

### Kdy použít cache:
- Často čtená data
- Data, která se mění zřídka
- Výsledky složitých dotazů
- Stránkování často zobrazovaných seznamů

### Kdy nepoužívat cache:
- Kritická data vyžadující aktuálnost
- Data, která se často mění
- Jednorázové dotazy
- Velké datasety (kvůli paměťovým nárokům)

## Implementační Detaily

Cache je implementována pomocí:
- `ICachableQuery<TResult>` interface
- `CacheBehavior<TRequest, TResponse>` pipeline behavior
- Podmíněná implementace cache klíčů (prázdný klíč = bez cache)
- Automatická registrace v DI kontejneru
