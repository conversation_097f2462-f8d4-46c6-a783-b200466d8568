# DataCapture - <PERSON>rické CRUD s Cache Invalidation

Tento projekt implementuje Clean Architecture s generickými CRUD operacemi a pokročilým cache invalidation systémem.

## 🎯 Klíčové Funkce

### ✅ **Generické CRUD Operace**
- **Jednotné API** pro všechny entity
- **Parametrická cache** s `UseCache` parametrem
- **Automatická registrace** handlerů v DI
- **Konzistentní vzory** napříč celou aplikací

### ✅ **Pokročilý Cache Invalidation**
- **Tag-based invalidation** podle názvu entity
- **Automatická invalidace** při změnách dat
- **Pipeline behaviors** pro cache a invalidation
- **Kompletní testovací pokrytí** (38 testů)

### ✅ **Clean Architecture**
- **Dependency inversion** - závislosti směřují dovnitř
- **Separation of concerns** - jasn<PERSON> oddělení vrstev
- **SOLID principy** - dodržení všech SOLID principů
- **Testovatelnost** - vysoké pokrytí testy

## 📁 Struktura Projektu

```
DataCapture/
├── SharedKernel/           # Sdílené abstrakce a modely
├── Domain/                 # Business logika a entity
├── Application/            # Use cases a aplikační logika
│   ├── Features/Generic/   # Generické CRUD operace
│   ├── Pipeline/          # Mediator behaviors
│   └── Services/          # Aplikační služby
├── Infrastructure/         # Implementace abstrakcí
│   ├── Persistence/       # Database context a konfigurace
│   ├── Services/          # Cache, mediator implementace
│   └── Migrations/        # EF Core migrace
├── API/                   # REST API endpoints
├── DataCapture/           # Blazor Server UI
└── Tests/                 # Unit a integrační testy
```

## 🚀 Generické Dotazy

### GetAllEntitiesQuery<TDto>
```csharp
var query = new GetAllEntitiesQuery<SampleDto> { UseCache = true };
var result = await mediator.Send(query);
```

### GetEntityByIdQuery<TDto>
```csharp
var query = new GetEntityByIdQuery<SampleDto> 
{ 
    Id = 123, 
    UseCache = true 
};
var result = await mediator.Send(query);
```

### GetPagedEntitiesQuery<TDto>
```csharp
var query = new GetPagedEntitiesQuery<SampleDto>
{
    PageNumber = 1,
    PageSize = 20,
    SortBy = "Name",
    SortDescending = false,
    UseCache = true
};
var result = await mediator.Send(query);
```

## 🚀 Generické Příkazy

### CreateEntityCommand<TEntity, TEditDto, TKey>
```csharp
var command = new CreateEntityCommand<SampleEntity, SampleDto, int>
{
    Payload = new SampleDto { Name = "Nový vzorek" }
};
var result = await mediator.Send(command);
```

### UpdateEntityCommand<TEntity, TEditDto, TKey>
```csharp
var command = new UpdateEntityCommand<SampleEntity, SampleDto, int>
{
    Id = 123,
    Payload = new SampleDto { Name = "Aktualizovaný vzorek" }
};
var result = await mediator.Send(command);
```

### DeleteEntityCommand<TEntity, TKey>
```csharp
var command = new DeleteEntityCommand<SampleEntity, int> { Id = 123 };
var result = await mediator.Send(command);
```

## 💾 Cache Systém

### Parametrická Cache
- **UseCache parametr** - zapnutí/vypnutí cache per-request
- **Automatické cache klíče** - generované podle typu a parametrů
- **Tag-based invalidation** - invalidace podle názvu entity

### Cache Klíče
- `GetById_{TDto}_{Id}` - např. "GetById_SampleDto_123"
- `GetAll_{TDto}` - např. "GetAll_SampleDto"  
- `GetPaged_{TDto}_Page{N}_Size{M}[_Sort{Field}[Desc]]`

### Cache Invalidation
```csharp
// Při vytvoření/aktualizaci/smazání entity se automaticky invaliduje cache
await mediator.Send(new CreateEntityCommand<SampleEntity, SampleDto, int>
{
    Payload = new SampleDto { Name = "Test" }
});
// → Automaticky se invalidují všechny cache záznamy s tagem "SampleEntity"
```

## 🏗️ Přidání Nové Entity

### 1. Definice Entity
```csharp
public class NewEntity : BaseAuditableEntity<int>
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}
```

### 2. DTO Třídy
```csharp
public class NewEntityDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}
```

### 3. Registrace v DI
```csharp
// V Application/DependencyInjection.cs
entityInfos.Add(new EntityInfo
{
    EntityType = typeof(NewEntity),
    KeyType = typeof(int),
    DtoType = typeof(NewEntityDto),
    AddEditType = typeof(NewEntityDto)
});
```

### 4. Použití
```csharp
// Všechny CRUD operace jsou automaticky dostupné
var allEntities = await mediator.Send(new GetAllEntitiesQuery<NewEntityDto> { UseCache = true });
var entity = await mediator.Send(new GetEntityByIdQuery<NewEntityDto> { Id = 1, UseCache = true });
var created = await mediator.Send(new CreateEntityCommand<NewEntity, NewEntityDto, int> { Payload = dto });
```

## 🧪 Testování

### Spuštění Testů
```bash
# Všechny testy
dotnet test

# Pouze Infrastructure testy
dotnet test Infrastructure.Tests

# Cache testy
dotnet test Infrastructure.Tests --filter "FullyQualifiedName~Cache"
```

### Testovací Pokrytí
- **38 testů** v Infrastructure.Tests
- **Cache invalidation** - 5 testů
- **Tag-based cache** - 8 testů
- **Parametrická cache** - 9 testů
- **Integrační testy** - 6 testů

## 📚 Dokumentace

- **[Cache Invalidation](CacheInvalidation.md)** - Kompletní popis cache invalidation systému
- **[Generic CRUD Migration](GenericCRUDMigration.md)** - Migrace z entity-specifických dotazů
- **[Parametric Cache](ParametricCache.md)** - Parametrická cache funkcionalita

## 🛠️ Technologie

- **.NET 9** - Nejnovější verze .NET
- **Entity Framework Core** - ORM pro databázi
- **MediatR** - Mediator pattern implementace
- **Blazor Server** - UI framework
- **xUnit** - Testovací framework
- **SQLite** - Databáze pro development
- **Scalar** - API dokumentace

## 🎯 Výhody Architektury

### 1. **DRY Princip**
- Žádná duplicita kódu pro CRUD operace
- Jednotné vzory napříč entitami
- Znovupoužitelné komponenty

### 2. **Výkon**
- Inteligentní cache s automatickou invalidací
- Optimalizované databázové dotazy
- Lazy loading a stránkování

### 3. **Udržovatelnost**
- Jasná separace zodpovědností
- Snadné testování
- Konzistentní API

### 4. **Rozšiřitelnost**
- Snadné přidání nových entit
- Flexibilní cache strategie
- Modulární architektura

## 🚀 Spuštění Aplikace

```bash
# Klonování repository
git clone https://github.com/KindlMichal/DataCapture.git
cd DataCapture

# Restore balíčků
dotnet restore

# Spuštění migrace
dotnet ef database update --project Infrastructure --startup-project API

# Spuštění API
dotnet run --project API

# Spuštění Blazor UI
dotnet run --project DataCapture
```

## 📝 Licence

Tento projekt je licencován pod MIT licencí.
