# Konfigurovatelný Cache Prefix

Tento dokument popisuje implementaci konfigurovatelného cache prefixu podle Clean Architecture principů.

## Problém

Původně byl cache prefix hardcoded jako konstanta `"DataCapture"` v `SharedKernel/Constants/CacheKeys.cs`. To způsobovalo problémy při:

- Nasazení do různých prostředí (dev, staging, prod)
- Používání stejné cache instance pro více aplikací
- Testování s izolovanými cache klíči
- Porušování Clean Architecture (cache klíče nepatří do SharedKernel)

## Řešení

### 1. ICacheKeyProvider Interface

Vytvořeno nové rozhraní `ICacheKeyProvider` v `Application/Abstraction/`:

```csharp
public interface ICacheKeyProvider
{
    string AppPrefix { get; }
    string Separator { get; }
    
    string WithPrefix(string key);
    string ForEntity(string entityName, string operation);
    string ForPagedQuery(string entityName, int pageNumber, int pageSize, 
        string? sortBy = null, bool sortDescending = false);
    string ForUser(string userId, string dataType);
}
```

### 2. CacheKeyProvider Implementation

Implementace v `Infrastructure/Services/CacheKeyProvider.cs`:

```csharp
public class CacheKeyProvider : ICacheKeyProvider
{
    private readonly string _appPrefix;
    private readonly string _separator;

    public CacheKeyProvider(IConfiguration configuration)
    {
        _appPrefix = configuration["Cache:Prefix"] ?? CacheKeys.DefaultAppPrefix;
        _separator = configuration["Cache:Separator"] ?? CacheKeys.Separator;
    }
    
    // ... implementace metod
}
```

### 3. Konfigurace

#### appsettings.json
```json
{
  "Cache": {
    "Prefix": "DataCapture",
    "Separator": "_"
  }
}
```

#### appsettings.Development.json
```json
{
  "Cache": {
    "Prefix": "DataCapture_Dev",
    "Separator": "_"
  }
}
```

### 4. Registrace v DI

V `Infrastructure/DependencyInjection.cs`:

```csharp
services.AddScoped<Application.Abstraction.ICacheKeyProvider, CacheKeyProvider>();
```

## Použití

### Doporučený způsob (nový)

```csharp
public class MyService
{
    private readonly ICacheKeyProvider _cacheKeyProvider;
    
    public MyService(ICacheKeyProvider cacheKeyProvider)
    {
        _cacheKeyProvider = cacheKeyProvider;
    }
    
    public void DoSomething()
    {
        var cacheKey = _cacheKeyProvider.ForEntity("SampleEntity", "GetAll");
        // Výsledek: "DataCapture_GetAll_SampleEntity" (nebo jiný prefix podle konfigurace)
    }
}
```

### Starý způsob (odstraněn)

```csharp
// ODSTRANĚNO - cache klíče byly přesunuty z SharedKernel do Infrastructure
// var cacheKey = CacheKeys.ForEntity("SampleEntity", "GetAll");
```

## Výhody

1. **Konfigurovatelnost** - Prefix lze nastavit podle prostředí
2. **Izolace** - Různé aplikace/prostředí mohou mít různé prefixy
3. **Testovatelnost** - Testy mohou používat vlastní prefixy
4. **Clean Architecture** - Cache klíče jsou správně umístěny v Infrastructure vrstvě
5. **Jednoduchost** - Zjednodušená struktura bez zbytečné komplexity

## Příklady konfigurace

### Produkční prostředí
```json
{
  "Cache": {
    "Prefix": "DataCapture_Prod",
    "Separator": "_"
  }
}
```

### Testovací prostředí
```json
{
  "Cache": {
    "Prefix": "DataCapture_Test",
    "Separator": "_"
  }
}
```

### Vývoj s vlastním prefixem
```json
{
  "Cache": {
    "Prefix": "MyApp_Dev",
    "Separator": "-"
  }
}
```

## Migrace

### Krok 1: Aktualizace kódu
Implementujte `ICacheKeyProvider` ve svých službách:

```csharp
// Nová implementace
public class MyService
{
    private readonly ICacheKeyProvider _cacheKeyProvider;

    public MyService(ICacheKeyProvider cacheKeyProvider)
    {
        _cacheKeyProvider = cacheKeyProvider;
    }

    public void DoSomething()
    {
        var key = _cacheKeyProvider.ForEntity("User", "GetAll");
    }
}
```

### Krok 2: Konfigurace
Přidejte sekci `Cache` do svých appsettings souborů.

### Krok 3: Testování
Ověřte, že cache klíče se generují správně s novým prefixem.

## Testování

Implementace obsahuje kompletní testovací pokrytí v `Infrastructure.Tests/Services/CacheKeyProviderTests.cs`:

- Test výchozí konfigurace
- Test vlastní konfigurace
- Test generování různých typů klíčů
- Test zachování informací o separátoru
- Test stránkovaných dotazů s různými parametry

## Poznámky

- Cache klíče byly přesunuty z SharedKernel do Infrastructure vrstvy
- Abstrakce je v Application vrstvě podle Clean Architecture
- Výchozí hodnoty zůstávají stejné ("DataCapture", "_")
- Konfigurace je načítána při startu aplikace
- Provider je registrován jako Scoped služba
- Řešení je nyní jednoduché a dodržuje architektonické principy
