# Cache Invalidation v DataCapture Aplikaci

Tento dokument popisuje kompletní implementaci cache invalidation systému, k<PERSON><PERSON> zajišťuje konzistenci cache při změnách dat.

## Přehled

Aplikace implementuje automatickou cache invalidation pomocí tag-based systému, kter<PERSON> zaj<PERSON>šťu<PERSON>, že cache se automaticky vymaže při změnách dat prostřednictvím příkazů (commands).

## Jak Cache Invalidation Funguje

### 1. **Tag-based Systém**
- Každý cache záznam má přiřazené tagy
- Dotazy používají tag založený na názvu entity (např. "SampleEntity")
- Příkazy invalidují cache podle stejného tagu

### 2. **Automatická Invalidation**
Cache se automaticky invaliduje při:
- **CreateEntityCommand** - vytvoření nové entity
- **UpdateEntityCommand** - aktualizace existující entity
- **DeleteEntityCommand** - smazání entity

### 3. **Pipeline Behaviors**
Systém používá dva pipeline behaviors:
- **CacheBehavior** - ukládá výsledky dotazů do cache
- **CacheInvalidationBehavior** - invaliduje cache po provedení příkazů

## Implementace

### Cache Tagy v Dotazech
```csharp
public class GetEntityByIdQuery<TDto> : IRequest<Result<TDto>>, ICachableQuery<Result<TDto>>
{
    public string? EntityName { get; set; } // Nastaveno handlerem
    public bool UseCache { get; set; } = false;
    
    public string CacheKey => UseCache ? $"GetById_{typeof(TDto).Name}_{Id}" : string.Empty;
    public IEnumerable<string>? Tags => UseCache && !string.IsNullOrEmpty(EntityName) 
        ? new[] { EntityName } : null;
}
```

### Cache Tagy v Příkazech
```csharp
public class CreateEntityCommand<TEntity, TEditDto, TKey> : IRequest<Result<bool>>, IInvalidateCache
{
    public IEnumerable<string> CacheKeys { get; } = Array.Empty<string>();
    public IEnumerable<string>? CacheTags => new[] { typeof(TEntity).Name };
}
```

### Tag-based Cache Service
```csharp
public class MemoryCacheService : ICacheService
{
    // Mapování tag -> klíče
    private readonly ConcurrentDictionary<string, ConcurrentBag<string>> _tagToKeys = new();
    
    public async Task SetAsync<T>(string key, T value, TimeSpan expiration, 
        IEnumerable<string>? tags, CancellationToken cancellationToken = default)
    {
        // Uložení do cache + registrace tagů
    }
    
    public async Task RemoveByTagAsync(string tag, CancellationToken cancellationToken = default)
    {
        // Odstranění všech klíčů s daným tagem
    }
}
```

## Příklady Použití

### Scénář 1: Vytvoření Entity
```csharp
// 1. Cache obsahuje data
var cachedSamples = await mediator.Send(new GetAllEntitiesQuery<SampleDto> { UseCache = true });

// 2. Vytvoření nové entity
await mediator.Send(new CreateEntityCommand<SampleEntity, SampleDto, int>
{
    Payload = new SampleDto { Name = "Nový vzorek" }
});

// 3. Cache je automaticky invalidována - další dotaz načte aktuální data
var freshSamples = await mediator.Send(new GetAllEntitiesQuery<SampleDto> { UseCache = true });
```

### Scénář 2: Aktualizace Entity
```csharp
// 1. Cache obsahuje entitu
var cachedEntity = await mediator.Send(new GetEntityByIdQuery<SampleDto> 
{ 
    Id = 1, 
    UseCache = true 
});

// 2. Aktualizace entity
await mediator.Send(new UpdateEntityCommand<SampleEntity, SampleDto, int>
{
    Id = 1,
    Payload = new SampleDto { Name = "Aktualizovaný název" }
});

// 3. Cache je invalidována - další dotaz načte aktualizovaná data
var freshEntity = await mediator.Send(new GetEntityByIdQuery<SampleDto> 
{ 
    Id = 1, 
    UseCache = true 
});
```

## Cache Klíče a Tagy

### Generované Cache Klíče
- **GetEntityByIdQuery**: `"GetById_{TDto}_{Id}"`
- **GetAllEntitiesQuery**: `"GetAll_{TDto}"`
- **GetPagedEntitiesQuery**: `"GetPaged_{TDto}_Page{PageNumber}_Size{PageSize}[_Sort{SortBy}[Desc]]"`

### Cache Tagy
- Všechny dotazy používají název entity jako tag (např. "SampleEntity")
- Příkazy invalidují podle stejného tagu
- Umožňuje invalidaci všech souvisejících cache záznamů najednou

## Výhody Tag-based Invalidation

1. **Automatická konzistence** - cache se invaliduje automaticky při změnách
2. **Efektivní invalidation** - jeden tag invaliduje všechny související záznamy
3. **Flexibilita** - podporuje složité scénáře s více tagy
4. **Výkon** - rychlá invalidation bez nutnosti procházet všechny klíče
5. **Jednoduchost** - vývojář nemusí řešit invalidation manuálně

## Testování

Systém je pokryt kompletními testy:
- **CacheInvalidationTests** - unit testy pro invalidation behavior
- **TagBasedCacheTests** - testy pro tag-based cache service
- **CacheInvalidationIntegrationTests** - integrační testy end-to-end
- **ParametricCacheTests** - testy pro parametrickou cache

Celkem 30 testů ověřuje správnou funkcionalnost cache invalidation systému.

## Konfigurace

Cache invalidation je automaticky nakonfigurována v `DependencyInjection.cs`:

```csharp
services.AddTransient(typeof(IPipelineBehavior<,>), typeof(CacheBehavior<,>));
services.AddTransient(typeof(IPipelineBehavior<,>), typeof(CacheInvalidationBehavior<,>));
```

Pořadí behaviors je důležité - `CacheInvalidationBehavior` musí být registrován po `CacheBehavior`.

## Doporučení

1. **Vždy používejte příkazy** pro změny dat - pouze tak se cache správně invaliduje
2. **Testujte cache invalidation** - ověřte, že se cache skutečně invaliduje
3. **Monitorujte výkon** - tag-based invalidation je efektivní, ale sledujte paměťové nároky
4. **Používejte konzistentní tagy** - dodržujte konvenci názvů entit pro tagy
