# Deployment Guide - DataCapture API

Tento dokument popisuje, jak správně deployovat aplikaci DataCapture API včetně databázového souboru.

## Zahrnutí databáze do deploye

Databázový soubor `./Data/datacapture.db` je automaticky zahrnut do deploye pomocí následující konfigurace v `API.csproj`:

```xml
<ItemGroup>
  <Content Include="..\Data\datacapture.db">
    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    <Link>Data\datacapture.db</Link>
  </Content>
</ItemGroup>
```

## Konfigurace connection stringů

### Development prostředí
V `appsettings.Development.json` je použita relativní cesta k původnímu umístění databáze:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=../Data/datacapture.db"
  }
}
```

### Production prostředí
V `appsettings.json` je použita cesta k databázi v deployovaném adresáři:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=Data/datacapture.db"
  }
}
```

## Typy deploymentu

### 1. Folder Deployment (Základní)

```bash
# Publikování do složky
dotnet publish API -c Release

# Výstup bude v: API/bin/Release/net9.0/publish/
# Databáze bude v: API/bin/Release/net9.0/publish/Data/datacapture.db
```

### 2. Self-Contained Deployment

```bash
# Pro Windows
dotnet publish API -c Release -r win-x64 --self-contained

# Pro Linux
dotnet publish API -c Release -r linux-x64 --self-contained

# Pro macOS
dotnet publish API -c Release -r osx-x64 --self-contained
```

### 3. Single File Deployment

```bash
dotnet publish API -c Release -r win-x64 --self-contained -p:PublishSingleFile=true
```

**Poznámka:** Při Single File deploymentu se databáze extrahuje do dočasného adresáře při spuštění.

## Docker Deployment

Pro Docker deployment vytvořte `Dockerfile` v root adresáři:

```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY ["API/API.csproj", "API/"]
COPY ["Application/Application.csproj", "Application/"]
COPY ["Infrastructure/Infrastructure.csproj", "Infrastructure/"]
COPY ["Domain/Domain.csproj", "Domain/"]
COPY ["SharedKernel/SharedKernel.csproj", "SharedKernel/"]
RUN dotnet restore "API/API.csproj"
COPY . .
WORKDIR "/src/API"
RUN dotnet build "API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "API.dll"]
```

## Ověření deploymentu

Po deploymentu ověřte:

1. **Existence databáze:**
   ```bash
   ls -la Data/datacapture.db
   ```

2. **Spuštění aplikace:**
   ```bash
   dotnet API.dll
   ```

3. **Testování API:**
   - Otevřete prohlížeč na `https://localhost:7003` (nebo konfigurovaný port)
   - Měli byste být přesměrováni na Scalar API dokumentaci

## Troubleshooting

### Databáze se nenašla
- Ověřte, že je databáze v adresáři `Data/` relativně k spustitelné aplikaci
- Zkontrolujte connection string v `appsettings.json`

### Chyby při spuštění
- Ověřte, že jsou všechny závislosti (.NET Runtime) nainstalovány
- Zkontrolujte oprávnění k databázovému souboru

### Performance
- Pro produkční prostředí zvažte použití externí databáze (SQL Server, PostgreSQL)
- SQLite je vhodná pro development a menší aplikace

## Poznámky

- Databázový soubor se kopíruje pouze při změně (`PreserveNewest`)
- Pro produkční prostředí zvažte backup strategie pro databázi
- Connection string lze přepsat pomocí environment variables nebo Azure App Configuration
