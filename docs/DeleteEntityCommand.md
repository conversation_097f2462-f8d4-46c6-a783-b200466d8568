# DeleteEntityCommand

Generický příkaz pro smazání entity podle ID v aplikaci DataCapture.

## P<PERSON>ehled

`DeleteEntityCommand<TEntity, TKey>` je generický CQRS příkaz, který umožňuje smazání libovolné entity z databáze na základě jejího primárního klíče. Příkaz implementuje pattern Command a je zpracováván pomocí `DeleteEntityCommandHandler<TEntity, TKey>`.

## Struktura

### Command

```csharp
public class DeleteEntityCommand<TEntity, TKey> : IRequest<Result<bool>>, IInvalidateCache
{
    public TKey Id { get; init; } = default!;
    public IEnumerable<string>? CacheTags => new[] { typeof(TEntity).Name };
    public IEnumerable<string> CacheKeys { get; } = Array.Empty<string>();
}
```

### Handler

```csharp
public class DeleteEntityCommandHandler<TEntity, TKey>
    : IRequestHandler<DeleteEntityCommand<TEntity, TKey>, Result<bool>>
    where TEntity : BaseEntity<TKey>
{
    // Implementace...
}
```

## Použití

### Registrace v DI kontejneru

```csharp
// V Application/DependencyInjection.cs
services.AddScoped<IRequestHandler<DeleteEntityCommand<SampleEntity, int>, Result<bool>>, 
                   DeleteEntityCommandHandler<SampleEntity, int>>();
```

### Použití v API endpointu

```csharp
group.MapDelete("/{id:int}", async (int id, [FromServices] IMediator mediator) =>
{
    var result = await mediator.Send(new DeleteEntityCommand<SampleEntity, int> { Id = id });
    if (!result.Succeeded)
        return Results.BadRequest(result.Errors);
    return result.Data ? Results.NoContent() : Results.NotFound();
})
.WithName("DeleteEntity")
.Produces(StatusCodes.Status204NoContent)
.Produces(StatusCodes.Status400BadRequest)
.Produces(StatusCodes.Status404NotFound);
```

### Použití v aplikační logice

```csharp
// Přímé použití přes mediator
var command = new DeleteEntityCommand<SampleEntity, int> { Id = entityId };
var result = await mediator.Send(command);

if (result.Succeeded && result.Data)
{
    // Entita byla úspěšně smazána
}
else if (result.Succeeded && !result.Data)
{
    // Entita nebyla nalezena
}
else
{
    // Došlo k chybě
    var errors = result.Errors;
}
```

## Návratové hodnoty

Příkaz vrací `Result<bool>` s následujícími možnostmi:

- **Úspěch (true)**: Entita byla nalezena a úspěšně smazána
- **Úspěch (false)**: Operace proběhla bez chyby, ale žádné záznamy nebyly smazány
- **Chyba**: Entita nebyla nalezena nebo došlo k výjimce během mazání

## Cache invalidation

Příkaz automaticky invaliduje cache pro daný typ entity pomocí:

- **CacheTags**: `[typeof(TEntity).Name]` - invaliduje všechny cache záznamy pro daný typ entity
- **CacheKeys**: Prázdné pole - žádné specifické klíče

## Chybové stavy

1. **NotFound**: Entita s daným ID nebyla nalezena
2. **Database Error**: Výjimka během komunikace s databází
3. **Validation Error**: Neplatné ID nebo jiné validační chyby

## Testování

Příklad unit testu:

```csharp
[Fact]
public async Task Handle_ExistingEntity_ShouldDeleteSuccessfully()
{
    // Arrange
    var mockContext = new Mock<IApplicationDbContext>();
    var mockDbSet = new Mock<DbSet<SampleEntity>>();
    
    var existingEntity = new SampleEntity { Id = 1, Name = "Test Entity" };
    
    mockDbSet.Setup(x => x.FindAsync(It.IsAny<object[]>(), It.IsAny<CancellationToken>()))
           .ReturnsAsync(existingEntity);
    
    mockContext.Setup(x => x.Set<SampleEntity>()).Returns(mockDbSet.Object);
    mockContext.Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()))
              .ReturnsAsync(1);
    
    var handler = new DeleteEntityCommandHandler<SampleEntity, int>(mockContext.Object);
    var command = new DeleteEntityCommand<SampleEntity, int> { Id = 1 };
    
    // Act
    var result = await handler.Handle(command, CancellationToken.None);
    
    // Assert
    Assert.True(result.Succeeded);
    Assert.True(result.Data);
}
```

## Bezpečnost

- Příkaz vyžaduje autorizaci (endpoint má `RequireAuthorization()`)
- Doporučuje se implementovat dodatečné kontroly oprávnění podle business logiky
- Zvážit implementaci soft delete místo fyzického mazání pro auditní účely

## Související komponenty

- `CreateEntityCommand<TEntity, TEditDto, TKey>` - pro vytváření entit
- `UpdateEntityCommand<TEntity, TEditDto, TKey>` - pro aktualizaci entit
- `GetEntityByIdQuery<TDto>` - pro získání entity podle ID
- `GetAllEntitiesQuery<TDto>` - pro získání všech entit
