using SharedKernel.Domain;
using Xunit;

namespace SharedKernel.Tests.Domain;

/// <summary>
/// Testy pro typované metody doménových událostí v BaseEntity<T>.
/// </summary>
public class BaseEntityTypedEventsTests
{
    // Testovací entita
    private class TestEntity : BaseEntity<int>
    {
        public string Name { get; set; } = string.Empty;
    }

    // Testovací doménové události
    private class TestCreatedEvent : DomainEvent
    {
        public string Message { get; }
        
        public TestCreatedEvent(string message)
        {
            Message = message;
        }
    }

    private class TestUpdatedEvent : DomainEvent
    {
        public string UpdateInfo { get; }
        
        public TestUpdatedEvent(string updateInfo)
        {
            UpdateInfo = updateInfo;
        }
    }

    [Fact]
    public void AddDomainEvent_Generic_ShouldAddEventToCollection()
    {
        // Arrange
        var entity = new TestEntity { Id = 1, Name = "Test" };
        var domainEvent = new TestCreatedEvent("Entity created");

        // Act
        entity.AddDomainEvent(domainEvent);

        // Assert
        Assert.Single(entity.DomainEvents);
        Assert.Contains(domainEvent, entity.DomainEvents);
    }

    [Fact]
    public void AddDomainEvent_Generic_WithNull_ShouldThrowArgumentNullException()
    {
        // Arrange
        var entity = new TestEntity { Id = 1, Name = "Test" };

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => entity.AddDomainEvent<TestCreatedEvent>(null!));
    }

    [Fact]
    public void GetDomainEvents_Generic_ShouldReturnEventsOfSpecificType()
    {
        // Arrange
        var entity = new TestEntity { Id = 1, Name = "Test" };
        var createdEvent = new TestCreatedEvent("Entity created");
        var updatedEvent = new TestUpdatedEvent("Entity updated");

        entity.AddDomainEvent(createdEvent);
        entity.AddDomainEvent(updatedEvent);

        // Act
        var createdEvents = entity.GetDomainEvents<TestCreatedEvent>().ToList();
        var updatedEvents = entity.GetDomainEvents<TestUpdatedEvent>().ToList();

        // Assert
        Assert.Single(createdEvents);
        Assert.Single(updatedEvents);
        Assert.Equal(createdEvent, createdEvents.First());
        Assert.Equal(updatedEvent, updatedEvents.First());
    }

    [Fact]
    public void HasDomainEvent_Generic_ShouldReturnTrueWhenEventExists()
    {
        // Arrange
        var entity = new TestEntity { Id = 1, Name = "Test" };
        var createdEvent = new TestCreatedEvent("Entity created");

        entity.AddDomainEvent(createdEvent);

        // Act & Assert
        Assert.True(entity.HasDomainEvent<TestCreatedEvent>());
        Assert.False(entity.HasDomainEvent<TestUpdatedEvent>());
    }

    [Fact]
    public void ClearDomainEvents_Generic_ShouldRemoveOnlySpecificEventType()
    {
        // Arrange
        var entity = new TestEntity { Id = 1, Name = "Test" };
        var createdEvent = new TestCreatedEvent("Entity created");
        var updatedEvent = new TestUpdatedEvent("Entity updated");

        entity.AddDomainEvent(createdEvent);
        entity.AddDomainEvent(updatedEvent);

        // Act
        entity.ClearDomainEvents<TestCreatedEvent>();

        // Assert
        Assert.Single(entity.DomainEvents);
        Assert.False(entity.HasDomainEvent<TestCreatedEvent>());
        Assert.True(entity.HasDomainEvent<TestUpdatedEvent>());
    }

    [Fact]
    public void UnpublishedEventsCount_ShouldReturnCorrectCount()
    {
        // Arrange
        var entity = new TestEntity { Id = 1, Name = "Test" };
        var event1 = new TestCreatedEvent("Event 1");
        var event2 = new TestUpdatedEvent("Event 2");
        var event3 = new TestCreatedEvent("Event 3");

        entity.AddDomainEvent(event1);
        entity.AddDomainEvent(event2);
        entity.AddDomainEvent(event3);

        // Act & Assert
        Assert.Equal(3, entity.UnpublishedEventsCount);

        // Označíme jednu událost jako publikovanou
        event1.MarkAsPublished();
        Assert.Equal(2, entity.UnpublishedEventsCount);
    }

    [Fact]
    public void AddDomainEvent_NonGeneric_WithNull_ShouldThrowArgumentNullException()
    {
        // Arrange
        var entity = new TestEntity { Id = 1, Name = "Test" };

        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => entity.AddDomainEvent(null!));
    }

    [Fact]
    public void RemoveDomainEvent_WithNull_ShouldNotThrow()
    {
        // Arrange
        var entity = new TestEntity { Id = 1, Name = "Test" };

        // Act & Assert - nemělo by hodit výjimku
        entity.RemoveDomainEvent(null!);
        Assert.Empty(entity.DomainEvents);
    }

    [Fact]
    public void ClearDomainEvents_Generic_WithNoEventsOfType_ShouldNotThrow()
    {
        // Arrange
        var entity = new TestEntity { Id = 1, Name = "Test" };
        var updatedEvent = new TestUpdatedEvent("Entity updated");
        entity.AddDomainEvent(updatedEvent);

        // Act & Assert - nemělo by hodit výjimku
        entity.ClearDomainEvents<TestCreatedEvent>();
        Assert.Single(entity.DomainEvents);
        Assert.True(entity.HasDomainEvent<TestUpdatedEvent>());
    }
}
