using SharedKernel.Infrastructure.Mapping;
using Xunit;

namespace SharedKernel.Tests.Infrastructure.Mapping;

/// <summary>
/// Testy pro null handling v UnifiedMapper.
/// </summary>
public class UnifiedMapperNullHandlingTests
{
    /// <summary>
    /// Testovací třída s nullable vlastnostmi.
    /// </summary>
    public class SourceWithNulls
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public int? OptionalNumber { get; set; }
        public DateTime? OptionalDate { get; set; }
    }

    /// <summary>
    /// Cílová třída s nullable vlastnostmi.
    /// </summary>
    public class TargetWithNulls
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public int? OptionalNumber { get; set; }
        public DateTime? OptionalDate { get; set; }
    }

    [Fact]
    public void Map_WithNullValues_ShouldNotAssignNullProperties()
    {
        // Arrange
        var mapper = new UnifiedMapper<SourceWithNulls, TargetWithNulls>();
        var source = new SourceWithNulls
        {
            Id = 1,
            Name = "Test Name",
            Description = null, // null hodnota
            OptionalNumber = null, // null hodnota
            OptionalDate = DateTime.Now
        };

        // Act
        var result = mapper.Map(source);

        // Assert
        Assert.Equal(1, result.Id);
        Assert.Equal("Test Name", result.Name);
        Assert.Null(result.Description); // null hodnota se nemapuje, zůstává null
        Assert.Null(result.OptionalNumber); // null hodnota se nemapuje, zůstává null
        Assert.Equal(source.OptionalDate, result.OptionalDate);
    }

    [Fact]
    public void Map_WithAllNullValues_ShouldCreateValidObject()
    {
        // Arrange
        var mapper = new UnifiedMapper<SourceWithNulls, TargetWithNulls>();
        var source = new SourceWithNulls
        {
            Id = 0,
            Name = null,
            Description = null,
            OptionalNumber = null,
            OptionalDate = null
        };

        // Act
        var result = mapper.Map(source);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(0, result.Id); // value type se mapuje vždy
        Assert.Null(result.Name);
        Assert.Null(result.Description);
        Assert.Null(result.OptionalNumber);
        Assert.Null(result.OptionalDate);
    }

    [Fact]
    public void Update_WithNullValues_ShouldNotOverwriteExistingValues()
    {
        // Arrange
        var mapper = new UnifiedMapper<SourceWithNulls, TargetWithNulls>();
        var source = new SourceWithNulls
        {
            Id = 2,
            Name = "Updated Name",
            Description = null, // null hodnota - neměla by přepsat existující
            OptionalNumber = 42,
            OptionalDate = null // null hodnota - neměla by přepsat existující
        };

        var target = new TargetWithNulls
        {
            Id = 1,
            Name = "Original Name",
            Description = "Original Description",
            OptionalNumber = 100,
            OptionalDate = DateTime.Today
        };

        // Act
        mapper.Update(source, target);

        // Assert
        Assert.Equal(2, target.Id); // value type se aktualizuje vždy
        Assert.Equal("Updated Name", target.Name);
        Assert.Equal("Original Description", target.Description); // null hodnota nepřepsala existující
        Assert.Equal(42, target.OptionalNumber);
        Assert.Equal(DateTime.Today, target.OptionalDate); // null hodnota nepřepsala existující
    }

    [Fact]
    public void Map_WithValueTypes_ShouldAlwaysAssign()
    {
        // Arrange
        var mapper = new UnifiedMapper<SourceWithNulls, TargetWithNulls>();
        var source = new SourceWithNulls
        {
            Id = 0, // value type - měl by se vždy mapovat i když je 0
            Name = "Test",
            Description = "Test Description",
            OptionalNumber = 0, // nullable value type s hodnotou 0
            OptionalDate = DateTime.MinValue // nullable value type s minimální hodnotou
        };

        // Act
        var result = mapper.Map(source);

        // Assert
        Assert.Equal(0, result.Id); // value type se mapuje vždy
        Assert.Equal("Test", result.Name);
        Assert.Equal("Test Description", result.Description);
        Assert.Equal(0, result.OptionalNumber); // nullable s hodnotou 0 se mapuje
        Assert.Equal(DateTime.MinValue, result.OptionalDate); // nullable s minimální hodnotou se mapuje
    }
}
