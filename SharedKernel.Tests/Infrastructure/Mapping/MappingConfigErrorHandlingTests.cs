using SharedKernel.Infrastructure.Mapping;
using Xunit;

namespace SharedKernel.Tests.Infrastructure.Mapping;

/// <summary>
/// Testy pro error handling v MappingConfig.
/// </summary>
public class MappingConfigErrorHandlingTests
{
    /// <summary>
    /// Testovací třída se základními vlastnostmi.
    /// </summary>
    public class SourceClass
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public int? OptionalNumber { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// Cílová třída s různými typy vlastností.
    /// </summary>
    public class TargetClass
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public int OptionalNumber { get; set; } // Non-nullable int
        public DateTime CreatedAt { get; set; }
        public string? FormattedDate { get; set; }
    }

    [Fact]
    public void Map_WithNullToNonNullableType_ShouldThrowInformativeException()
    {
        // Arrange
        var mapper = new UnifiedMapper<SourceClass, TargetClass>(
            forwardConfig: config =>
            {
                config.Map(s => s.OptionalNumber, t => t.OptionalNumber); // null -> non-nullable int
            });

        var source = new SourceClass
        {
            Id = 1,
            Name = "Test",
            OptionalNumber = null // null hodnota
        };

        // Act & Assert
        var exception = Assert.Throws<InvalidOperationException>(() => mapper.Map(source, useConfig: true));
        
        Assert.Contains("Cannot convert null value from OptionalNumber to non-nullable type Int32", exception.Message);
        Assert.Contains("Mapping failed from OptionalNumber to OptionalNumber", exception.Message);
    }

    [Fact]
    public void Map_WithValidConversion_ShouldSucceed()
    {
        // Arrange
        var mapper = new UnifiedMapper<SourceClass, TargetClass>(
            forwardConfig: config =>
            {
                config.Map(s => s.Id, t => t.Id);
                config.Map(s => s.Name, t => t.Name);
                config.Map(s => s.CreatedAt, t => t.FormattedDate, date => date.ToString("yyyy-MM-dd"));
            });

        var source = new SourceClass
        {
            Id = 1,
            Name = "Test",
            CreatedAt = new DateTime(2023, 12, 25)
        };

        // Act
        var result = mapper.Map(source, useConfig: true);

        // Assert
        Assert.Equal(1, result.Id);
        Assert.Equal("Test", result.Name);
        Assert.Equal("2023-12-25", result.FormattedDate);
    }

    [Fact]
    public void Map_WithCustomConverterException_ShouldWrapException()
    {
        // Arrange
        var mapper = new UnifiedMapper<SourceClass, TargetClass>(
            forwardConfig: config =>
            {
                config.Map(s => s.Name, t => t.FormattedDate, name =>
                {
                    if (name == "ERROR")
                        throw new ArgumentException("Custom converter error");
                    return name ?? "default";
                });
            });

        var source = new SourceClass
        {
            Id = 1,
            Name = "ERROR"
        };

        // Act & Assert
        var exception = Assert.Throws<InvalidOperationException>(() => mapper.Map(source, useConfig: true));

        Assert.Contains("Mapping failed from Name to FormattedDate", exception.Message);
        Assert.Contains("Custom converter error", exception.Message);

        // Vnořená exception může být buď ArgumentException nebo InvalidOperationException (závisí na wrappingu)
        Assert.True(exception.InnerException is ArgumentException ||
                   exception.InnerException is InvalidOperationException);
    }

    [Fact]
    public void Map_WithTypeConversionError_ShouldProvideInformativeMessage()
    {
        // Arrange
        var mapper = new UnifiedMapper<SourceClass, TargetClass>(
            forwardConfig: config =>
            {
                config.Map(s => s.Name, t => t.OptionalNumber); // string -> int conversion
            });

        var source = new SourceClass
        {
            Id = 1,
            Name = "NotANumber" // Cannot convert to int
        };

        // Act & Assert
        var exception = Assert.Throws<InvalidOperationException>(() => mapper.Map(source, useConfig: true));
        
        Assert.Contains("Mapping failed from Name to OptionalNumber", exception.Message);
    }

    [Fact]
    public void Map_WithNullToNullableType_ShouldSucceed()
    {
        // Arrange
        var mapper = new UnifiedMapper<SourceClass, TargetClass>(
            forwardConfig: config =>
            {
                config.Map(s => s.Name, t => t.Description); // string? -> string?
            });

        var source = new SourceClass
        {
            Id = 1,
            Name = null
        };

        // Act
        var result = mapper.Map(source, useConfig: true);

        // Assert
        Assert.Null(result.Description);
    }

    [Fact]
    public void Compile_WithInvalidTargetType_ShouldThrowInformativeException()
    {
        // Arrange - vytvoříme třídu bez bezparametrického konstruktoru
        var mapper = new UnifiedMapper<SourceClass, ClassWithoutParameterlessConstructor>(
            forwardConfig: config =>
            {
                config.Map(s => s.Id, t => t.Id);
            });

        var source = new SourceClass { Id = 1 };

        // Act & Assert
        var exception = Assert.Throws<InvalidOperationException>(() => mapper.Map(source, useConfig: true));
        
        Assert.Contains("Failed to create and map instance of ClassWithoutParameterlessConstructor", exception.Message);
    }

    /// <summary>
    /// Testovací třída bez bezparametrického konstruktoru.
    /// </summary>
    public class ClassWithoutParameterlessConstructor
    {
        public int Id { get; set; }
        
        public ClassWithoutParameterlessConstructor(int id)
        {
            Id = id;
        }
    }

    [Fact]
    public void Map_WithAssignableTypes_ShouldUseDirectAssignment()
    {
        // Arrange
        var mapper = new UnifiedMapper<SourceClass, TargetClass>(
            forwardConfig: config =>
            {
                config.Map(s => s.Name, t => t.Description); // string? -> string? (assignable)
            });

        var source = new SourceClass
        {
            Id = 1,
            Name = "Test Value"
        };

        // Act
        var result = mapper.Map(source, useConfig: true);

        // Assert
        Assert.Equal("Test Value", result.Description);
    }
}
