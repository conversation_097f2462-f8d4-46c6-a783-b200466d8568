using SharedKernel.Models;
using Xunit;

namespace SharedKernel.Tests.Models;

/// <summary>
/// Unit testy pro fluent API metody Result<T>.
/// </summary>
public class ResultFluentApiTests
{
    #region Map Tests

    [Fact]
    public void Map_WithSuccessfulResult_ShouldMapValue()
    {
        // Arrange
        var result = Result<int>.Ok(42);

        // Act
        var mappedResult = result.Map(x => x.ToString());

        // Assert
        Assert.True(mappedResult.Succeeded);
        Assert.Equal("42", mappedResult.Data);
    }

    [Fact]
    public void Map_WithFailedResult_ShouldReturnError()
    {
        // Arrange
        var result = Result<int>.Error("Original error");

        // Act
        var mappedResult = result.Map(x => x.ToString());

        // Assert
        Assert.False(mappedResult.Succeeded);
        Assert.Equal(new[] { "Original error" }, mappedResult.Errors);
    }

    [Fact]
    public void Map_WithNullData_ShouldMapToDefault()
    {
        // Arrange
        var result = Result<string>.Ok(null);

        // Act
        var mappedResult = result.Map(x => x?.Length ?? 0);

        // Assert
        Assert.True(mappedResult.Succeeded);
        Assert.Equal(0, mappedResult.Data);
    }

    [Fact]
    public void Map_WithExceptionInMapper_ShouldReturnError()
    {
        // Arrange
        var result = Result<string>.Ok("test");

        // Act
        var mappedResult = result.Map<int>(x => throw new InvalidOperationException("Mapping failed"));

        // Assert
        Assert.False(mappedResult.Succeeded);
        Assert.Contains("Mapping failed", mappedResult.Errors[0]);
    }

    #endregion

    #region Bind Tests

    [Fact]
    public void Bind_WithSuccessfulResult_ShouldBindValue()
    {
        // Arrange
        var result = Result<int>.Ok(42);

        // Act
        var boundResult = result.Bind(x => Result<string>.Ok(x.ToString()));

        // Assert
        Assert.True(boundResult.Succeeded);
        Assert.Equal("42", boundResult.Data);
    }

    [Fact]
    public void Bind_WithFailedResult_ShouldReturnOriginalError()
    {
        // Arrange
        var result = Result<int>.Error("Original error");

        // Act
        var boundResult = result.Bind(x => Result<string>.Ok(x.ToString()));

        // Assert
        Assert.False(boundResult.Succeeded);
        Assert.Equal(new[] { "Original error" }, boundResult.Errors);
    }

    [Fact]
    public void Bind_WithNullData_ShouldReturnError()
    {
        // Arrange
        var result = Result<string>.Ok(null);

        // Act
        var boundResult = result.Bind(x => Result<int>.Ok(x.Length));

        // Assert
        Assert.False(boundResult.Succeeded);
        Assert.Equal(new[] { "Data is null" }, boundResult.Errors);
    }

    [Fact]
    public void Bind_WithExceptionInBinder_ShouldReturnError()
    {
        // Arrange
        var result = Result<string>.Ok("test");

        // Act
        var boundResult = result.Bind<int>(x => throw new InvalidOperationException("Binding failed"));

        // Assert
        Assert.False(boundResult.Succeeded);
        Assert.Contains("Binding failed", boundResult.Errors[0]);
    }

    #endregion

    #region OnSuccess Tests

    [Fact]
    public void OnSuccess_WithSuccessfulResult_ShouldExecuteAction()
    {
        // Arrange
        var result = Result<string>.Ok("test");
        var actionExecuted = false;
        string? capturedValue = null;

        // Act
        var returnedResult = result.OnSuccess(value =>
        {
            actionExecuted = true;
            capturedValue = value;
        });

        // Assert
        Assert.True(actionExecuted);
        Assert.Equal("test", capturedValue);
        Assert.Same(result, returnedResult); // Should return same instance for chaining
    }

    [Fact]
    public void OnSuccess_WithFailedResult_ShouldNotExecuteAction()
    {
        // Arrange
        var result = Result<string>.Error("Error");
        var actionExecuted = false;

        // Act
        var returnedResult = result.OnSuccess(value => actionExecuted = true);

        // Assert
        Assert.False(actionExecuted);
        Assert.Same(result, returnedResult);
    }

    [Fact]
    public void OnSuccess_WithNullData_ShouldNotExecuteAction()
    {
        // Arrange
        var result = Result<string>.Ok(null);
        var actionExecuted = false;

        // Act
        var returnedResult = result.OnSuccess(value => actionExecuted = true);

        // Assert
        Assert.False(actionExecuted);
        Assert.Same(result, returnedResult);
    }

    [Fact]
    public void OnSuccess_WithExceptionInAction_ShouldReturnError()
    {
        // Arrange
        var result = Result<string>.Ok("test");

        // Act
        var returnedResult = result.OnSuccess(value => throw new InvalidOperationException("Action failed"));

        // Assert
        Assert.False(returnedResult.Succeeded);
        Assert.Contains("OnSuccess action failed", returnedResult.Errors[0]);
    }

    #endregion

    #region OnError Tests

    [Fact]
    public void OnError_WithFailedResult_ShouldExecuteAction()
    {
        // Arrange
        var result = Result<string>.Error("Error 1", "Error 2");
        var actionExecuted = false;
        string[]? capturedErrors = null;

        // Act
        var returnedResult = result.OnError(errors =>
        {
            actionExecuted = true;
            capturedErrors = errors;
        });

        // Assert
        Assert.True(actionExecuted);
        Assert.Equal(new[] { "Error 1", "Error 2" }, capturedErrors);
        Assert.Same(result, returnedResult);
    }

    [Fact]
    public void OnError_WithSuccessfulResult_ShouldNotExecuteAction()
    {
        // Arrange
        var result = Result<string>.Ok("Success");
        var actionExecuted = false;

        // Act
        var returnedResult = result.OnError(errors => actionExecuted = true);

        // Assert
        Assert.False(actionExecuted);
        Assert.Same(result, returnedResult);
    }

    [Fact]
    public void OnError_WithExceptionInAction_ShouldIgnoreException()
    {
        // Arrange
        var result = Result<string>.Error("Original error");

        // Act & Assert - Should not throw
        var returnedResult = result.OnError(errors => throw new InvalidOperationException("Action failed"));

        Assert.Same(result, returnedResult);
        Assert.False(returnedResult.Succeeded);
        Assert.Equal(new[] { "Original error" }, returnedResult.Errors);
    }

    #endregion

    #region Create Tests

    [Fact]
    public void Create_WithValidData_ShouldReturnSuccess()
    {
        // Arrange & Act
        var result = Result<string>.Create("test", x => x?.Length > 0, "String must not be empty");

        // Assert
        Assert.True(result.Succeeded);
        Assert.Equal("test", result.Data);
    }

    [Fact]
    public void Create_WithInvalidData_ShouldReturnError()
    {
        // Arrange & Act
        var result = Result<string>.Create("", x => x?.Length > 0, "String must not be empty");

        // Assert
        Assert.False(result.Succeeded);
        Assert.Equal(new[] { "String must not be empty" }, result.Errors);
    }

    [Fact]
    public void Create_WithNullData_ShouldReturnError()
    {
        // Arrange & Act
        var result = Result<string>.Create(null, x => x?.Length > 0, "String must not be empty");

        // Assert
        Assert.False(result.Succeeded);
        Assert.Equal(new[] { "Data cannot be null" }, result.Errors);
    }

    [Fact]
    public void Create_WithMultipleValidations_AllValid_ShouldReturnSuccess()
    {
        // Arrange & Act
        var result = Result<string>.Create("test",
            (x => x?.Length > 0, "String must not be empty"),
            (x => x?.Length < 10, "String must be shorter than 10 characters")
        );

        // Assert
        Assert.True(result.Succeeded);
        Assert.Equal("test", result.Data);
    }

    [Fact]
    public void Create_WithMultipleValidations_SomeInvalid_ShouldReturnAllErrors()
    {
        // Arrange & Act
        var result = Result<string>.Create("",
            (x => x?.Length > 0, "String must not be empty"),
            (x => x?.Length < 10, "String must be shorter than 10 characters")
        );

        // Assert
        Assert.False(result.Succeeded);
        Assert.Equal(new[] { "String must not be empty" }, result.Errors);
    }

    #endregion

    #region Convenience Methods Tests

    [Fact]
    public void ImplicitConversion_ShouldCreateSuccessfulResult()
    {
        // Arrange & Act
        Result<string> result = "test value";

        // Assert
        Assert.True(result.Succeeded);
        Assert.Equal("test value", result.Data);
    }

    [Fact]
    public void GetValueOrThrow_WithSuccessfulResult_ShouldReturnValue()
    {
        // Arrange
        var result = Result<string>.Ok("test");

        // Act
        var value = result.GetValueOrThrow();

        // Assert
        Assert.Equal("test", value);
    }

    [Fact]
    public void GetValueOrThrow_WithFailedResult_ShouldThrowException()
    {
        // Arrange
        var result = Result<string>.Error("Error message");

        // Act & Assert
        var exception = Assert.Throws<InvalidOperationException>(() => result.GetValueOrThrow());
        Assert.Contains("Error message", exception.Message);
    }

    [Fact]
    public void GetValueOrDefault_WithSuccessfulResult_ShouldReturnValue()
    {
        // Arrange
        var result = Result<string>.Ok("test");

        // Act
        var value = result.GetValueOrDefault("default");

        // Assert
        Assert.Equal("test", value);
    }

    [Fact]
    public void GetValueOrDefault_WithFailedResult_ShouldReturnDefault()
    {
        // Arrange
        var result = Result<string>.Error("Error");

        // Act
        var value = result.GetValueOrDefault("default");

        // Assert
        Assert.Equal("default", value);
    }

    #endregion

    #region Chaining Tests

    [Fact]
    public void FluentChaining_ShouldWorkCorrectly()
    {
        // Arrange
        var result = Result<int>.Ok(42);
        var actionExecuted = false;

        // Act
        var finalResult = result
            .Map(x => x * 2)
            .OnSuccess(x => actionExecuted = true)
            .Map(x => x.ToString());

        // Assert
        Assert.True(finalResult.Succeeded);
        Assert.Equal("84", finalResult.Data);
        Assert.True(actionExecuted);
    }

    [Fact]
    public void FluentChaining_WithError_ShouldStopAtFirstError()
    {
        // Arrange
        var result = Result<int>.Error("Initial error");
        var actionExecuted = false;

        // Act
        var finalResult = result
            .Map(x => x * 2)
            .OnSuccess(x => actionExecuted = true)
            .Map(x => x.ToString());

        // Assert
        Assert.False(finalResult.Succeeded);
        Assert.Equal(new[] { "Initial error" }, finalResult.Errors);
        Assert.False(actionExecuted);
    }

    #endregion

    #region Deconstruction Tests

    [Fact]
    public void Deconstruct_WithSuccessfulResult_ShouldDeconstructCorrectly()
    {
        // Arrange
        var result = Result<string>.Ok("test data");

        // Act
        var (succeeded, data, errors) = result;

        // Assert
        Assert.True(succeeded);
        Assert.Equal("test data", data);
        Assert.Empty(errors);
    }

    [Fact]
    public void Deconstruct_WithFailedResult_ShouldDeconstructCorrectly()
    {
        // Arrange
        var result = Result<string>.Error("Error 1", "Error 2");

        // Act
        var (succeeded, data, errors) = result;

        // Assert
        Assert.False(succeeded);
        Assert.Null(data);
        Assert.Equal(new[] { "Error 1", "Error 2" }, errors);
    }

    [Fact]
    public void Deconstruct_WithNullData_ShouldDeconstructCorrectly()
    {
        // Arrange
        var result = Result<string>.Ok(null);

        // Act
        var (succeeded, data, errors) = result;

        // Assert
        Assert.True(succeeded);
        Assert.Null(data);
        Assert.Empty(errors);
    }

    [Fact]
    public void Deconstruct_InSwitchExpression_ShouldWorkCorrectly()
    {
        // Arrange
        var successResult = Result<int>.Ok(42);
        var errorResult = Result<int>.Error("Something went wrong");

        // Act
        var (successSucceeded, successData, successErrors) = successResult;
        var (errorSucceeded, errorData, errorErrors) = errorResult;

        var successMessage = successSucceeded ? $"Success: {successData}" : $"Error: {string.Join(", ", successErrors)}";
        var errorMessage = errorSucceeded ? $"Success: {errorData}" : $"Error: {string.Join(", ", errorErrors)}";

        // Assert
        Assert.Equal("Success: 42", successMessage);
        Assert.Equal("Error: Something went wrong", errorMessage);
    }

    [Fact]
    public void Deconstruct_WithIgnoredComponents_ShouldWorkCorrectly()
    {
        // Arrange
        var result = Result<string>.Ok("test");

        // Act
        var (succeeded, _, _) = result; // Ignorujeme data a errors
        var (_, data, _) = result;      // Ignorujeme succeeded a errors
        var (_, _, errors) = result;    // Ignorujeme succeeded a data

        // Assert
        Assert.True(succeeded);
        Assert.Equal("test", data);
        Assert.Empty(errors);
    }

    #endregion
}
