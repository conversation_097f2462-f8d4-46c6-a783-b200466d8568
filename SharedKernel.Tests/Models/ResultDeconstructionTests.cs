using SharedKernel.Models;
using Xunit;

namespace SharedKernel.Tests.Models;

/// <summary>
/// Unit testy pro dekonstrukci Result tříd.
/// </summary>
public class ResultDeconstructionTests
{
    // Konkrétní implementace základní Result třídy pro testování
    private record TestResult : Result
    {
        public TestResult(bool succeeded, string[] errors) : base(succeeded, errors)
        {
        }

        public static TestResult Success() => new(true, Array.Empty<string>());
        public static TestResult Failure(params string[] errors) => new(false, errors);
    }

    #region Base Result Deconstruction Tests

    [Fact]
    public void BaseResult_Deconstruct_WithSuccessfulResult_ShouldDeconstructCorrectly()
    {
        // Arrange
        var result = TestResult.Success();

        // Act
        var (succeeded, errors) = result;

        // Assert
        Assert.True(succeeded);
        Assert.Empty(errors);
    }

    [Fact]
    public void BaseResult_Deconstruct_WithFailedResult_ShouldDeconstructCorrectly()
    {
        // Arrange
        var result = TestResult.Failure("Error 1", "Error 2");

        // Act
        var (succeeded, errors) = result;

        // Assert
        Assert.False(succeeded);
        Assert.Equal(new[] { "Error 1", "Error 2" }, errors);
    }

    [Fact]
    public void BaseResult_Deconstruct_InSwitchExpression_ShouldWorkCorrectly()
    {
        // Arrange
        var successResult = TestResult.Success();
        var errorResult = TestResult.Failure("Something went wrong");

        // Act
        var (successSucceeded, successErrors) = successResult;
        var (errorSucceeded, errorErrors) = errorResult;

        var successMessage = successSucceeded ? "Success" : $"Error: {string.Join(", ", successErrors)}";
        var errorMessage = errorSucceeded ? "Success" : $"Error: {string.Join(", ", errorErrors)}";

        // Assert
        Assert.Equal("Success", successMessage);
        Assert.Equal("Error: Something went wrong", errorMessage);
    }

    [Fact]
    public void BaseResult_Deconstruct_WithIgnoredComponents_ShouldWorkCorrectly()
    {
        // Arrange
        var result = TestResult.Success();

        // Act
        var (succeeded, _) = result; // Ignorujeme errors
        var (_, errors) = result;    // Ignorujeme succeeded

        // Assert
        Assert.True(succeeded);
        Assert.Empty(errors);
    }

    #endregion

    #region Generic Result Deconstruction Tests

    [Fact]
    public void GenericResult_Deconstruct_WithSuccessfulResult_ShouldDeconstructCorrectly()
    {
        // Arrange
        var result = Result<string>.Ok("test data");

        // Act
        var (succeeded, data, errors) = result;

        // Assert
        Assert.True(succeeded);
        Assert.Equal("test data", data);
        Assert.Empty(errors);
    }

    [Fact]
    public void GenericResult_Deconstruct_WithFailedResult_ShouldDeconstructCorrectly()
    {
        // Arrange
        var result = Result<string>.Error("Error 1", "Error 2");

        // Act
        var (succeeded, data, errors) = result;

        // Assert
        Assert.False(succeeded);
        Assert.Null(data);
        Assert.Equal(new[] { "Error 1", "Error 2" }, errors);
    }

    [Fact]
    public void GenericResult_Deconstruct_WithNullData_ShouldDeconstructCorrectly()
    {
        // Arrange
        var result = Result<string>.Ok(null);

        // Act
        var (succeeded, data, errors) = result;

        // Assert
        Assert.True(succeeded);
        Assert.Null(data);
        Assert.Empty(errors);
    }

    [Fact]
    public void GenericResult_Deconstruct_InSwitchExpression_ShouldWorkCorrectly()
    {
        // Arrange
        var successResult = Result<int>.Ok(42);
        var errorResult = Result<int>.Error("Something went wrong");

        // Act
        var (successSucceeded, successData, successErrors) = successResult;
        var (errorSucceeded, errorData, errorErrors) = errorResult;

        var successMessage = successSucceeded ? $"Success: {successData}" : $"Error: {string.Join(", ", successErrors)}";
        var errorMessage = errorSucceeded ? $"Success: {errorData}" : $"Error: {string.Join(", ", errorErrors)}";

        // Assert
        Assert.Equal("Success: 42", successMessage);
        Assert.Equal("Error: Something went wrong", errorMessage);
    }

    [Fact]
    public void GenericResult_Deconstruct_WithIgnoredComponents_ShouldWorkCorrectly()
    {
        // Arrange
        var result = Result<string>.Ok("test");

        // Act
        var (succeeded, _, _) = result; // Ignorujeme data a errors
        var (_, data, _) = result;      // Ignorujeme succeeded a errors
        var (_, _, errors) = result;    // Ignorujeme succeeded a data

        // Assert
        Assert.True(succeeded);
        Assert.Equal("test", data);
        Assert.Empty(errors);
    }

    #endregion

    #region Pattern Matching Examples

    [Fact]
    public void Deconstruct_InPatternMatching_ShouldEnableElegantHandling()
    {
        // Arrange
        var results = new[]
        {
            Result<int>.Ok(42),
            Result<int>.Error("Invalid input"),
            Result<int>.Ok(0),
            Result<int>.Error("Network error", "Timeout")
        };

        // Act & Assert
        foreach (var result in results)
        {
            var (succeeded, data, errors) = result;

            var message = (succeeded, data, errors) switch
            {
                (true, > 0, _) => $"Positive result: {data}",
                (true, 0, _) => "Zero result",
                (true, _, _) => $"Negative result: {data}",
                (false, _, var errs) when errs.Length == 1 => $"Single error: {errs[0]}",
                (false, _, var errs) => $"Multiple errors: {string.Join(", ", errs)}"
            };

            Assert.NotNull(message);
            Assert.NotEmpty(message);
        }
    }

    [Fact]
    public void Deconstruct_WithComplexTypes_ShouldWorkCorrectly()
    {
        // Arrange
        var user = new { Id = 1, Name = "John Doe", Email = "<EMAIL>" };
        var result = Result<object>.Ok(user);

        // Act
        var (succeeded, data, errors) = result;

        // Assert
        Assert.True(succeeded);
        Assert.Equal(user, data);
        Assert.Empty(errors);
    }

    #endregion
}
