using SharedKernel.Domain;
using Xunit;

namespace SharedKernel.Tests;

/// <summary>
/// Testy pro ověření správné práce s DateTimeOffset v SharedKernel.
/// </summary>
public class DateTimeOffsetTests
{
    /// <summary>
    /// Test ověřuje, že DomainEvent používá DateTimeOffset místo DateTime.
    /// </summary>
    [Fact]
    public void DomainEvent_Should_Use_DateTimeOffset()
    {
        // Arrange & Act
        var domainEvent = new TestDomainEvent();
        
        // Assert
        Assert.IsType<DateTimeOffset>(domainEvent.DateOccurred);
        Assert.True(domainEvent.DateOccurred <= DateTimeOffset.UtcNow);
        Assert.True(domainEvent.DateOccurred > DateTimeOffset.UtcNow.AddSeconds(-1));
    }

    /// <summary>
    /// Test ověřuje, že ITrackableEntity používá DateTimeOffset pro CreatedAt a ModifiedAt.
    /// </summary>
    [Fact]
    public void TrackableEntity_Should_Use_DateTimeOffset()
    {
        // Arrange & Act
        var entity = new TestTrackableEntity { Id = 1 };
        var now = DateTimeOffset.UtcNow;

        entity.CreatedAt = now;
        entity.ModifiedAt = now.AddMinutes(5);

        // Assert
        Assert.True(entity.CreatedAt.HasValue);
        Assert.True(entity.ModifiedAt.HasValue);
        Assert.IsType<DateTimeOffset>(entity.CreatedAt.Value);
        Assert.IsType<DateTimeOffset>(entity.ModifiedAt.Value);
        Assert.Equal(now, entity.CreatedAt);
        Assert.Equal(now.AddMinutes(5), entity.ModifiedAt);
    }

    /// <summary>
    /// Test ověřuje, že ISoftDelete používá DateTimeOffset pro DeletedAt.
    /// </summary>
    [Fact]
    public void SoftDeleteEntity_Should_Use_DateTimeOffset()
    {
        // Arrange & Act
        var entity = new TestSoftDeleteEntity { Id = 1 };
        var now = DateTimeOffset.UtcNow;

        entity.DeletedAt = now;

        // Assert
        Assert.True(entity.DeletedAt.HasValue);
        Assert.IsType<DateTimeOffset>(entity.DeletedAt.Value);
        Assert.Equal(now, entity.DeletedAt);
    }

    /// <summary>
    /// Test ověřuje, že kombinovaná entita používá DateTimeOffset pro všechny časové vlastnosti.
    /// </summary>
    [Fact]
    public void TrackableSoftDeleteEntity_Should_Use_DateTimeOffset()
    {
        // Arrange & Act
        var entity = new TestTrackableSoftDeleteEntity { Id = 1 };
        var now = DateTimeOffset.UtcNow;

        entity.CreatedAt = now;
        entity.ModifiedAt = now.AddMinutes(5);
        entity.DeletedAt = now.AddMinutes(10);

        // Assert
        Assert.True(entity.CreatedAt.HasValue);
        Assert.True(entity.ModifiedAt.HasValue);
        Assert.True(entity.DeletedAt.HasValue);
        Assert.IsType<DateTimeOffset>(entity.CreatedAt.Value);
        Assert.IsType<DateTimeOffset>(entity.ModifiedAt.Value);
        Assert.IsType<DateTimeOffset>(entity.DeletedAt.Value);
        Assert.Equal(now, entity.CreatedAt);
        Assert.Equal(now.AddMinutes(5), entity.ModifiedAt);
        Assert.Equal(now.AddMinutes(10), entity.DeletedAt);
    }

    /// <summary>
    /// Test ověřuje, že DateTimeOffset správně zachovává informace o časovém pásmu.
    /// </summary>
    [Fact]
    public void DateTimeOffset_Should_Preserve_Timezone_Information()
    {
        // Arrange
        var utcTime = DateTimeOffset.UtcNow;
        var localTime = DateTimeOffset.Now;
        var specificTime = new DateTimeOffset(2025, 7, 31, 12, 0, 0, TimeSpan.FromHours(2));
        
        // Act & Assert
        Assert.Equal(TimeSpan.Zero, utcTime.Offset);
        Assert.NotEqual(TimeSpan.Zero, localTime.Offset); // Může být různé podle lokálního nastavení
        Assert.Equal(TimeSpan.FromHours(2), specificTime.Offset);
        
        // Ověření, že všechny časy lze převést na UTC
        Assert.True(utcTime.UtcDateTime != default);
        Assert.True(localTime.UtcDateTime != default);
        Assert.True(specificTime.UtcDateTime != default);
    }
}

/// <summary>
/// Testovací implementace DomainEvent pro účely testování.
/// </summary>
public class TestDomainEvent : DomainEvent
{
}

/// <summary>
/// Testovací implementace ITrackableEntity pro účely testování.
/// </summary>
public class TestTrackableEntity : BaseTrackableEntity<int>
{
}

/// <summary>
/// Testovací implementace ISoftDelete pro účely testování.
/// </summary>
public class TestSoftDeleteEntity : BaseSoftDeleteEntity<int>
{
}

/// <summary>
/// Testovací implementace kombinované entity pro účely testování.
/// </summary>
public class TestTrackableSoftDeleteEntity : BaseTrackableSoftDeleteEntity<int>
{
}
